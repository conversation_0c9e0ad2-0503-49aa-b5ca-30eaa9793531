<template>
  <div class="workspace-files">
    <!-- 页面头部 -->
    <div class="files-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/workspace' }">工作空间</el-breadcrumb-item>
          <el-breadcrumb-item>{{ workspaceName }}</el-breadcrumb-item>
          <el-breadcrumb-item v-for="folder in breadcrumbs" :key="folder.id">
            {{ folder.name }}
          </el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-button-group>
          <el-button icon="el-icon-view" :class="{ 'is-active': viewMode === 'grid' }" @click="viewMode = 'grid'">
            网格
          </el-button>
          <el-button icon="el-icon-menu" :class="{ 'is-active': viewMode === 'list' }" @click="viewMode = 'list'">
            列表
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 工具栏 -->
    <div class="files-toolbar">
      <div class="toolbar-left">
        <el-upload ref="upload" :action="uploadUrl" :headers="uploadHeaders" :data="uploadData"
          :on-success="handleUploadSuccess" :on-error="handleUploadError" :before-upload="handleBeforeUpload"
          :show-file-list="false" multiple>
          <el-button type="primary" icon="el-icon-upload">
            上传文件
          </el-button>
        </el-upload>
        <el-button type="success" icon="el-icon-folder-add" @click="handleCreateFolder">
          新建文件夹
        </el-button>
        <el-button type="info" icon="el-icon-edit-outline" @click="handleCreateNote">
          新建笔记
        </el-button>
        <el-button type="danger" icon="el-icon-delete" :disabled="selectedFiles.length === 0"
          @click="handleBatchDelete">
          批量删除
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-button icon="el-icon-cpu" @click="$router.push('/ai/chat')" style="margin-right: 8px">
          AI问答
        </el-button>
        <el-input v-model="searchKeyword" placeholder="搜索文件..." prefix-icon="el-icon-search" style="width: 200px"
          @input="handleSearch" clearable />
      </div>
    </div>

    <!-- 文件列表 -->
    <div class="files-content" v-loading="loading">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="files-grid">
        <div v-for="item in filteredFiles" :key="item.id" class="file-item"
          :class="{ 'is-selected': selectedFiles.includes(item.id) }" @click="handleItemClick(item)"
          @contextmenu.prevent="handleContextMenu(item, $event)">
          <div class="file-checkbox">
            <el-checkbox :value="selectedFiles.includes(item.id)" @change="handleSelectFile(item.id, $event)"
              @click.stop />
          </div>
          <div class="file-icon">
            <i :class="getFileIcon(item)"></i>
          </div>
          <div class="file-name" :title="item.name">{{ item.name }}</div>
          <div class="file-info">
            <span class="file-size">{{ formatFileSize(item.size) }}</span>
            <span class="file-time">{{ formatTime(item.updateTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <el-table v-else :data="filteredFiles" @selection-change="handleSelectionChange" @row-click="handleRowClick"
        @row-contextmenu="handleRowContextMenu">
        <el-table-column type="selection" width="55" />
        <el-table-column label="名称" min-width="200">
          <template slot-scope="scope">
            <div class="file-name-cell">
              <i :class="getFileIcon(scope.row)"></i>
              <span>{{ scope.row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="大小" width="100">
          <template slot-scope="scope">
            {{ formatFileSize(scope.row.size) }}
          </template>
        </el-table-column>
        <el-table-column label="修改时间" width="150">
          <template slot-scope="scope">
            {{ formatTime(scope.row.updateTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click.stop="previewFile(scope.row)">
              预览
            </el-button>
            <el-button type="text" size="small" @click.stop="handleDownload(scope.row)">
              下载
            </el-button>
            <el-button type="text" size="small" @click.stop="handleShare(scope.row)">
              分享
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="!loading && filteredFiles.length === 0" class="empty-state">
        <i class="el-icon-folder-opened"></i>
        <h3>文件夹为空</h3>
        <p>上传您的第一个文件开始使用吧</p>
      </div>
    </div>

    <!-- 右键菜单 -->
    <div v-show="contextMenuVisible" class="context-menu"
      :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }">
      <div class="menu-item" @click="previewFile(contextMenuItem)">
        <i class="el-icon-view"></i> 预览
      </div>
      <div class="menu-item" @click="handleDownload(contextMenuItem)">
        <i class="el-icon-download"></i> 下载
      </div>
      <div class="menu-item" @click="handleRename(contextMenuItem)">
        <i class="el-icon-edit"></i> 重命名
      </div>
      <div class="menu-item" @click="handleShare(contextMenuItem)">
        <i class="el-icon-share"></i> 分享
      </div>
      <div class="menu-item danger" @click="handleDelete(contextMenuItem)">
        <i class="el-icon-delete"></i> 删除
      </div>
    </div>

    <!-- 新建文件夹对话框 -->
    <el-dialog title="新建文件夹" :visible.sync="folderDialogVisible" width="400px">
      <el-form ref="folderForm" :model="folderForm" :rules="folderRules">
        <el-form-item label="文件夹名称" prop="name">
          <el-input v-model="folderForm.name" placeholder="请输入文件夹名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="folderDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitCreateFolder">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 重命名对话框 -->
    <el-dialog title="重命名" :visible.sync="renameDialogVisible" width="400px">
      <el-form ref="renameForm" :model="renameForm" :rules="renameRules">
        <el-form-item label="新名称" prop="name">
          <el-input v-model="renameForm.name" placeholder="请输入新名称" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="renameDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitRename">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 分享对话框 -->
    <el-dialog title="分享文件" :visible.sync="shareDialogVisible" width="500px">
      <div class="share-content">
        <div class="share-link">
          <el-input v-model="shareLink" readonly>
            <el-button slot="append" @click="copyShareLink">复制链接</el-button>
          </el-input>
        </div>
        <div class="share-options">
          <el-form label-width="80px">
            <el-form-item label="有效期">
              <el-select v-model="shareExpire" @change="updateShareLink">
                <el-option label="1天" value="1" />
                <el-option label="7天" value="7" />
                <el-option label="30天" value="30" />
                <el-option label="永久" value="0" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  listWorkspaceFiles,
  uploadWorkspaceFile,
  deleteWorkspaceFile,
  renameWorkspaceFile,
  renameWorkspaceFolder,
  downloadWorkspaceFile,
  createWorkspaceFolder,
  shareWorkspaceFile,
  batchDeleteWorkspaceFiles
} from '@/api/workspace/file'
import { getWorkspace } from '@/api/workspace/workspace'
import { getToken } from '@/utils/auth'

export default {
  name: 'WorkspaceFiles',
  data () {
    return {
      loading: false,
      workspaceId: null,
      workspaceName: '',
      currentFolderId: null,
      breadcrumbs: [],
      viewMode: 'grid',
      searchKeyword: '',
      fileList: [],
      selectedFiles: [],
      contextMenuVisible: false,
      contextMenuX: 0,
      contextMenuY: 0,
      contextMenuItem: null,

      // 上传相关
      uploadUrl: process.env.VUE_APP_BASE_API + '/workspace/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + getToken()
      },

      // 对话框
      folderDialogVisible: false,
      renameDialogVisible: false,
      shareDialogVisible: false,

      // 表单
      folderForm: { name: '' },
      renameForm: { name: '' },
      renameTarget: null,
      shareLink: '',
      shareExpire: '7',
      shareTarget: null,

      // 验证规则
      folderRules: {
        name: [
          { required: true, message: '请输入文件夹名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      },
      renameRules: {
        name: [
          { required: true, message: '请输入新名称', trigger: 'blur' },
          { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    uploadData () {
      return {
        workspaceId: this.workspaceId,
        folderId: this.currentFolderId
      }
    },
    filteredFiles () {
      if (!this.searchKeyword) return this.fileList
      return this.fileList.filter(file =>
        file.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    }
  },
  created () {
    this.workspaceId = this.$route.params.id
    this.loadWorkspaceInfo()
    this.loadFiles()
    this.bindGlobalEvents()
  },
  beforeDestroy () {
    this.unbindGlobalEvents()
  },
  methods: {
    // 加载工作空间信息
    async loadWorkspaceInfo () {
      try {
        // 模拟API调用 - 实际项目中替换为真实API
        await new Promise(resolve => setTimeout(resolve, 300))

        const mockWorkspaces = {
          1: '我的个人空间',
          2: '研发团队协作空间',
          3: '公共资源库',
          4: '项目Alpha'
        }

        this.workspaceName = mockWorkspaces[this.workspaceId] || '工作空间'
      } catch (error) {
        this.$message.error('加载工作空间信息失败')
      }
    },

    // 加载文件列表
    async loadFiles () {
      this.loading = true
      try {
        // 模拟API调用 - 实际项目中替换为真实API
        await new Promise(resolve => setTimeout(resolve, 600))

        const mockFiles = [
          {
            id: 1,
            name: '项目文档',
            type: 'folder',
            size: 0,
            updateTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 2,
            name: '需求分析报告.docx',
            type: 'file',
            size: 2048576,
            updateTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 3,
            name: '系统架构图.png',
            type: 'file',
            size: 1024000,
            updateTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 4,
            name: '数据库设计.xlsx',
            type: 'file',
            size: 512000,
            updateTime: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 5,
            name: '会议记录',
            type: 'folder',
            size: 0,
            updateTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 6,
            name: '用户手册.pdf',
            type: 'file',
            size: 3072000,
            updateTime: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 7,
            name: '项目笔记.md',
            type: 'note',
            size: 15360,
            updateTime: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 8,
            name: '会议纪要.md',
            type: 'note',
            size: 8192,
            updateTime: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 9,
            name: 'config.json',
            type: 'file',
            size: 1024,
            updateTime: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 10,
            name: 'demo.mp4',
            type: 'file',
            size: 10485760,
            updateTime: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 11,
            name: 'background.mp3',
            type: 'file',
            size: 5242880,
            updateTime: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 12,
            name: 'logo.png',
            type: 'file',
            size: 204800,
            updateTime: new Date(Date.now() - 16 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 13,
            name: 'script.js',
            type: 'file',
            size: 4096,
            updateTime: new Date(Date.now() - 20 * 60 * 60 * 1000).toISOString()
          },
          {
            id: 14,
            name: 'readme.txt',
            type: 'file',
            size: 2048,
            updateTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()
          }
        ]

        this.fileList = mockFiles
      } catch (error) {
        this.$message.error('加载文件列表失败')
      } finally {
        this.loading = false
      }
    },

    // 处理文件上传前
    handleBeforeUpload (file) {
      const isLt50M = file.size / 1024 / 1024 < 50
      if (!isLt50M) {
        this.$message.error('上传文件大小不能超过 50MB!')
        return false
      }
      return true
    },

    // 处理上传成功
    handleUploadSuccess (response, file) {
      if (response.code === 200) {
        this.$message.success('上传成功')
        this.loadFiles()
      } else {
        this.$message.error(response.msg || '上传失败')
      }
    },

    // 处理上传失败
    handleUploadError (error) {
      this.$message.error('上传失败')
    },

    // 处理项目点击
    handleItemClick (item) {
      if (item.type === 'folder') {
        this.enterFolder(item)
      } else if (item.type === 'note') {
        this.openNote(item)
      } else {
        this.previewFile(item)
      }
    },

    // 进入文件夹
    enterFolder (folder) {
      this.currentFolderId = folder.id
      this.breadcrumbs.push({ id: folder.id, name: folder.name })
      this.loadFiles()
    },

    // 打开笔记
    openNote (note) {
      this.$router.push(`/workspace/${this.workspaceId}/note/${note.id}`)
    },

    // 预览文件
    previewFile (file) {
      // 所有文件都支持预览
      this.$router.push(`/workspace/${this.workspaceId}/preview/${file.id}`)
    },

    // 处理文件选择
    handleSelectFile (fileId, checked) {
      if (checked) {
        if (!this.selectedFiles.includes(fileId)) {
          this.selectedFiles.push(fileId)
        }
      } else {
        const index = this.selectedFiles.indexOf(fileId)
        if (index > -1) {
          this.selectedFiles.splice(index, 1)
        }
      }
    },

    // 处理表格选择变化
    handleSelectionChange (selection) {
      this.selectedFiles = selection.map(item => item.id)
    },

    // 处理行点击
    handleRowClick (row) {
      this.handleItemClick(row)
    },

    // 处理右键菜单
    handleContextMenu (item, event) {
      this.contextMenuItem = item
      this.contextMenuX = event.clientX
      this.contextMenuY = event.clientY
      this.contextMenuVisible = true
    },

    handleRowContextMenu (row, column, event) {
      this.handleContextMenu(row, event)
    },

    // 创建文件夹
    handleCreateFolder () {
      this.folderForm.name = ''
      this.folderDialogVisible = true
    },

    // 创建笔记
    handleCreateNote () {
      this.$router.push(`/workspace/${this.workspaceId}/note/new`)
    },

    async submitCreateFolder () {
      try {
        await this.$refs.folderForm.validate()
        await createWorkspaceFolder(this.workspaceId, {
          name: this.folderForm.name,
          parentId: this.currentFolderId
        })
        this.$message.success('创建成功')
        this.folderDialogVisible = false
        this.loadFiles()
      } catch (error) {
        if (error !== false) {
          this.$message.error('创建失败')
        }
      }
    },

    // 重命名
    handleRename (item) {
      this.renameTarget = item
      this.renameForm.name = item.name
      this.renameDialogVisible = true
      this.contextMenuVisible = false
    },

    async submitRename () {
      try {
        await this.$refs.renameForm.validate()
        if (this.renameTarget.type === 'folder') {
          await renameWorkspaceFolder(this.workspaceId, this.renameTarget.id, this.renameForm.name)
        } else {
          await renameWorkspaceFile(this.workspaceId, this.renameTarget.id, this.renameForm.name)
        }
        this.$message.success('重命名成功')
        this.renameDialogVisible = false
        this.loadFiles()
      } catch (error) {
        if (error !== false) {
          this.$message.error('重命名失败')
        }
      }
    },

    // 下载文件
    async handleDownload (item) {
      try {
        const response = await downloadWorkspaceFile(this.workspaceId, item.id)
        const url = window.URL.createObjectURL(new Blob([response]))
        const link = document.createElement('a')
        link.href = url
        link.setAttribute('download', item.name)
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      } catch (error) {
        this.$message.error('下载失败')
      }
      this.contextMenuVisible = false
    },

    // 分享文件
    async handleShare (item) {
      try {
        this.shareTarget = item
        const response = await shareWorkspaceFile(this.workspaceId, item.id, this.shareExpire)
        this.shareLink = response.data.shareUrl
        this.shareDialogVisible = true
      } catch (error) {
        this.$message.error('生成分享链接失败')
      }
      this.contextMenuVisible = false
    },

    // 更新分享链接
    async updateShareLink () {
      if (this.shareTarget) {
        try {
          const response = await shareWorkspaceFile(this.workspaceId, this.shareTarget.id, this.shareExpire)
          this.shareLink = response.data.shareUrl
        } catch (error) {
          this.$message.error('更新分享链接失败')
        }
      }
    },

    // 复制分享链接
    copyShareLink () {
      navigator.clipboard.writeText(this.shareLink).then(() => {
        this.$message.success('链接已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    // 删除文件
    async handleDelete (item) {
      try {
        await this.$confirm('确定要删除该文件吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await deleteWorkspaceFile(this.workspaceId, item.id)
        this.$message.success('删除成功')
        this.loadFiles()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
      this.contextMenuVisible = false
    },

    // 批量删除
    async handleBatchDelete () {
      if (this.selectedFiles.length === 0) return

      try {
        await this.$confirm(`确定要删除选中的 ${this.selectedFiles.length} 个文件吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await batchDeleteWorkspaceFiles(this.workspaceId, this.selectedFiles)
        this.$message.success('删除成功')
        this.selectedFiles = []
        this.loadFiles()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    // 搜索文件
    handleSearch () {
      // 搜索逻辑在 computed 中处理
    },

    // 获取文件图标
    getFileIcon (item) {
      if (item.type === 'folder') {
        return 'el-icon-folder'
      }

      if (item.type === 'note') {
        return 'el-icon-edit-outline'
      }

      const ext = item.name.split('.').pop().toLowerCase()
      const iconMap = {
        pdf: 'el-icon-document',
        doc: 'el-icon-document',
        docx: 'el-icon-document',
        xls: 'el-icon-s-grid',
        xlsx: 'el-icon-s-grid',
        ppt: 'el-icon-present',
        pptx: 'el-icon-present',
        txt: 'el-icon-document',
        md: 'el-icon-edit-outline',
        jpg: 'el-icon-picture',
        jpeg: 'el-icon-picture',
        png: 'el-icon-picture',
        gif: 'el-icon-picture',
        mp4: 'el-icon-video-camera',
        avi: 'el-icon-video-camera',
        mp3: 'el-icon-headset',
        wav: 'el-icon-headset',
        zip: 'el-icon-folder-opened',
        rar: 'el-icon-folder-opened'
      }

      return iconMap[ext] || 'el-icon-document'
    },

    // 格式化文件大小
    formatFileSize (size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return size.toFixed(2) + ' ' + units[index]
    },

    // 格式化时间
    formatTime (time) {
      if (!time) return ''
      return new Date(time).toLocaleString()
    },

    // 绑定全局事件
    bindGlobalEvents () {
      document.addEventListener('click', this.handleGlobalClick)
    },

    // 解绑全局事件
    unbindGlobalEvents () {
      document.removeEventListener('click', this.handleGlobalClick)
    },

    // 处理全局点击
    handleGlobalClick () {
      this.contextMenuVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.workspace-files {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-left {
    ::v-deep .el-breadcrumb__inner {
      color: #606266;
      font-weight: normal;
    }

    ::v-deep .el-breadcrumb__inner.is-link {
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }
  }

  .header-right {
    .el-button-group .el-button.is-active {
      background: #409eff;
      color: white;
    }
  }
}

.files-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .toolbar-left {
    display: flex;
    align-items: center;
    gap: 12px;

    .el-button {
      white-space: nowrap;
    }
  }
}

.files-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-height: 400px;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  padding: 20px;

  .file-item {
    position: relative;
    padding: 16px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
    }

    &.is-selected {
      border-color: #409eff;
      background: #f0f9ff;
    }

    .file-checkbox {
      position: absolute;
      top: 8px;
      right: 8px;
    }

    .file-icon {
      text-align: center;
      margin-bottom: 12px;

      i {
        font-size: 48px;
        color: #409eff;
      }
    }

    .file-name {
      font-size: 14px;
      font-weight: 500;
      color: #303133;
      text-align: center;
      margin-bottom: 8px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .file-info {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #909399;

      .file-size,
      .file-time {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }
}

.file-name-cell {
  display: flex;
  align-items: center;

  i {
    margin-right: 8px;
    font-size: 16px;
    color: #409eff;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;

  i {
    font-size: 64px;
    color: #dcdfe6;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #606266;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

.context-menu {
  position: fixed;
  background: white;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  min-width: 120px;

  .menu-item {
    padding: 8px 16px;
    cursor: pointer;
    font-size: 14px;
    color: #606266;
    transition: all 0.3s ease;

    &:hover {
      background: #f5f7fa;
      color: #409eff;
    }

    &.danger {
      color: #f56c6c;

      &:hover {
        background: #fef0f0;
        color: #f56c6c;
      }
    }

    i {
      margin-right: 8px;
      width: 14px;
    }
  }
}

.share-content {
  .share-link {
    margin-bottom: 20px;
  }

  .share-options {
    .el-form-item {
      margin-bottom: 0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .workspace-files {
    padding: 16px;
  }

  .files-header,
  .files-toolbar {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
    padding: 16px;
  }
}
</style>
