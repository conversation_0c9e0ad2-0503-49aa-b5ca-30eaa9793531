# 文档管理 API 接口文档

## 📋 概述

本文档详细介绍工作空间文档管理功能的所有API接口，包括版本控制、协作、模板管理等。

## 🔗 基础信息

- **基础URL**: `/api/v1`
- **认证方式**: Bear<PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 📚 版本控制 API

### 获取文档版本列表

```http
GET /workspace/{workspaceId}/documents/{documentId}/versions
```

**参数说明**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| workspaceId | String | 是 | 工作空间ID |
| documentId | String | 是 | 文档ID |
| page | Number | 否 | 页码，默认1 |
| pageSize | Number | 否 | 每页数量，默认10 |
| sortBy | String | 否 | 排序字段：name/modified/size |
| order | String | 否 | 排序方向：asc/desc |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "v1_1640995200000",
        "number": 1,
        "title": "初始版本",
        "content": "文档内容",
        "author": {
          "id": "user_123",
          "name": "张三",
          "avatar": "avatar.jpg"
        },
        "message": "创建文档",
        "createdAt": "2021-12-31T16:00:00Z",
        "size": 1024,
        "hash": "abc123",
        "changes": []
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 10
  }
}
```

### 创建新版本

```http
POST /workspace/{workspaceId}/documents/{documentId}/versions
```

**请求体**

```json
{
  "title": "版本标题",
  "content": "文档内容",
  "message": "版本说明",
  "metadata": {
    "tags": ["重要", "草稿"]
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "版本创建成功",
  "data": {
    "id": "v2_1640995300000",
    "number": 2,
    "title": "版本标题",
    "content": "文档内容",
    "author": {
      "id": "user_123",
      "name": "张三"
    },
    "message": "版本说明",
    "createdAt": "2021-12-31T16:01:40Z",
    "parentId": "v1_1640995200000",
    "changes": [
      {
        "type": "add",
        "lineNumber": 5,
        "content": "新增内容"
      }
    ]
  }
}
```

### 版本对比

```http
GET /workspace/{workspaceId}/documents/{documentId}/versions/compare
```

**参数说明**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| version1 | String | 是 | 版本1 ID |
| version2 | String | 是 | 版本2 ID |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "version1": {
      "id": "v1_1640995200000",
      "title": "版本1"
    },
    "version2": {
      "id": "v2_1640995300000",
      "title": "版本2"
    },
    "changes": [
      {
        "type": "add",
        "lineNumber": 5,
        "content": "新增的内容",
        "oldLineNumber": null
      },
      {
        "type": "delete",
        "lineNumber": null,
        "content": "删除的内容",
        "oldLineNumber": 3
      },
      {
        "type": "modify",
        "lineNumber": 8,
        "content": "修改后的内容",
        "oldContent": "修改前的内容",
        "oldLineNumber": 7
      }
    ],
    "summary": {
      "totalChanges": 3,
      "additions": 1,
      "deletions": 1,
      "modifications": 1
    }
  }
}
```

### 回滚到指定版本

```http
POST /workspace/{workspaceId}/documents/{documentId}/versions/{versionId}/rollback
```

**请求体**

```json
{
  "message": "回滚原因"
}
```

## 🤝 协作 API

### 加入协作会话

```http
POST /workspace/{workspaceId}/documents/{documentId}/collaboration/join
```

**请求体**

```json
{
  "userInfo": {
    "id": "user_123",
    "nickname": "张三",
    "avatar": "avatar.jpg"
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "加入成功",
  "data": {
    "sessionId": "session_123",
    "collaborators": [
      {
        "id": "user_123",
        "nickname": "张三",
        "avatar": "avatar.jpg",
        "isActive": true,
        "lastActivity": "2021-12-31T16:00:00Z"
      }
    ]
  }
}
```

### 发送协作操作

```http
POST /workspace/{workspaceId}/documents/{documentId}/collaboration/operation
```

**请求体**

```json
{
  "type": "content-change",
  "content": "更新后的内容",
  "position": 100,
  "length": 10,
  "timestamp": 1640995200000
}
```

### 更新光标位置

```http
PUT /workspace/{workspaceId}/documents/{documentId}/collaboration/cursor
```

**请求体**

```json
{
  "position": 150,
  "selection": {
    "start": 150,
    "end": 200
  }
}
```

### 添加评论

```http
POST /workspace/{workspaceId}/documents/{documentId}/comments
```

**请求体**

```json
{
  "content": "这是一条评论 @张三",
  "position": 100,
  "mentions": ["张三"]
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "评论添加成功",
  "data": {
    "id": "comment_123",
    "content": "这是一条评论 @张三",
    "position": 100,
    "author": {
      "id": "user_123",
      "nickname": "李四",
      "avatar": "avatar.jpg"
    },
    "createdAt": "2021-12-31T16:00:00Z",
    "mentions": ["张三"],
    "replies": []
  }
}
```

### 回复评论

```http
POST /workspace/{workspaceId}/documents/{documentId}/comments/{commentId}/replies
```

**请求体**

```json
{
  "content": "回复内容 @原作者",
  "mentions": ["原作者"]
}
```

## 📄 模板 API

### 获取模板列表

```http
GET /workspace/{workspaceId}/templates
```

**参数说明**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| category | String | 否 | 模板分类 |
| tags | String | 否 | 标签，多个用逗号分隔 |
| search | String | 否 | 搜索关键词 |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": "template_123",
      "name": "会议记录",
      "description": "标准会议记录模板",
      "content": "# 会议记录\n\n**会议时间：** \n**参会人员：** ",
      "category": "会议",
      "tags": ["会议", "记录"],
      "author": {
        "id": "user_123",
        "name": "张三"
      },
      "createdAt": "2021-12-31T16:00:00Z",
      "usageCount": 100,
      "isFavorite": false
    }
  ]
}
```

### 创建模板

```http
POST /workspace/{workspaceId}/templates
```

**请求体**

```json
{
  "name": "模板名称",
  "description": "模板描述",
  "content": "模板内容",
  "category": "分类",
  "tags": ["标签1", "标签2"]
}
```

### 使用模板创建文档

```http
POST /workspace/{workspaceId}/templates/{templateId}/create-document
```

**请求体**

```json
{
  "title": "新文档标题",
  "folderId": "folder_123"
}
```

## 📁 文档管理 API

### 创建文档

```http
POST /workspace/{workspaceId}/documents
```

**请求体**

```json
{
  "title": "文档标题",
  "content": "文档内容",
  "folderId": "folder_123",
  "templateId": "template_123"
}
```

### 获取文档详情

```http
GET /workspace/{workspaceId}/documents/{documentId}
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "doc_123",
    "title": "文档标题",
    "content": "文档内容",
    "author": {
      "id": "user_123",
      "name": "张三"
    },
    "createdAt": "2021-12-31T16:00:00Z",
    "updatedAt": "2021-12-31T16:30:00Z",
    "size": 1024,
    "versionInfo": {
      "current": 3,
      "total": 5
    },
    "collaborationInfo": {
      "isActive": true,
      "collaboratorCount": 2
    },
    "permissions": {
      "canEdit": true,
      "canComment": true,
      "canShare": true
    }
  }
}
```

### 搜索文档

```http
GET /workspace/{workspaceId}/documents/search
```

**参数说明**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| keyword | String | 是 | 搜索关键词 |
| type | String | 否 | 文档类型 |
| author | String | 否 | 作者ID |
| dateRange | String | 否 | 时间范围：7d/30d/90d |

### 分享文档

```http
POST /workspace/{workspaceId}/documents/{documentId}/share
```

**请求体**

```json
{
  "permissions": ["read", "comment"],
  "expireAt": "2022-01-31T16:00:00Z",
  "password": "123456"
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "分享成功",
  "data": {
    "shareId": "share_123",
    "shareUrl": "https://example.com/share/share_123",
    "permissions": ["read", "comment"],
    "expireAt": "2022-01-31T16:00:00Z"
  }
}
```

## 🔒 权限管理

### 设置文档权限

```http
PUT /workspace/{workspaceId}/documents/{documentId}/permissions
```

**请求体**

```json
{
  "users": [
    {
      "userId": "user_123",
      "permissions": ["read", "write", "comment"]
    }
  ],
  "groups": [
    {
      "groupId": "group_123",
      "permissions": ["read", "comment"]
    }
  ],
  "public": {
    "enabled": true,
    "permissions": ["read"]
  }
}
```

## 📊 统计 API

### 获取文档统计

```http
GET /workspace/{workspaceId}/documents/{documentId}/stats
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "viewCount": 100,
    "editCount": 50,
    "commentCount": 20,
    "collaboratorCount": 5,
    "versionCount": 10,
    "lastActivity": "2021-12-31T16:00:00Z",
    "wordCount": 1500,
    "characterCount": 8000
  }
}
```

### 获取工作空间统计

```http
GET /workspace/{workspaceId}/stats
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "documentCount": 100,
    "templateCount": 20,
    "memberCount": 15,
    "storageUsed": 1073741824,
    "storageLimit": 10737418240,
    "activeCollaborations": 5,
    "recentActivity": [
      {
        "type": "document_created",
        "documentId": "doc_123",
        "userId": "user_123",
        "timestamp": "2021-12-31T16:00:00Z"
      }
    ]
  }
}
```

## 🚨 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 📝 请求示例

### JavaScript/Axios

```javascript
import axios from 'axios'

// 创建版本
const createVersion = async (workspaceId, documentId, versionData) => {
  try {
    const response = await axios.post(
      `/api/v1/workspace/${workspaceId}/documents/${documentId}/versions`,
      versionData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    )
    return response.data
  } catch (error) {
    console.error('创建版本失败:', error)
    throw error
  }
}

// 获取协作者
const getCollaborators = async (workspaceId, documentId) => {
  try {
    const response = await axios.get(
      `/api/v1/workspace/${workspaceId}/documents/${documentId}/collaboration/users`,
      {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      }
    )
    return response.data.data
  } catch (error) {
    console.error('获取协作者失败:', error)
    throw error
  }
}
```

### cURL

```bash
# 创建文档
curl -X POST \
  'https://api.example.com/workspace/123/documents' \
  -H 'Authorization: Bearer your_token' \
  -H 'Content-Type: application/json' \
  -d '{
    "title": "新文档",
    "content": "文档内容",
    "folderId": "folder_123"
  }'

# 获取版本列表
curl -X GET \
  'https://api.example.com/workspace/123/documents/456/versions?page=1&pageSize=10' \
  -H 'Authorization: Bearer your_token'
```

## 🔄 WebSocket 事件

### 连接协作会话

```javascript
const ws = new WebSocket('wss://api.example.com/ws/collaboration')

// 连接成功
ws.onopen = () => {
  ws.send(JSON.stringify({
    type: 'join',
    documentId: 'doc_123',
    userInfo: {
      id: 'user_123',
      nickname: '张三'
    }
  }))
}

// 接收消息
ws.onmessage = (event) => {
  const message = JSON.parse(event.data)
  
  switch (message.type) {
    case 'operation':
      // 处理协作操作
      handleOperation(message.data)
      break
    case 'cursor_update':
      // 处理光标更新
      handleCursorUpdate(message.data)
      break
    case 'comment_added':
      // 处理新评论
      handleCommentAdded(message.data)
      break
    case 'user_joined':
      // 用户加入
      handleUserJoined(message.data)
      break
    case 'user_left':
      // 用户离开
      handleUserLeft(message.data)
      break
  }
}
```

---

📧 如有问题，请联系开发团队或查看项目文档。
