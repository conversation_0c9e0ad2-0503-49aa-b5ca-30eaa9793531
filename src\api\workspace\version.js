import request from '@/utils/request'

// 获取文档版本列表
export function getDocumentVersions(workspaceId, documentId, query) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions`,
    method: 'get',
    params: query
  })
}

// 获取指定版本详情
export function getDocumentVersion(workspaceId, documentId, versionId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}`,
    method: 'get'
  })
}

// 创建新版本
export function createDocumentVersion(workspaceId, documentId, data) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions`,
    method: 'post',
    data: data
  })
}

// 回滚到指定版本
export function rollbackToVersion(workspaceId, documentId, versionId, data) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}/rollback`,
    method: 'post',
    data: data
  })
}

// 比较两个版本
export function compareVersions(workspaceId, documentId, versionId1, versionId2) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/compare`,
    method: 'get',
    params: {
      version1: versionId1,
      version2: versionId2
    }
  })
}

// 获取版本统计信息
export function getVersionStats(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/stats`,
    method: 'get'
  })
}

// 创建版本标签
export function createVersionTag(workspaceId, documentId, versionId, data) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}/tags`,
    method: 'post',
    data: data
  })
}

// 获取版本标签列表
export function getVersionTags(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/tags`,
    method: 'get'
  })
}

// 删除版本标签
export function deleteVersionTag(workspaceId, documentId, tagName) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/tags/${tagName}`,
    method: 'delete'
  })
}

// 创建分支
export function createBranch(workspaceId, documentId, data) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/branches`,
    method: 'post',
    data: data
  })
}

// 获取分支列表
export function getBranches(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/branches`,
    method: 'get'
  })
}

// 切换分支
export function switchBranch(workspaceId, documentId, branchName) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/branches/${branchName}/switch`,
    method: 'post'
  })
}

// 合并分支
export function mergeBranch(workspaceId, documentId, data) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/branches/merge`,
    method: 'post',
    data: data
  })
}

// 导出版本历史
export function exportVersionHistory(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/export`,
    method: 'get',
    responseType: 'blob'
  })
}

// 导入版本历史
export function importVersionHistory(workspaceId, documentId, data) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/import`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: data
  })
}

// 清理旧版本
export function cleanupVersions(workspaceId, documentId, keepCount) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/cleanup`,
    method: 'post',
    data: { keepCount }
  })
}

// 获取版本变更记录
export function getVersionChanges(workspaceId, documentId, versionId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}/changes`,
    method: 'get'
  })
}

// 恢复已删除的版本
export function restoreVersion(workspaceId, documentId, versionId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}/restore`,
    method: 'post'
  })
}

// 删除版本
export function deleteVersion(workspaceId, documentId, versionId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}`,
    method: 'delete'
  })
}

// 获取版本差异文件
export function getVersionDiff(workspaceId, documentId, versionId1, versionId2, format = 'json') {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/diff`,
    method: 'get',
    params: {
      version1: versionId1,
      version2: versionId2,
      format: format
    }
  })
}

// 批量操作版本
export function batchVersionOperation(workspaceId, documentId, operation, versionIds) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/batch`,
    method: 'post',
    data: {
      operation,
      versionIds
    }
  })
}

// 设置版本权限
export function setVersionPermissions(workspaceId, documentId, versionId, permissions) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}/permissions`,
    method: 'put',
    data: permissions
  })
}

// 获取版本权限
export function getVersionPermissions(workspaceId, documentId, versionId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}/permissions`,
    method: 'get'
  })
}

// 版本审核
export function reviewVersion(workspaceId, documentId, versionId, reviewData) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}/review`,
    method: 'post',
    data: reviewData
  })
}

// 获取版本审核记录
export function getVersionReviews(workspaceId, documentId, versionId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}/reviews`,
    method: 'get'
  })
}

// 版本发布
export function publishVersion(workspaceId, documentId, versionId, publishData) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}/publish`,
    method: 'post',
    data: publishData
  })
}

// 获取已发布版本列表
export function getPublishedVersions(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/published`,
    method: 'get'
  })
}

// 取消发布版本
export function unpublishVersion(workspaceId, documentId, versionId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/versions/${versionId}/unpublish`,
    method: 'post'
  })
}
