# AI智能问答 API 接口文档

## 📋 概述

AI智能问答模块提供基于人工智能的问答服务，支持文档问答、原辅料分析、智能搜索等功能。

## 🔗 基础信息

- **基础URL**: `/api/v1/ai`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **字符编码**: UTF-8

## 🤖 AI模型管理 API

### 1. 获取可用模型列表

```http
GET /ai/models
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "models": [
      {
        "id": "gpt-3.5-turbo",
        "name": "GPT-3.5 Turbo",
        "provider": "OpenAI",
        "type": "chat",
        "description": "快速响应的对话模型",
        "capabilities": ["chat", "document_qa", "search"],
        "maxTokens": 4096,
        "costPerToken": 0.002,
        "isAvailable": true,
        "isPremium": false,
        "responseTime": "fast",
        "accuracy": "high"
      },
      {
        "id": "gpt-4",
        "name": "GPT-4",
        "provider": "OpenAI",
        "type": "chat",
        "description": "最先进的对话模型",
        "capabilities": ["chat", "document_qa", "search", "analysis"],
        "maxTokens": 8192,
        "costPerToken": 0.03,
        "isAvailable": true,
        "isPremium": true,
        "responseTime": "medium",
        "accuracy": "very_high"
      }
    ]
  }
}
```

### 2. 获取模型详情

```http
GET /ai/models/{modelId}
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "gpt-3.5-turbo",
    "name": "GPT-3.5 Turbo",
    "provider": "OpenAI",
    "version": "0613",
    "description": "快速响应的对话模型，适合日常问答",
    "capabilities": ["chat", "document_qa", "search"],
    "parameters": {
      "maxTokens": 4096,
      "temperature": 0.7,
      "topP": 1.0,
      "frequencyPenalty": 0.0,
      "presencePenalty": 0.0
    },
    "pricing": {
      "inputTokens": 0.0015,
      "outputTokens": 0.002,
      "currency": "USD"
    },
    "limits": {
      "requestsPerMinute": 60,
      "tokensPerMinute": 90000,
      "requestsPerDay": 3000
    },
    "status": "active",
    "lastUpdated": "2024-01-20T15:30:00Z"
  }
}
```

## 💬 聊天对话 API

### 1. 发送聊天消息

```http
POST /ai/chat
```

**请求体**

```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "system",
      "content": "你是一个专业的文档助手"
    },
    {
      "role": "user",
      "content": "请帮我分析这个文档的要点"
    }
  ],
  "context": {
    "workspaceId": "ws_123456",
    "documentId": "doc_123456",
    "sessionId": "session_123456"
  },
  "parameters": {
    "temperature": 0.7,
    "maxTokens": 2048,
    "stream": false
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "chat_123456",
    "model": "gpt-3.5-turbo",
    "response": {
      "role": "assistant",
      "content": "根据文档内容，我为您总结以下要点：\n\n1. **核心概念**：文档主要介绍了...\n2. **关键流程**：包含以下几个步骤...\n3. **注意事项**：需要特别关注...",
      "finishReason": "stop"
    },
    "usage": {
      "promptTokens": 150,
      "completionTokens": 200,
      "totalTokens": 350
    },
    "cost": 0.0007,
    "responseTime": 1.5,
    "timestamp": "2024-01-20T15:30:00Z"
  }
}
```

### 2. 流式聊天

```http
POST /ai/chat/stream
```

**请求体**

```json
{
  "model": "gpt-3.5-turbo",
  "messages": [
    {
      "role": "user",
      "content": "请详细解释这个概念"
    }
  ],
  "parameters": {
    "stream": true
  }
}
```

**响应格式 (Server-Sent Events)**

```
data: {"type": "start", "id": "chat_123456"}

data: {"type": "content", "content": "这个"}

data: {"type": "content", "content": "概念"}

data: {"type": "content", "content": "是指..."}

data: {"type": "end", "usage": {"totalTokens": 250}, "cost": 0.0005}
```

## 📄 文档问答 API

### 1. 文档问答

```http
POST /ai/document/ask
```

**请求体**

```json
{
  "question": "这个文档的主要内容是什么？",
  "documentIds": ["doc_123456", "doc_789012"],
  "model": "gpt-3.5-turbo",
  "context": {
    "workspaceId": "ws_123456",
    "includeMetadata": true,
    "searchScope": "content"
  },
  "parameters": {
    "temperature": 0.3,
    "maxTokens": 1024
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "answer": "根据文档内容分析，主要内容包括：\n\n1. **产品概述**：介绍了产品的核心功能...\n2. **技术架构**：详细说明了系统架构...\n3. **使用指南**：提供了详细的操作步骤...",
    "sources": [
      {
        "documentId": "doc_123456",
        "documentTitle": "产品需求文档",
        "relevantSections": [
          {
            "content": "产品核心功能包括...",
            "startPosition": 150,
            "endPosition": 300,
            "relevanceScore": 0.95
          }
        ]
      }
    ],
    "confidence": 0.92,
    "model": "gpt-3.5-turbo",
    "usage": {
      "totalTokens": 450
    },
    "responseTime": 2.1
  }
}
```

### 2. 获取文档摘要

```http
GET /ai/document/summary/{documentId}
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| length | String | 否 | 摘要长度：short/medium/long |
| language | String | 否 | 摘要语言：zh-CN/en-US |
| focus | String | 否 | 关注点：overview/technical/business |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "documentId": "doc_123456",
    "summary": {
      "overview": "这是一份关于产品功能设计的详细文档...",
      "keyPoints": [
        "核心功能模块设计",
        "用户交互流程",
        "技术实现方案",
        "性能优化策略"
      ],
      "wordCount": 150,
      "readingTime": "2分钟"
    },
    "metadata": {
      "originalWordCount": 2500,
      "compressionRatio": 0.06,
      "generatedAt": "2024-01-20T15:30:00Z"
    }
  }
}
```

### 3. 批量分析文档

```http
POST /ai/document/batch-analyze
```

**请求体**

```json
{
  "documentIds": ["doc_123", "doc_456", "doc_789"],
  "analysisType": "summary",
  "parameters": {
    "includeKeywords": true,
    "includeSentiment": true,
    "includeTopics": true
  }
}
```

## 🧪 原辅料分析 API

### 1. 原辅料问答

```http
POST /ai/material/ask
```

**请求体**

```json
{
  "question": "这个原辅料的安全性如何？",
  "materialIds": ["material_123456"],
  "context": {
    "analysisType": "safety",
    "includeRegulations": true,
    "region": "CN"
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "answer": "根据安全数据分析，该原辅料的安全性评估如下：\n\n**安全等级**：低风险\n**主要风险**：无显著风险\n**使用建议**：按标准用量使用...",
    "analysis": {
      "safetyLevel": "low_risk",
      "riskFactors": [],
      "recommendations": [
        "按照标准用量使用",
        "避免与强氧化剂接触",
        "储存在干燥环境中"
      ],
      "regulations": [
        {
          "region": "CN",
          "standard": "GB 2760-2014",
          "status": "approved",
          "maxUsage": "0.5%"
        }
      ]
    },
    "sources": [
      {
        "materialId": "material_123456",
        "materialName": "柠檬酸",
        "dataSource": "safety_database",
        "lastUpdated": "2024-01-15T00:00:00Z"
      }
    ]
  }
}
```

### 2. 获取原辅料分析报告

```http
GET /ai/material/analysis/{materialId}
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "materialId": "material_123456",
    "materialName": "柠檬酸",
    "analysis": {
      "composition": {
        "mainComponent": "柠檬酸",
        "purity": "≥99.5%",
        "impurities": ["水分", "硫酸灰分", "重金属"]
      },
      "properties": {
        "appearance": "白色结晶粉末",
        "solubility": "易溶于水",
        "stability": "稳定",
        "ph": "2.1 (1% 水溶液)"
      },
      "safety": {
        "toxicity": "低毒",
        "irritation": "轻微刺激",
        "allergenicity": "无"
      },
      "applications": [
        "食品添加剂",
        "化妆品原料",
        "医药中间体"
      ],
      "regulations": [
        {
          "region": "CN",
          "standard": "GB 1987-2007",
          "status": "approved"
        }
      ]
    },
    "generatedAt": "2024-01-20T15:30:00Z"
  }
}
```

## 🔍 智能搜索 API

### 1. 智能搜索

```http
POST /ai/search
```

**请求体**

```json
{
  "query": "产品设计相关的文档",
  "scope": {
    "workspaceId": "ws_123456",
    "documentTypes": ["markdown", "pdf"],
    "dateRange": {
      "from": "2024-01-01",
      "to": "2024-01-31"
    }
  },
  "parameters": {
    "useSemanticSearch": true,
    "includeContent": true,
    "maxResults": 20
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "results": [
      {
        "id": "doc_123456",
        "title": "产品UI设计规范",
        "type": "document",
        "snippet": "本文档详细介绍了产品的UI设计规范，包括颜色、字体、布局等...",
        "relevanceScore": 0.95,
        "semanticScore": 0.88,
        "matchedKeywords": ["产品", "设计", "规范"],
        "author": "张三",
        "lastModified": "2024-01-20T15:30:00Z",
        "url": "/workspace/ws_123456/documents/doc_123456"
      }
    ],
    "total": 15,
    "searchTime": 0.12,
    "suggestions": [
      "UI设计",
      "产品原型",
      "设计系统"
    ]
  }
}
```

### 2. 获取搜索建议

```http
GET /ai/search/suggestions
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| q | String | 是 | 搜索关键词 |
| limit | Number | 否 | 建议数量，默认5 |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "suggestions": [
      {
        "text": "产品设计规范",
        "type": "document",
        "count": 8
      },
      {
        "text": "产品需求分析",
        "type": "document",
        "count": 5
      },
      {
        "text": "产品原型设计",
        "type": "document",
        "count": 3
      }
    ]
  }
}
```

## 💡 推荐问题 API

### 1. 获取推荐问题

```http
POST /ai/questions/recommend
```

**请求体**

```json
{
  "context": {
    "workspaceId": "ws_123456",
    "documentId": "doc_123456",
    "userRole": "product_manager",
    "recentQuestions": ["文档的主要内容是什么？"]
  },
  "count": 5
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "questions": [
      {
        "id": "q_123",
        "text": "这个功能的技术实现难度如何？",
        "category": "technical",
        "relevanceScore": 0.92,
        "estimatedAnswerTime": "30秒"
      },
      {
        "id": "q_456",
        "text": "用户对这个功能的反馈如何？",
        "category": "user_feedback",
        "relevanceScore": 0.88,
        "estimatedAnswerTime": "45秒"
      }
    ]
  }
}
```

## 📊 聊天会话管理 API

### 1. 创建聊天会话

```http
POST /ai/chat/session
```

**请求体**

```json
{
  "title": "产品设计讨论",
  "model": "gpt-3.5-turbo",
  "context": {
    "workspaceId": "ws_123456",
    "documentIds": ["doc_123456"]
  }
}
```

### 2. 获取聊天会话列表

```http
GET /ai/chat/sessions
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "sessions": [
      {
        "id": "session_123456",
        "title": "产品设计讨论",
        "model": "gpt-3.5-turbo",
        "messageCount": 15,
        "lastMessageAt": "2024-01-20T15:30:00Z",
        "createdAt": "2024-01-20T14:00:00Z",
        "isActive": true
      }
    ],
    "total": 10
  }
}
```

### 3. 获取聊天历史

```http
GET /ai/chat/history/{sessionId}
```

### 4. 删除聊天会话

```http
DELETE /ai/chat/session/{sessionId}
```

### 5. 导出聊天记录

```http
GET /ai/chat/export/{sessionId}
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| format | String | 否 | 导出格式：pdf/markdown/json |

## 🚨 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |
| 2001 | AI模型不可用 |
| 2002 | Token额度不足 |
| 2003 | 内容被过滤 |
| 2004 | 上下文过长 |

## 📝 使用示例

### JavaScript/Axios

```javascript
// 发送AI问答
const askAI = async (question, context = {}) => {
  try {
    const response = await axios.post('/api/v1/ai/chat', {
      model: 'gpt-3.5-turbo',
      messages: [
        { role: 'user', content: question }
      ],
      context,
      parameters: {
        temperature: 0.7,
        maxTokens: 1024
      }
    }, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })
    return response.data
  } catch (error) {
    console.error('AI问答失败:', error)
    throw error
  }
}

// 文档问答
const askDocument = async (question, documentIds) => {
  try {
    const response = await axios.post('/api/v1/ai/document/ask', {
      question,
      documentIds,
      model: 'gpt-3.5-turbo'
    }, {
      headers: { 'Authorization': `Bearer ${token}` }
    })
    return response.data
  } catch (error) {
    console.error('文档问答失败:', error)
    throw error
  }
}
```
