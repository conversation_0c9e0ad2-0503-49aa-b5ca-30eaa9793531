import request from '@/utils/request'

// 查询原辅料列表（统一接口）
export function listMaterial (query) {
  return request({
    url: '/knowledge/material/list',
    method: 'get',
    params: query
  })
}

// 查询原辅料详细
export function getMaterial (id) {
  return request({
    url: '/knowledge/material/' + id,
    method: 'get'
  })
}

// 新增原辅料
export function addMaterial (data) {
  return request({
    url: '/knowledge/material',
    method: 'post',
    data: data
  })
}

// 修改原辅料
export function updateMaterial (data) {
  return request({
    url: '/knowledge/material',
    method: 'put',
    data: data
  })
}

// 删除原辅料
export function delMaterial (id) {
  return request({
    url: '/knowledge/material/' + id,
    method: 'delete'
  })
}

// 根据文献ID查询原辅料列表
export function listMaterialByLiterature (literatureId, materialType = null) {
  const params = { literatureId }
  if (materialType) {
    params.materialType = materialType
  }
  return request({
    url: '/knowledge/material/list',
    method: 'get',
    params: params
  })
}

// 导入原辅料
export function importMaterial (data) {
  return request({
    url: '/knowledge/material/import',
    method: 'post',
    data: data
  })
}



