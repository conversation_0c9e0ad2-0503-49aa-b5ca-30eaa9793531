# 工作空间文档管理功能使用指南

## 📋 概述

本文档介绍工作空间模块新增的文档管理功能，包括富文本编辑、实时协同、版本控制等核心特性。

## 🚀 核心功能

### 1. 富文本编辑器 (RichTextEditor)

#### 功能特性
- **三种编辑模式**：富文本、Markdown、块编辑器
- **模板库**：内置会议记录、项目计划等模板
- **格式化工具**：粗体、斜体、标题、列表、代码块等
- **自动保存**：实时保存编辑内容

#### 使用方法

```vue
<template>
  <RichTextEditor
    v-model="content"
    :document-id="documentId"
    :enable-collaboration="true"
    :show-preview="true"
    @auto-save="handleAutoSave"
    @version-created="handleVersionCreated"
    @version-restored="handleVersionRestored"
  />
</template>

<script>
import RichTextEditor from '@/components/RichTextEditor'

export default {
  components: {
    RichTextEditor
  },
  data() {
    return {
      content: '',
      documentId: 'doc_123'
    }
  },
  methods: {
    handleAutoSave(content) {
      console.log('自动保存:', content)
    },
    handleVersionCreated(version) {
      console.log('版本创建:', version)
    },
    handleVersionRestored(version) {
      console.log('版本恢复:', version)
    }
  }
}
</script>
```

#### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| value | String | '' | 编辑器内容 |
| documentId | String | 必填 | 文档唯一标识 |
| enableCollaboration | Boolean | true | 是否启用协作 |
| showPreview | Boolean | true | 是否显示预览 |

#### 事件说明

| 事件 | 参数 | 说明 |
|------|------|------|
| input | content | 内容变化 |
| auto-save | content | 自动保存 |
| version-created | version | 版本创建 |
| version-restored | version | 版本恢复 |

### 2. 协作光标 (CollaborativeCursor)

#### 功能特性
- **实时光标同步**：显示其他用户的光标位置
- **用户标识**：不同颜色区分不同用户
- **选择区域显示**：显示用户选中的文本区域

#### 使用方法

```vue
<template>
  <div class="editor-container">
    <textarea ref="editor" v-model="content"></textarea>
    <CollaborativeCursor
      :cursors="collaborativeCursors"
      :current-connection-id="connectionId"
      :editor-element="$refs.editor"
    />
  </div>
</template>

<script>
import CollaborativeCursor from '@/components/CollaborativeCursor'

export default {
  components: {
    CollaborativeCursor
  },
  data() {
    return {
      content: '',
      collaborativeCursors: new Map(),
      connectionId: 'user_123'
    }
  }
}
</script>
```

#### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| cursors | Map | new Map() | 光标位置映射 |
| currentConnectionId | String | '' | 当前用户连接ID |
| editorElement | HTMLElement | null | 编辑器DOM元素 |

### 3. 评论系统 (CommentSystem)

#### 功能特性
- **添加评论**：支持在文档中添加评论
- **回复评论**：支持评论的回复功能
- **@提醒**：支持@其他用户进行提醒
- **评论管理**：删除、解决评论

#### 使用方法

```vue
<template>
  <CommentSystem
    :document-id="documentId"
    :connection-id="connectionId"
    :collaborators="collaborators"
    :current-user="currentUser"
  />
</template>

<script>
import CommentSystem from '@/components/CommentSystem'

export default {
  components: {
    CommentSystem
  },
  data() {
    return {
      documentId: 'doc_123',
      connectionId: 'conn_123',
      collaborators: [
        { id: 1, nickname: '用户1', avatar: '' },
        { id: 2, nickname: '用户2', avatar: '' }
      ],
      currentUser: { id: 1, nickname: '当前用户', avatar: '' }
    }
  }
}
</script>
```

#### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| documentId | String | 必填 | 文档ID |
| connectionId | String | 必填 | 连接ID |
| collaborators | Array | [] | 协作者列表 |
| currentUser | Object | 必填 | 当前用户信息 |

### 4. 版本历史 (VersionHistory)

#### 功能特性
- **版本列表**：显示文档的所有历史版本
- **版本对比**：对比两个版本的差异
- **版本恢复**：恢复到指定版本
- **版本标签**：为版本添加标签
- **分支管理**：创建和管理分支

#### 使用方法

```vue
<template>
  <VersionHistory
    :document-id="documentId"
    :current-content="content"
    :current-user="currentUser"
    @version-created="handleVersionCreated"
    @version-restored="handleVersionRestored"
  />
</template>

<script>
import VersionHistory from '@/components/VersionHistory'

export default {
  components: {
    VersionHistory
  },
  data() {
    return {
      documentId: 'doc_123',
      content: '当前文档内容',
      currentUser: { id: 1, name: '用户名' }
    }
  },
  methods: {
    handleVersionCreated(version) {
      console.log('新版本创建:', version)
    },
    handleVersionRestored(version) {
      this.content = version.content
      console.log('版本已恢复:', version)
    }
  }
}
</script>
```

#### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| documentId | String | 必填 | 文档ID |
| currentContent | String | '' | 当前内容 |
| currentUser | Object | 必填 | 当前用户 |

### 5. 增强文件管理器 (EnhancedFileManager)

#### 功能特性
- **多视图模式**：网格、列表、时间线视图
- **高级搜索**：支持文件名、类型、时间等搜索
- **批量操作**：支持批量删除、移动等操作
- **协作状态**：显示文件的协作状态
- **版本信息**：显示文件的版本信息

#### 使用方法

```vue
<template>
  <EnhancedFileManager
    :workspace-id="workspaceId"
    :workspace-name="workspaceName"
  />
</template>

<script>
import EnhancedFileManager from '@/components/EnhancedFileManager'

export default {
  components: {
    EnhancedFileManager
  },
  data() {
    return {
      workspaceId: 'workspace_123',
      workspaceName: '我的工作空间'
    }
  }
}
</script>
```

#### 属性说明

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| workspaceId | String | 必填 | 工作空间ID |
| workspaceName | String | '工作空间' | 工作空间名称 |

## 🔧 服务使用

### 1. 协作服务 (enhancedCollaboration.js)

#### 基本使用

```javascript
import enhancedCollaborationService from '@/utils/enhancedCollaboration'

// 连接到协作会话
const connectionId = enhancedCollaborationService.connect(documentId, userInfo)

// 发送操作
enhancedCollaborationService.sendOperation(connectionId, {
  type: 'content-change',
  content: '新内容',
  timestamp: Date.now()
})

// 更新光标位置
enhancedCollaborationService.updateCursor(connectionId, {
  position: 100,
  selection: { start: 100, end: 120 }
})

// 添加评论
enhancedCollaborationService.addComment(connectionId, {
  content: '这是一条评论',
  position: 50
})

// 监听事件
enhancedCollaborationService.on(connectionId, 'operation', (operation) => {
  console.log('收到操作:', operation)
})

// 断开连接
enhancedCollaborationService.disconnect(connectionId)
```

#### 主要方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| connect | documentId, userInfo | connectionId | 连接协作会话 |
| disconnect | connectionId | - | 断开连接 |
| sendOperation | connectionId, operation | - | 发送操作 |
| updateCursor | connectionId, cursor | - | 更新光标 |
| addComment | connectionId, comment | comment | 添加评论 |
| getCollaborators | documentId | Array | 获取协作者 |
| getCursors | documentId | Map | 获取光标位置 |
| getComments | documentId | Array | 获取评论列表 |

### 2. 版本控制服务 (versionControl.js)

#### 基本使用

```javascript
import versionControlService from '@/utils/versionControl'

// 初始化文档
versionControlService.initDocument(documentId, initialContent, author)

// 创建版本
const version = versionControlService.createVersion(documentId, {
  content: '文档内容',
  title: '版本标题',
  author: { id: 1, name: '作者' },
  message: '版本说明'
})

// 获取版本列表
const versions = versionControlService.getVersions(documentId, {
  sortBy: 'date',
  limit: 10
})

// 版本对比
const comparison = versionControlService.compareVersions(
  documentId, 
  versionId1, 
  versionId2
)

// 回滚版本
const rollbackVersion = versionControlService.rollbackToVersion(
  documentId, 
  targetVersionId, 
  author
)

// 创建标签
versionControlService.createTag(
  documentId, 
  'v1.0', 
  versionId, 
  '正式版本'
)

// 创建分支
versionControlService.createBranch(
  documentId, 
  'feature-branch', 
  fromVersionId
)
```

#### 主要方法

| 方法 | 参数 | 返回值 | 说明 |
|------|------|--------|------|
| initDocument | documentId, content, author | document | 初始化文档 |
| createVersion | documentId, versionData | version | 创建版本 |
| getVersions | documentId, options | Array | 获取版本列表 |
| getVersion | documentId, versionId | version | 获取指定版本 |
| compareVersions | documentId, v1, v2 | comparison | 版本对比 |
| rollbackToVersion | documentId, versionId, author | version | 回滚版本 |
| createTag | documentId, name, versionId, desc | tag | 创建标签 |
| createBranch | documentId, name, fromVersionId | branch | 创建分支 |

## 📝 集成示例

### 完整的文档编辑页面

```vue
<template>
  <div class="document-editor">
    <!-- 工具栏 -->
    <div class="toolbar">
      <el-button @click="saveDocument">保存</el-button>
      <el-button @click="showVersionHistory = true">版本历史</el-button>
      <el-button @click="showComments = true">评论</el-button>
    </div>

    <!-- 编辑器 -->
    <RichTextEditor
      v-model="content"
      :document-id="documentId"
      :enable-collaboration="true"
      @auto-save="handleAutoSave"
    />

    <!-- 版本历史 -->
    <VersionHistory
      v-if="showVersionHistory"
      :document-id="documentId"
      :current-content="content"
      :current-user="currentUser"
      @version-restored="handleVersionRestored"
    />

    <!-- 评论系统 -->
    <CommentSystem
      v-if="showComments"
      :document-id="documentId"
      :connection-id="connectionId"
      :collaborators="collaborators"
      :current-user="currentUser"
    />
  </div>
</template>

<script>
import RichTextEditor from '@/components/RichTextEditor'
import VersionHistory from '@/components/VersionHistory'
import CommentSystem from '@/components/CommentSystem'
import enhancedCollaborationService from '@/utils/enhancedCollaboration'
import versionControlService from '@/utils/versionControl'

export default {
  components: {
    RichTextEditor,
    VersionHistory,
    CommentSystem
  },
  data() {
    return {
      content: '',
      documentId: 'doc_123',
      connectionId: null,
      collaborators: [],
      currentUser: { id: 1, name: '当前用户' },
      showVersionHistory: false,
      showComments: false
    }
  },
  mounted() {
    this.initCollaboration()
    this.initVersionControl()
  },
  methods: {
    initCollaboration() {
      this.connectionId = enhancedCollaborationService.connect(
        this.documentId, 
        this.currentUser
      )
      this.collaborators = enhancedCollaborationService.getCollaborators(
        this.documentId
      )
    },
    
    initVersionControl() {
      versionControlService.initDocument(
        this.documentId, 
        this.content, 
        this.currentUser
      )
    },
    
    handleAutoSave(content) {
      // 自动保存逻辑
      console.log('自动保存:', content)
    },
    
    handleVersionRestored(version) {
      this.content = version.content
      this.showVersionHistory = false
    },
    
    saveDocument() {
      // 创建新版本
      versionControlService.createVersion(this.documentId, {
        content: this.content,
        title: `版本 ${Date.now()}`,
        author: this.currentUser,
        message: '手动保存'
      })
    }
  }
}
</script>
```

## 🎯 最佳实践

1. **性能优化**
   - 使用防抖处理频繁的内容变更
   - 限制协作用户数量
   - 定期清理过期的版本和评论

2. **错误处理**
   - 添加网络断线重连机制
   - 处理协作冲突
   - 版本创建失败的回滚

3. **用户体验**
   - 提供清晰的协作状态指示
   - 实时显示保存状态
   - 友好的错误提示

4. **安全考虑**
   - 验证用户权限
   - 过滤恶意内容
   - 限制操作频率

## 📚 API 接口

详细的API接口文档请参考：
- [版本控制API](./src/api/workspace/version.js)
- [协作API](./src/api/workspace/collaboration.js)
- [模板API](./src/api/workspace/template.js)
- [文档管理API](./src/api/workspace/file.js)

## 🔍 故障排除

### 常见问题

1. **协作功能不工作**
   - 检查WebSocket连接
   - 确认用户权限
   - 查看控制台错误

2. **版本创建失败**
   - 检查内容是否为空
   - 确认用户信息完整
   - 查看存储空间

3. **评论不显示**
   - 检查文档ID是否正确
   - 确认用户在协作列表中
   - 查看网络连接

### 调试技巧

```javascript
// 开启调试模式
localStorage.setItem('debug', 'collaboration,version')

// 查看协作状态
console.log(enhancedCollaborationService.getDocumentStatus(documentId))

// 查看版本统计
console.log(versionControlService.getDocumentStats(documentId))
```

---

📧 如有问题，请联系开发团队或查看项目文档。
