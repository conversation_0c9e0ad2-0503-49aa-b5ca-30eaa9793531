<template>
  <div class="app-container material-detail">
    <!-- 页面头部 -->
    <div class="detail-header">
      <el-page-header @back="goBack" :content="pageTitle">
        <template slot="content">
          <span class="detail-title">{{ pageTitle }}</span>
          <el-tag v-if="materialData.type === 'raw'" type="primary" class="type-tag">原料</el-tag>
          <el-tag v-else-if="materialData.type === 'auxiliary'" type="success" class="type-tag">辅料</el-tag>
        </template>
      </el-page-header>
    </div>

    <!-- 详情内容 -->
    <div class="detail-content" v-loading="loading">
      <el-card class="detail-card">
        <!-- 基本信息 -->
        <div class="section">
          <h3 class="section-title">基本信息</h3>
          <div class="info-grid">
            <div class="info-item" v-if="materialData.molecularFormula || isEditing">
              <label>分子式：</label>
              <span v-if="!isEditing">{{ materialData.molecularFormula }}</span>
              <el-input v-else v-model="editForm.molecularFormula" placeholder="请输入分子式"></el-input>
            </div>
            <div class="info-item" v-if="materialData.molecularWeight || isEditing">
              <label>分子质量：</label>
              <span v-if="!isEditing">{{ materialData.molecularWeight }}</span>
              <el-input v-else v-model="editForm.molecularWeight" placeholder="请输入分子质量"></el-input>
            </div>
            <div class="info-item" v-if="materialData.specification || isEditing">
              <label>规格：</label>
              <span v-if="!isEditing">{{ materialData.specification }}</span>
              <el-input v-else v-model="editForm.specification" placeholder="请输入规格"></el-input>
            </div>
          </div>
        </div>

        <!-- 化学结构图 -->
        <!-- <div class="section" v-if="materialData.structureImage">
          <h3 class="section-title">化学结构图</h3>
          <div class="structure-image">
            <img :src="materialData.structureImage" alt="化学结构图" />
          </div>
        </div> -->

        <!-- 简介描述 -->
        <div class="section" v-if="materialData.description || isEditing">
          <h3 class="section-title">简介描述</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.description }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.description" placeholder="请输入简介描述" :rows="4"></el-input>
        </div>

        <!-- 性状 -->
        <div class="section" v-if="materialData.trait || isEditing">
          <h3 class="section-title">性状</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.trait }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.trait" placeholder="请输入性状" :rows="3"></el-input>
        </div>

        <!-- 鉴别 -->
        <div class="section" v-if="materialData.identification || isEditing">
          <h3 class="section-title">鉴别</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.identification }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.identification" placeholder="请输入鉴别" :rows="3"></el-input>
        </div>

        <!-- 检查 -->
        <div class="section" v-if="materialData.inspection || isEditing">
          <h3 class="section-title">检查</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.inspection }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.inspection" placeholder="请输入检查" :rows="3"></el-input>
        </div>

        <!-- 含量测定 -->
        <div class="section" v-if="materialData.assay || isEditing">
          <h3 class="section-title">含量测定</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.assay }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.assay" placeholder="请输入含量测定" :rows="3"></el-input>
        </div>

        <!-- 贮藏 -->
        <div class="section" v-if="materialData.storage || isEditing">
          <h3 class="section-title">贮藏</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.storage }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.storage" placeholder="请输入贮藏" :rows="3"></el-input>
        </div>

        <!-- 贮藏 -->
        <div class="section" v-if="materialData.type || isEditing">
          <h3 class="section-title">类别</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.type }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.type" placeholder="请输入类别" :rows="3"></el-input>
        </div>

        <!-- 制剂 -->
        <div class="section" v-if="materialData.preparation || isEditing">
          <h3 class="section-title">制剂</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.preparation }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.preparation" placeholder="请输入制剂" :rows="3"></el-input>
        </div>

        <!-- 作用与用途 -->
        <div class="section" v-if="materialData.actionAndUse || isEditing">
          <h3 class="section-title">作用与用途</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.actionAndUse }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.actionAndUse" placeholder="请输入作用与用途" :rows="3"></el-input>
        </div>

        <!-- 用法与用量 -->
        <div class="section" v-if="materialData.usageAndDosage || isEditing">
          <h3 class="section-title">用法与用量</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.usageAndDosage }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.usageAndDosage" placeholder="请输入用法与用量"
            :rows="3"></el-input>
        </div>

        <!-- 不良反应 -->
        <div class="section" v-if="materialData.adverseReaction || isEditing">
          <h3 class="section-title">不良反应</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.adverseReaction }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.adverseReaction" placeholder="请输入不良反应"
            :rows="3"></el-input>
        </div>

        <!-- 注意事项 -->
        <div class="section" v-if="materialData.precautions || isEditing">
          <h3 class="section-title">注意事项</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.precautions }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.precautions" placeholder="请输入注意事项" :rows="3"></el-input>
        </div>

        <!-- 休药期 -->
        <div class="section" v-if="materialData.withdrawalPeriod || isEditing">
          <h3 class="section-title">休药期</h3>
          <div v-if="!isEditing" class="content-text">
            {{ materialData.withdrawalPeriod }}
          </div>
          <el-input v-else type="textarea" v-model="editForm.withdrawalPeriod" placeholder="请输入休药期"
            :rows="3"></el-input>
        </div>
      </el-card>

      <!-- 底部操作按钮 -->
      <div class="detail-footer">
        <el-button @click="goBack">返回列表</el-button>
        <el-button v-if="!isEditing" type="primary" @click="handleEdit"
          v-hasPermi="['knowledge:material:edit']">编辑</el-button>
        <template v-else>
          <el-button @click="cancelEdit">取消</el-button>
          <el-button type="primary" @click="saveEdit" :loading="saving">保存</el-button>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { getMaterial, updateMaterial } from "@/api/knowledge/material"

export default {
  name: "MaterialDetail",
  data () {
    return {
      loading: true,
      saving: false,
      isEditing: false,
      materialData: {},
      editForm: {},
      materialId: null
    }
  },
  computed: {
    pageTitle () {
      if (!this.materialData.name) return '原辅料详情'
      const title = this.materialData.name.length > 20
        ? this.materialData.name.substring(0, 20) + '...'
        : this.materialData.name
      return this.isEditing ? `编辑 - ${title}` : title
    }
  },
  created () {
    this.materialId = this.$route.params.id
    this.getMaterialDetail()
  },
  methods: {
    /** 获取原辅料详情 */
    getMaterialDetail () {
      this.loading = true
      getMaterial(this.materialId).then(response => {
        this.materialData = response.data
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取详情失败')
      })
    },
    /** 返回列表页 */
    goBack () {
      if (this.isEditing) {
        this.$confirm('当前有未保存的修改，确定要离开吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$router.go(-1)
        })
      } else {
        this.$router.go(-1)
      }
    },
    /** 编辑按钮 */
    handleEdit() {
      this.isEditing = true
      // 复制数据到编辑表单
      this.editForm = { ...this.materialData }
    },
    /** 取消编辑 */
    cancelEdit() {
      this.$confirm('确定要取消编辑吗？未保存的修改将丢失。', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.isEditing = false
        this.editForm = {}
      })
    },
    /** 保存编辑 */
    saveEdit() {
      this.saving = true
      updateMaterial(this.editForm).then(response => {
        this.$message.success('修改成功')
        this.isEditing = false
        this.materialData = { ...this.editForm }
        this.editForm = {}
        this.saving = false
      }).catch(() => {
        this.saving = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.material-detail {
  .detail-header {
    margin-bottom: 20px;

    .detail-title {
      font-size: 18px;
      font-weight: 600;
      margin-right: 12px;
    }

    .type-tag {
      margin-left: 8px;
    }
  }

  .detail-content {
    .detail-card {
      margin-bottom: 20px;

      .section {
        margin-bottom: 32px;

        &:last-child {
          margin-bottom: 0;
        }

        .section-title {
          font-size: 16px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 16px 0;
          padding-bottom: 8px;
          border-bottom: 2px solid #409EFF;
          position: relative;

          &::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30px;
            height: 2px;
            background-color: #409EFF;
          }
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 16px;

          .info-item {
            display: flex;
            align-items: center;

            label {
              font-weight: 600;
              color: #606266;
              min-width: 80px;
              margin-right: 8px;
            }

            span {
              color: #303133;
              flex: 1;
            }
          }
        }

        .content-text {
          line-height: 1.8;
          color: #303133;
          font-size: 14px;
          text-align: justify;
          padding: 16px;
          background-color: #fafafa;
          border-radius: 4px;
          border-left: 4px solid #409EFF;
          white-space: pre-wrap;
        }

        .structure-image {
          text-align: center;
          padding: 20px;
          background-color: #fafafa;
          border-radius: 4px;

          img {
            max-width: 100%;
            max-height: 400px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }

    .detail-footer {
      text-align: center;
      padding: 20px 0;

      .el-button {
        margin: 0 8px;
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .material-detail {
    .detail-content {
      .detail-card {
        .section {
          .info-grid {
            grid-template-columns: 1fr;
          }
        }
      }
    }
  }
}
</style>




