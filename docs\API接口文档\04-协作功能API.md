# 协作功能 API 接口文档

## 📋 概述

协作功能模块提供实时协作编辑、光标同步、评论系统、提醒通知等功能，支持多人同时编辑文档。

## 🔗 基础信息

- **基础URL**: `/api/v1/workspace/{workspaceId}/documents/{documentId}/collaboration`
- **认证方式**: Bearer Token + WebSocket
- **数据格式**: JSON
- **字符编码**: UTF-8

## 🤝 协作会话 API

### 1. 加入协作会话

```http
POST /workspace/{workspaceId}/documents/{documentId}/collaboration/join
```

**请求体**

```json
{
  "userInfo": {
    "id": "user_123",
    "nickname": "张三",
    "avatar": "avatar.jpg",
    "role": "editor"
  },
  "clientInfo": {
    "platform": "web",
    "userAgent": "Mozilla/5.0...",
    "screenResolution": "1920x1080"
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "加入协作成功",
  "data": {
    "sessionId": "session_123456",
    "documentId": "doc_123456",
    "wsUrl": "wss://api.example.com/ws/collaboration/session_123456",
    "token": "ws_token_abc123",
    "currentContent": "当前文档内容...",
    "currentVersion": "v_123456",
    "collaborators": [
      {
        "id": "user_123",
        "nickname": "张三",
        "avatar": "avatar.jpg",
        "role": "editor",
        "isActive": true,
        "cursor": {
          "line": 10,
          "column": 25,
          "selection": {
            "start": {"line": 10, "column": 20},
            "end": {"line": 10, "column": 30}
          }
        },
        "lastActivity": "2024-01-20T15:30:00Z",
        "joinedAt": "2024-01-20T15:00:00Z"
      }
    ],
    "permissions": {
      "canEdit": true,
      "canComment": true,
      "canViewCursors": true
    }
  }
}
```

### 2. 离开协作会话

```http
POST /workspace/{workspaceId}/documents/{documentId}/collaboration/leave
```

**请求体**

```json
{
  "sessionId": "session_123456",
  "reason": "user_leave"
}
```

### 3. 获取协作者列表

```http
GET /workspace/{workspaceId}/documents/{documentId}/collaboration/users
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "collaborators": [
      {
        "id": "user_123",
        "nickname": "张三",
        "avatar": "avatar.jpg",
        "role": "editor",
        "isActive": true,
        "isOnline": true,
        "cursor": {
          "line": 10,
          "column": 25,
          "visible": true
        },
        "lastActivity": "2024-01-20T15:30:00Z",
        "joinedAt": "2024-01-20T15:00:00Z",
        "editCount": 15,
        "commentCount": 3
      }
    ],
    "totalCount": 3,
    "activeCount": 2
  }
}
```

## 📝 实时编辑 API (WebSocket)

### WebSocket 连接

```javascript
// 连接WebSocket
const ws = new WebSocket('wss://api.example.com/ws/collaboration/session_123456?token=ws_token_abc123')

// 连接成功
ws.onopen = function(event) {
  console.log('协作连接建立成功')
}

// 接收消息
ws.onmessage = function(event) {
  const message = JSON.parse(event.data)
  handleCollaborationMessage(message)
}
```

### 1. 发送编辑操作

**消息格式**

```json
{
  "type": "operation",
  "data": {
    "operationId": "op_123456",
    "type": "insert",
    "position": {
      "line": 10,
      "column": 25
    },
    "content": "插入的文本",
    "length": 6,
    "authorId": "user_123",
    "timestamp": 1642694400000,
    "version": "v_123456"
  }
}
```

### 2. 接收编辑操作

**消息格式**

```json
{
  "type": "operation_received",
  "data": {
    "operationId": "op_123457",
    "type": "delete",
    "position": {
      "line": 15,
      "column": 10
    },
    "length": 5,
    "authorId": "user_456",
    "authorName": "李四",
    "timestamp": 1642694460000,
    "version": "v_123457"
  }
}
```

### 3. 光标位置同步

**发送光标位置**

```json
{
  "type": "cursor_update",
  "data": {
    "userId": "user_123",
    "cursor": {
      "line": 20,
      "column": 15,
      "selection": {
        "start": {"line": 20, "column": 10},
        "end": {"line": 20, "column": 20}
      },
      "visible": true
    },
    "timestamp": 1642694500000
  }
}
```

**接收光标位置**

```json
{
  "type": "cursor_received",
  "data": {
    "userId": "user_456",
    "userName": "李四",
    "userAvatar": "avatar2.jpg",
    "cursor": {
      "line": 25,
      "column": 30,
      "selection": null,
      "visible": true,
      "color": "#ff6b6b"
    },
    "timestamp": 1642694520000
  }
}
```

## 💬 评论系统 API

### 1. 获取评论列表

```http
GET /workspace/{workspaceId}/documents/{documentId}/comments
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | Number | 否 | 页码，默认1 |
| pageSize | Number | 否 | 每页数量，默认20 |
| position | String | 否 | 位置筛选：line:10 |
| status | String | 否 | 状态筛选：open/resolved |
| author | String | 否 | 作者筛选 |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "comments": [
      {
        "id": "comment_123456",
        "content": "这里需要补充更多细节",
        "position": {
          "line": 15,
          "column": 20,
          "selection": {
            "start": {"line": 15, "column": 15},
            "end": {"line": 15, "column": 25}
          },
          "selectedText": "关键内容"
        },
        "authorId": "user_123",
        "authorName": "张三",
        "authorAvatar": "avatar.jpg",
        "status": "open",
        "priority": "normal",
        "type": "suggestion",
        "replies": [
          {
            "id": "reply_123",
            "content": "我来补充一下",
            "authorId": "user_456",
            "authorName": "李四",
            "authorAvatar": "avatar2.jpg",
            "createdAt": "2024-01-20T16:00:00Z"
          }
        ],
        "mentions": ["user_789"],
        "reactions": [
          {
            "type": "like",
            "count": 3,
            "users": ["user_456", "user_789", "user_101"]
          }
        ],
        "createdAt": "2024-01-20T15:30:00Z",
        "updatedAt": "2024-01-20T16:00:00Z",
        "resolvedAt": null,
        "resolvedBy": null
      }
    ],
    "total": 25,
    "page": 1,
    "pageSize": 20
  }
}
```

### 2. 添加评论

```http
POST /workspace/{workspaceId}/documents/{documentId}/comments
```

**请求体**

```json
{
  "content": "这里需要修改",
  "position": {
    "line": 20,
    "column": 10,
    "selection": {
      "start": {"line": 20, "column": 5},
      "end": {"line": 20, "column": 15}
    },
    "selectedText": "选中的文本"
  },
  "type": "suggestion",
  "priority": "high",
  "mentions": ["user_456", "user_789"]
}
```

### 3. 回复评论

```http
POST /workspace/{workspaceId}/documents/{documentId}/comments/{commentId}/replies
```

**请求体**

```json
{
  "content": "我同意这个建议",
  "mentions": ["user_123"]
}
```

### 4. 解决评论

```http
PUT /workspace/{workspaceId}/documents/{documentId}/comments/{commentId}/resolve
```

### 5. 删除评论

```http
DELETE /workspace/{workspaceId}/documents/{documentId}/comments/{commentId}
```

## 🔔 提醒通知 API

### 1. 获取提醒列表

```http
GET /workspace/{workspaceId}/documents/{documentId}/mentions
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "mentions": [
      {
        "id": "mention_123456",
        "type": "comment",
        "sourceId": "comment_123456",
        "sourceType": "comment",
        "mentionedUserId": "user_123",
        "mentionedByUserId": "user_456",
        "mentionedByUserName": "李四",
        "content": "在评论中提到了您",
        "context": "这里需要@张三 来确认一下",
        "documentTitle": "产品需求文档",
        "isRead": false,
        "readAt": null,
        "createdAt": "2024-01-20T15:30:00Z"
      }
    ],
    "unreadCount": 5,
    "total": 15
  }
}
```

### 2. 标记提醒已读

```http
PUT /workspace/{workspaceId}/documents/{documentId}/mentions/{mentionId}/read
```

### 3. 批量标记已读

```http
PUT /workspace/{workspaceId}/documents/{documentId}/mentions/batch-read
```

**请求体**

```json
{
  "mentionIds": ["mention_123", "mention_456", "mention_789"]
}
```

## 📊 协作统计 API

### 1. 获取协作统计

```http
GET /workspace/{workspaceId}/documents/{documentId}/collaboration/statistics
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "overview": {
      "totalCollaborators": 8,
      "activeCollaborators": 3,
      "totalSessions": 25,
      "totalOperations": 1250,
      "totalComments": 45
    },
    "activity": {
      "todayOperations": 85,
      "todayComments": 12,
      "todayActiveUsers": 5,
      "peakConcurrentUsers": 6
    },
    "collaborators": [
      {
        "userId": "user_123",
        "userName": "张三",
        "operationCount": 250,
        "commentCount": 15,
        "sessionCount": 8,
        "totalTime": 7200,
        "lastActiveAt": "2024-01-20T15:30:00Z"
      }
    ],
    "timeline": [
      {
        "date": "2024-01-20",
        "operations": 85,
        "comments": 12,
        "activeUsers": 5
      }
    ]
  }
}
```

## 🔄 冲突解决 API

### 1. 获取冲突列表

```http
GET /workspace/{workspaceId}/documents/{documentId}/collaboration/conflicts
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "conflicts": [
      {
        "id": "conflict_123456",
        "type": "content",
        "position": {
          "line": 25,
          "column": 10
        },
        "operations": [
          {
            "operationId": "op_123",
            "authorId": "user_123",
            "authorName": "张三",
            "content": "版本A的内容",
            "timestamp": 1642694400000
          },
          {
            "operationId": "op_124",
            "authorId": "user_456",
            "authorName": "李四",
            "content": "版本B的内容",
            "timestamp": 1642694410000
          }
        ],
        "status": "pending",
        "createdAt": "2024-01-20T15:30:00Z"
      }
    ]
  }
}
```

### 2. 解决冲突

```http
POST /workspace/{workspaceId}/documents/{documentId}/collaboration/conflicts/{conflictId}/resolve
```

**请求体**

```json
{
  "resolution": "manual",
  "selectedOperationId": "op_123",
  "mergedContent": "合并后的内容",
  "message": "手动解决冲突"
}
```

## 🚨 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 协作会话不存在 |
| 409 | 操作冲突 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |
| 1001 | WebSocket连接失败 |
| 1002 | 协作会话已满 |
| 1003 | 文档被锁定 |

## 📝 使用示例

### JavaScript WebSocket 客户端

```javascript
class CollaborationClient {
  constructor(sessionId, token) {
    this.sessionId = sessionId
    this.token = token
    this.ws = null
    this.isConnected = false
  }

  connect() {
    const wsUrl = `wss://api.example.com/ws/collaboration/${this.sessionId}?token=${this.token}`
    this.ws = new WebSocket(wsUrl)
    
    this.ws.onopen = () => {
      this.isConnected = true
      console.log('协作连接建立')
    }
    
    this.ws.onmessage = (event) => {
      const message = JSON.parse(event.data)
      this.handleMessage(message)
    }
    
    this.ws.onclose = () => {
      this.isConnected = false
      console.log('协作连接断开')
    }
  }

  sendOperation(operation) {
    if (this.isConnected) {
      this.ws.send(JSON.stringify({
        type: 'operation',
        data: operation
      }))
    }
  }

  updateCursor(cursor) {
    if (this.isConnected) {
      this.ws.send(JSON.stringify({
        type: 'cursor_update',
        data: cursor
      }))
    }
  }

  handleMessage(message) {
    switch (message.type) {
      case 'operation_received':
        this.applyOperation(message.data)
        break
      case 'cursor_received':
        this.updateRemoteCursor(message.data)
        break
      case 'user_joined':
        this.onUserJoined(message.data)
        break
      case 'user_left':
        this.onUserLeft(message.data)
        break
    }
  }
}
```
