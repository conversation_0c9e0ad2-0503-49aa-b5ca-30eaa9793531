# 文档管理功能快速开始指南

## 🚀 快速开始

本指南将帮助您快速集成和使用工作空间文档管理功能。

## 📦 安装依赖

确保您的项目已安装以下依赖：

```bash
npm install marked quill vue-quill-editor
```

或者如果使用 yarn：

```bash
yarn add marked quill vue-quill-editor
```

## 🔧 基础配置

### 1. 导入样式

在您的主样式文件中导入必要的样式：

```css
/* main.css 或 App.vue */
@import 'quill/dist/quill.snow.css';
@import 'quill/dist/quill.bubble.css';
```

### 2. 全局配置

在 `main.js` 中进行全局配置：

```javascript
import Vue from 'vue'
import VueQuillEditor from 'vue-quill-editor'

// 导入样式
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'

Vue.use(VueQuillEditor)
```

## 🎯 5分钟快速集成

### 步骤1：创建基础编辑页面

```vue
<template>
  <div class="document-page">
    <h1>文档编辑器</h1>
    
    <!-- 基础富文本编辑器 -->
    <RichTextEditor
      v-model="content"
      :document-id="documentId"
      :enable-collaboration="true"
      @auto-save="handleAutoSave"
    />
    
    <!-- 版本历史按钮 -->
    <el-button @click="showVersions = true">查看版本历史</el-button>
    
    <!-- 版本历史组件 -->
    <VersionHistory
      v-if="showVersions"
      :document-id="documentId"
      :current-content="content"
      :current-user="currentUser"
      @version-restored="handleVersionRestored"
    />
  </div>
</template>

<script>
import RichTextEditor from '@/components/RichTextEditor'
import VersionHistory from '@/components/VersionHistory'

export default {
  name: 'DocumentPage',
  components: {
    RichTextEditor,
    VersionHistory
  },
  data() {
    return {
      content: '# 欢迎使用文档编辑器\n\n开始编写您的内容...',
      documentId: 'doc_' + Date.now(),
      showVersions: false,
      currentUser: {
        id: 1,
        name: '当前用户',
        avatar: ''
      }
    }
  },
  methods: {
    handleAutoSave(content) {
      console.log('自动保存:', content)
      // 这里可以调用API保存到服务器
    },
    
    handleVersionRestored(version) {
      this.content = version.content
      this.showVersions = false
      this.$message.success('版本恢复成功')
    }
  }
}
</script>
```

### 步骤2：添加协作功能

```vue
<template>
  <div class="collaborative-editor">
    <!-- 协作状态栏 -->
    <div class="collaboration-bar">
      <el-tag v-if="isCollaborating" type="success">
        <i class="el-icon-connection"></i> 协作中
      </el-tag>
      <span class="collaborator-count">{{ collaborators.length }} 人在线</span>
    </div>
    
    <!-- 编辑器 -->
    <RichTextEditor
      v-model="content"
      :document-id="documentId"
      :enable-collaboration="true"
    />
    
    <!-- 评论系统 -->
    <CommentSystem
      :document-id="documentId"
      :connection-id="connectionId"
      :collaborators="collaborators"
      :current-user="currentUser"
    />
  </div>
</template>

<script>
import RichTextEditor from '@/components/RichTextEditor'
import CommentSystem from '@/components/CommentSystem'
import enhancedCollaborationService from '@/utils/enhancedCollaboration'

export default {
  name: 'CollaborativeEditor',
  components: {
    RichTextEditor,
    CommentSystem
  },
  data() {
    return {
      content: '',
      documentId: 'doc_123',
      connectionId: null,
      isCollaborating: false,
      collaborators: [],
      currentUser: {
        id: 1,
        nickname: '当前用户',
        avatar: ''
      }
    }
  },
  mounted() {
    this.initCollaboration()
  },
  beforeDestroy() {
    this.disconnectCollaboration()
  },
  methods: {
    initCollaboration() {
      // 连接协作服务
      this.connectionId = enhancedCollaborationService.connect(
        this.documentId,
        this.currentUser
      )
      
      // 监听协作事件
      enhancedCollaborationService.on(this.connectionId, 'userJoined', this.handleUserJoined)
      enhancedCollaborationService.on(this.connectionId, 'userLeft', this.handleUserLeft)
      
      this.isCollaborating = true
      this.updateCollaborators()
    },
    
    disconnectCollaboration() {
      if (this.connectionId) {
        enhancedCollaborationService.disconnect(this.connectionId)
      }
    },
    
    updateCollaborators() {
      this.collaborators = enhancedCollaborationService.getCollaborators(this.documentId)
    },
    
    handleUserJoined(userInfo) {
      this.updateCollaborators()
      this.$message.info(`${userInfo.nickname} 加入了协作`)
    },
    
    handleUserLeft(userInfo) {
      this.updateCollaborators()
      this.$message.info(`${userInfo.nickname} 离开了协作`)
    }
  }
}
</script>

<style scoped>
.collaboration-bar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

.collaborator-count {
  font-size: 12px;
  color: #67c23a;
}
</style>
```

### 步骤3：添加文件管理

```vue
<template>
  <div class="workspace">
    <!-- 文件管理器 -->
    <EnhancedFileManager
      :workspace-id="workspaceId"
      :workspace-name="workspaceName"
    />
  </div>
</template>

<script>
import EnhancedFileManager from '@/components/EnhancedFileManager'

export default {
  name: 'Workspace',
  components: {
    EnhancedFileManager
  },
  data() {
    return {
      workspaceId: 'workspace_123',
      workspaceName: '我的工作空间'
    }
  }
}
</script>
```

## 🛠️ 服务配置

### 1. 版本控制服务

```javascript
// utils/documentService.js
import versionControlService from '@/utils/versionControl'

export class DocumentService {
  constructor(documentId, author) {
    this.documentId = documentId
    this.author = author
    this.init()
  }
  
  init() {
    // 初始化版本控制
    versionControlService.initDocument(this.documentId, '', this.author)
  }
  
  // 保存文档
  async saveDocument(content, title = '', message = '') {
    try {
      const version = versionControlService.createVersion(this.documentId, {
        content,
        title: title || `版本 ${Date.now()}`,
        message: message || '自动保存',
        author: this.author
      })
      
      console.log('文档保存成功:', version)
      return version
    } catch (error) {
      console.error('文档保存失败:', error)
      throw error
    }
  }
  
  // 获取版本列表
  getVersions(options = {}) {
    return versionControlService.getVersions(this.documentId, {
      sortBy: 'date',
      limit: 10,
      ...options
    })
  }
  
  // 恢复版本
  async restoreVersion(versionId) {
    try {
      const version = versionControlService.rollbackToVersion(
        this.documentId,
        versionId,
        this.author
      )
      
      console.log('版本恢复成功:', version)
      return version
    } catch (error) {
      console.error('版本恢复失败:', error)
      throw error
    }
  }
}

// 使用示例
const documentService = new DocumentService('doc_123', {
  id: 1,
  name: '张三'
})

// 保存文档
await documentService.saveDocument('文档内容', '文档标题')

// 获取版本列表
const versions = documentService.getVersions()
```

### 2. 协作服务配置

```javascript
// utils/collaborationManager.js
import enhancedCollaborationService from '@/utils/enhancedCollaboration'

export class CollaborationManager {
  constructor(documentId, userInfo) {
    this.documentId = documentId
    this.userInfo = userInfo
    this.connectionId = null
    this.eventHandlers = new Map()
  }
  
  // 开始协作
  startCollaboration() {
    this.connectionId = enhancedCollaborationService.connect(
      this.documentId,
      this.userInfo
    )
    
    // 设置事件监听
    this.setupEventListeners()
    
    return this.connectionId
  }
  
  // 停止协作
  stopCollaboration() {
    if (this.connectionId) {
      enhancedCollaborationService.disconnect(this.connectionId)
      this.connectionId = null
    }
  }
  
  // 发送内容变更
  sendContentChange(content) {
    if (this.connectionId) {
      enhancedCollaborationService.sendOperation(this.connectionId, {
        type: 'content-change',
        content,
        timestamp: Date.now()
      })
    }
  }
  
  // 更新光标位置
  updateCursor(position, selection = null) {
    if (this.connectionId) {
      enhancedCollaborationService.updateCursor(this.connectionId, {
        position,
        selection
      })
    }
  }
  
  // 添加评论
  addComment(content, position) {
    if (this.connectionId) {
      return enhancedCollaborationService.addComment(this.connectionId, {
        content,
        position
      })
    }
  }
  
  // 注册事件处理器
  on(event, handler) {
    this.eventHandlers.set(event, handler)
    if (this.connectionId) {
      enhancedCollaborationService.on(this.connectionId, event, handler)
    }
  }
  
  // 设置事件监听
  setupEventListeners() {
    this.eventHandlers.forEach((handler, event) => {
      enhancedCollaborationService.on(this.connectionId, event, handler)
    })
  }
  
  // 获取协作者列表
  getCollaborators() {
    return enhancedCollaborationService.getCollaborators(this.documentId)
  }
  
  // 获取光标位置
  getCursors() {
    return enhancedCollaborationService.getCursors(this.documentId)
  }
}

// 使用示例
const collaborationManager = new CollaborationManager('doc_123', {
  id: 1,
  nickname: '张三',
  avatar: ''
})

// 开始协作
collaborationManager.startCollaboration()

// 监听事件
collaborationManager.on('operation', (operation) => {
  console.log('收到操作:', operation)
})

collaborationManager.on('userJoined', (userInfo) => {
  console.log('用户加入:', userInfo)
})
```

## 📱 移动端适配

### 响应式设计

```vue
<template>
  <div class="mobile-editor">
    <!-- 移动端工具栏 -->
    <div class="mobile-toolbar">
      <el-button-group size="small">
        <el-button icon="el-icon-bold" @click="formatText('bold')"></el-button>
        <el-button icon="el-icon-italic" @click="formatText('italic')"></el-button>
        <el-button icon="el-icon-more" @click="showMoreTools = true"></el-button>
      </el-button-group>
    </div>
    
    <!-- 编辑器 -->
    <RichTextEditor
      v-model="content"
      :document-id="documentId"
      class="mobile-rich-editor"
    />
    
    <!-- 更多工具抽屉 -->
    <el-drawer
      title="编辑工具"
      :visible.sync="showMoreTools"
      direction="btt"
      size="40%"
    >
      <div class="mobile-tools">
        <el-button @click="insertMarkdown('# ', '')">标题</el-button>
        <el-button @click="insertMarkdown('- ', '')">列表</el-button>
        <el-button @click="insertMarkdown('> ', '')">引用</el-button>
        <el-button @click="insertMarkdown('```\n', '\n```')">代码</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<style scoped>
.mobile-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.mobile-toolbar {
  padding: 8px;
  border-bottom: 1px solid #ebeef5;
  background: #fff;
  position: sticky;
  top: 0;
  z-index: 100;
}

.mobile-rich-editor {
  flex: 1;
  overflow: hidden;
}

.mobile-tools {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  padding: 16px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .mobile-toolbar {
    padding: 12px;
  }
  
  .mobile-tools {
    grid-template-columns: 1fr;
  }
}
</style>
```

## 🔍 调试技巧

### 1. 开启调试模式

```javascript
// 在浏览器控制台中执行
localStorage.setItem('debug', 'collaboration,version,comment')

// 或在代码中设置
if (process.env.NODE_ENV === 'development') {
  window.DEBUG = {
    collaboration: true,
    version: true,
    comment: true
  }
}
```

### 2. 监控协作状态

```javascript
// 创建调试面板
const createDebugPanel = () => {
  const panel = document.createElement('div')
  panel.id = 'debug-panel'
  panel.style.cssText = `
    position: fixed;
    top: 10px;
    right: 10px;
    width: 300px;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 10px;
    border-radius: 5px;
    font-size: 12px;
    z-index: 9999;
  `
  document.body.appendChild(panel)
  
  return panel
}

// 更新调试信息
const updateDebugInfo = (collaborationManager, documentService) => {
  const panel = document.getElementById('debug-panel')
  if (panel) {
    panel.innerHTML = `
      <div>协作者: ${collaborationManager.getCollaborators().length}</div>
      <div>版本数: ${documentService.getVersions().length}</div>
      <div>连接状态: ${collaborationManager.connectionId ? '已连接' : '未连接'}</div>
      <div>最后更新: ${new Date().toLocaleTimeString()}</div>
    `
  }
}

// 定期更新
setInterval(() => {
  updateDebugInfo(collaborationManager, documentService)
}, 1000)
```

## 🚨 常见问题

### Q1: 编辑器无法加载？
**A**: 检查是否正确安装了 `quill` 和 `marked` 依赖，并确保样式文件已正确导入。

### Q2: 协作功能不工作？
**A**: 确认 WebSocket 连接正常，检查用户权限和网络连接。

### Q3: 版本创建失败？
**A**: 检查文档内容是否为空，确认用户信息完整。

### Q4: 移动端显示异常？
**A**: 添加 viewport meta 标签，使用响应式CSS。

## 📚 更多资源

- [完整使用指南](./文档管理功能使用指南.md)
- [API接口文档](./API接口文档.md)
- [组件API参考](./组件API参考.md)
- [最佳实践](./最佳实践.md)

---

🎉 恭喜！您已经成功集成了文档管理功能。如有问题，请查看详细文档或联系开发团队。
