<template>
  <div class="collaborative-cursors">
    <!-- 其他用户的光标 -->
    <div
      v-for="cursor in otherCursors"
      :key="cursor.connectionId"
      class="cursor-indicator"
      :style="getCursorStyle(cursor)"
    >
      <!-- 光标线 -->
      <div class="cursor-line" :style="{ backgroundColor: cursor.color }"></div>
      
      <!-- 用户标签 -->
      <div class="cursor-label" :style="{ backgroundColor: cursor.color }">
        <span class="user-name">{{ cursor.userInfo.nickname }}</span>
      </div>
      
      <!-- 选择区域 -->
      <div
        v-if="cursor.cursor.selection"
        class="cursor-selection"
        :style="getSelectionStyle(cursor)"
      ></div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CollaborativeCursor',
  props: {
    cursors: {
      type: Map,
      default: () => new Map()
    },
    currentConnectionId: {
      type: String,
      default: ''
    },
    editorElement: {
      type: HTMLElement,
      default: null
    }
  },
  data() {
    return {
      userColors: new Map(),
      availableColors: [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9'
      ],
      colorIndex: 0
    }
  },
  computed: {
    otherCursors() {
      const cursors = []
      if (!this.cursors) return cursors
      
      this.cursors.forEach((cursorData, connectionId) => {
        if (connectionId !== this.currentConnectionId) {
          cursors.push({
            connectionId,
            ...cursorData,
            color: this.getUserColor(connectionId)
          })
        }
      })
      
      return cursors
    }
  },
  methods: {
    /**
     * 获取用户颜色
     */
    getUserColor(connectionId) {
      if (!this.userColors.has(connectionId)) {
        const color = this.availableColors[this.colorIndex % this.availableColors.length]
        this.userColors.set(connectionId, color)
        this.colorIndex++
      }
      return this.userColors.get(connectionId)
    },

    /**
     * 获取光标样式
     */
    getCursorStyle(cursor) {
      if (!this.editorElement || !cursor.cursor) return {}
      
      const position = this.getPositionFromOffset(cursor.cursor.position)
      
      return {
        position: 'absolute',
        left: `${position.left}px`,
        top: `${position.top}px`,
        zIndex: 1000
      }
    },

    /**
     * 获取选择区域样式
     */
    getSelectionStyle(cursor) {
      if (!this.editorElement || !cursor.cursor.selection) return {}
      
      const startPos = this.getPositionFromOffset(cursor.cursor.selection.start)
      const endPos = this.getPositionFromOffset(cursor.cursor.selection.end)
      
      return {
        position: 'absolute',
        left: `${startPos.left}px`,
        top: `${startPos.top}px`,
        width: `${endPos.left - startPos.left}px`,
        height: `${endPos.top - startPos.top + 20}px`,
        backgroundColor: cursor.color,
        opacity: 0.2,
        zIndex: 999
      }
    },

    /**
     * 根据文本偏移量获取屏幕位置
     */
    getPositionFromOffset(offset) {
      if (!this.editorElement) return { left: 0, top: 0 }
      
      // 对于textarea元素
      if (this.editorElement.tagName === 'TEXTAREA') {
        return this.getTextareaPosition(offset)
      }
      
      // 对于富文本编辑器
      if (this.editorElement.contentEditable === 'true') {
        return this.getContentEditablePosition(offset)
      }
      
      return { left: 0, top: 0 }
    },

    /**
     * 获取textarea中的位置
     */
    getTextareaPosition(offset) {
      const textarea = this.editorElement
      const text = textarea.value
      
      // 创建临时元素来计算位置
      const temp = document.createElement('div')
      temp.style.position = 'absolute'
      temp.style.visibility = 'hidden'
      temp.style.whiteSpace = 'pre-wrap'
      temp.style.wordWrap = 'break-word'
      temp.style.font = window.getComputedStyle(textarea).font
      temp.style.width = textarea.clientWidth + 'px'
      temp.style.padding = window.getComputedStyle(textarea).padding
      temp.style.border = window.getComputedStyle(textarea).border
      
      document.body.appendChild(temp)
      
      // 添加光标前的文本
      temp.textContent = text.substring(0, offset)
      
      // 添加一个span来标记光标位置
      const cursor = document.createElement('span')
      cursor.textContent = '|'
      temp.appendChild(cursor)
      
      const rect = textarea.getBoundingClientRect()
      const cursorRect = cursor.getBoundingClientRect()
      
      const position = {
        left: cursorRect.left - rect.left + textarea.scrollLeft,
        top: cursorRect.top - rect.top + textarea.scrollTop
      }
      
      document.body.removeChild(temp)
      
      return position
    },

    /**
     * 获取富文本编辑器中的位置
     */
    getContentEditablePosition(offset) {
      const editor = this.editorElement
      const range = document.createRange()
      const selection = window.getSelection()
      
      try {
        // 创建文本节点迭代器
        const walker = document.createTreeWalker(
          editor,
          NodeFilter.SHOW_TEXT,
          null,
          false
        )
        
        let currentOffset = 0
        let node = walker.nextNode()
        
        while (node) {
          const nodeLength = node.textContent.length
          
          if (currentOffset + nodeLength >= offset) {
            // 找到目标节点
            range.setStart(node, offset - currentOffset)
            range.setEnd(node, offset - currentOffset)
            break
          }
          
          currentOffset += nodeLength
          node = walker.nextNode()
        }
        
        const rect = range.getBoundingClientRect()
        const editorRect = editor.getBoundingClientRect()
        
        return {
          left: rect.left - editorRect.left + editor.scrollLeft,
          top: rect.top - editorRect.top + editor.scrollTop
        }
      } catch (error) {
        console.error('Error getting position:', error)
        return { left: 0, top: 0 }
      }
    },

    /**
     * 更新光标位置
     */
    updateCursors() {
      this.$forceUpdate()
    }
  },
  watch: {
    cursors: {
      deep: true,
      handler() {
        this.updateCursors()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.collaborative-cursors {
  position: relative;
  pointer-events: none;
}

.cursor-indicator {
  position: absolute;
  pointer-events: none;
}

.cursor-line {
  width: 2px;
  height: 20px;
  position: relative;
  animation: blink 1s infinite;
}

.cursor-label {
  position: absolute;
  top: -25px;
  left: -5px;
  padding: 2px 6px;
  border-radius: 3px;
  color: white;
  font-size: 12px;
  white-space: nowrap;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.user-name {
  font-weight: 500;
}

.cursor-selection {
  position: absolute;
  pointer-events: none;
  border-radius: 2px;
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0.3;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cursor-label {
    font-size: 10px;
    padding: 1px 4px;
  }
  
  .cursor-line {
    width: 1px;
    height: 16px;
  }
}
</style>
