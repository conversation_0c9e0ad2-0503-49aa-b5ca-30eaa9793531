import request from '@/utils/request'

// 查询工作空间文件列表
export function listWorkspaceFiles (workspaceId, query) {
  return request({
    url: '/workspace/' + workspaceId + '/files',
    method: 'get',
    params: query
  })
}

// 查询文件详细信息
export function getWorkspaceFile (workspaceId, fileId) {
  return request({
    url: '/workspace/' + workspaceId + '/files/' + fileId,
    method: 'get'
  })
}

// 上传文件到工作空间
export function uploadWorkspaceFile (workspaceId, data) {
  return request({
    url: '/workspace/' + workspaceId + '/files/upload',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: data
  })
}

// 删除工作空间文件
export function deleteWorkspaceFile (workspaceId, fileId) {
  return request({
    url: '/workspace/' + workspaceId + '/files/' + fileId,
    method: 'delete'
  })
}

// 重命名文件
export function renameWorkspaceFile (workspaceId, fileId, newName) {
  return request({
    url: '/workspace/' + workspaceId + '/files/' + fileId + '/rename',
    method: 'put',
    data: { newName }
  })
}

// 移动文件到文件夹
export function moveWorkspaceFile (workspaceId, fileId, folderId) {
  return request({
    url: '/workspace/' + workspaceId + '/files/' + fileId + '/move',
    method: 'put',
    data: { folderId }
  })
}

// 复制文件
export function copyWorkspaceFile (workspaceId, fileId, targetFolderId) {
  return request({
    url: '/workspace/' + workspaceId + '/files/' + fileId + '/copy',
    method: 'post',
    data: { targetFolderId }
  })
}

// 下载文件
export function downloadWorkspaceFile (workspaceId, fileId) {
  return request({
    url: '/workspace/' + workspaceId + '/files/' + fileId + '/download',
    method: 'get',
    responseType: 'blob'
  })
}

// 创建文件夹
export function createWorkspaceFolder (workspaceId, data) {
  return request({
    url: '/workspace/' + workspaceId + '/folders',
    method: 'post',
    data: data
  })
}

// 删除文件夹
export function deleteWorkspaceFolder (workspaceId, folderId) {
  return request({
    url: '/workspace/' + workspaceId + '/folders/' + folderId,
    method: 'delete'
  })
}

// 重命名文件夹
export function renameWorkspaceFolder (workspaceId, folderId, newName) {
  return request({
    url: '/workspace/' + workspaceId + '/folders/' + folderId + '/rename',
    method: 'put',
    data: { newName }
  })
}

// 获取文件分享链接
export function shareWorkspaceFile (workspaceId, fileId, expireTime) {
  return request({
    url: '/workspace/' + workspaceId + '/files/' + fileId + '/share',
    method: 'post',
    data: { expireTime }
  })
}

// 取消文件分享
export function unshareWorkspaceFile (workspaceId, fileId) {
  return request({
    url: '/workspace/' + workspaceId + '/files/' + fileId + '/unshare',
    method: 'delete'
  })
}

// 批量删除文件
export function batchDeleteWorkspaceFiles (workspaceId, fileIds) {
  return request({
    url: '/workspace/' + workspaceId + '/files/batch-delete',
    method: 'delete',
    data: { fileIds }
  })
}

// 获取文件存储统计
export function getWorkspaceStorageStats (workspaceId) {
  return request({
    url: '/workspace/' + workspaceId + '/storage/stats',
    method: 'get'
  })
}

// 创建文档
export function createDocument (workspaceId, documentData) {
  return request({
    url: '/workspace/' + workspaceId + '/documents',
    method: 'post',
    data: documentData
  })
}

// 获取文档详情
export function getDocument (workspaceId, documentId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId,
    method: 'get'
  })
}

// 更新文档
export function updateDocument (workspaceId, documentId, documentData) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId,
    method: 'put',
    data: documentData
  })
}

// 删除文档
export function deleteDocument (workspaceId, documentId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId,
    method: 'delete'
  })
}

// 获取文档列表
export function getDocuments (workspaceId, query) {
  return request({
    url: '/workspace/' + workspaceId + '/documents',
    method: 'get',
    params: query
  })
}

// 搜索文档
export function searchDocuments (workspaceId, searchQuery) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/search',
    method: 'get',
    params: searchQuery
  })
}

// 复制文档
export function duplicateDocument (workspaceId, documentId, documentData) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/duplicate',
    method: 'post',
    data: documentData
  })
}

// 移动文档
export function moveDocument (workspaceId, documentId, targetFolderId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/move',
    method: 'post',
    data: { targetFolderId }
  })
}

// 获取文档权限
export function getDocumentPermissions (workspaceId, documentId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/permissions',
    method: 'get'
  })
}

// 设置文档权限
export function setDocumentPermissions (workspaceId, documentId, permissions) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/permissions',
    method: 'put',
    data: permissions
  })
}

// 分享文档
export function shareDocument (workspaceId, documentId, shareData) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/share',
    method: 'post',
    data: shareData
  })
}

// 取消分享文档
export function unshareDocument (workspaceId, documentId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/unshare',
    method: 'delete'
  })
}

// 获取文档分享信息
export function getDocumentShareInfo (workspaceId, documentId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/share',
    method: 'get'
  })
}

// 导出文档
export function exportDocument (workspaceId, documentId, format) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/export',
    method: 'get',
    params: { format },
    responseType: 'blob'
  })
}

// 导入文档
export function importDocument (workspaceId, documentFile) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/import',
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: documentFile
  })
}

// 获取文档统计信息
export function getDocumentStats (workspaceId, documentId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/stats',
    method: 'get'
  })
}

// 收藏文档
export function favoriteDocument (workspaceId, documentId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/favorite',
    method: 'post'
  })
}

// 取消收藏文档
export function unfavoriteDocument (workspaceId, documentId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/unfavorite',
    method: 'post'
  })
}

// 获取收藏的文档
export function getFavoriteDocuments (workspaceId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/favorites',
    method: 'get'
  })
}

// 获取最近访问的文档
export function getRecentDocuments (workspaceId, limit = 10) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/recent',
    method: 'get',
    params: { limit }
  })
}

// 标记文档为已读
export function markDocumentAsRead (workspaceId, documentId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/read',
    method: 'post'
  })
}

// 获取文档阅读状态
export function getDocumentReadStatus (workspaceId, documentId) {
  return request({
    url: '/workspace/' + workspaceId + '/documents/' + documentId + '/read-status',
    method: 'get'
  })
}
