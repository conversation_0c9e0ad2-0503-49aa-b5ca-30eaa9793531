/**
 * 协作服务 - 模拟实时协作功能
 * 在实际项目中，这里应该使用WebSocket或Socket.IO连接到后端服务
 */

class CollaborationService {
  constructor() {
    this.connections = new Map() // 存储连接信息
    this.documents = new Map() // 存储文档状态
    this.users = new Map() // 存储用户信息
    this.eventListeners = new Map() // 事件监听器
  }

  /**
   * 连接到协作服务
   * @param {string} documentId 文档ID
   * @param {object} userInfo 用户信息
   */
  connect(documentId, userInfo) {
    const connectionId = `${documentId}_${userInfo.id}_${Date.now()}`
    
    // 存储连接信息
    this.connections.set(connectionId, {
      documentId,
      userInfo,
      isActive: true,
      lastActivity: Date.now()
    })

    // 初始化文档状态
    if (!this.documents.has(documentId)) {
      this.documents.set(documentId, {
        content: '',
        version: 0,
        collaborators: new Set(),
        operations: []
      })
    }

    // 添加协作者
    const document = this.documents.get(documentId)
    document.collaborators.add(userInfo.id)

    // 通知其他用户有新用户加入
    this.broadcastUserJoined(documentId, userInfo, connectionId)

    return connectionId
  }

  /**
   * 断开连接
   * @param {string} connectionId 连接ID
   */
  disconnect(connectionId) {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const { documentId, userInfo } = connection
    
    // 移除连接
    this.connections.delete(connectionId)

    // 从协作者列表中移除
    const document = this.documents.get(documentId)
    if (document) {
      document.collaborators.delete(userInfo.id)
      
      // 通知其他用户有用户离开
      this.broadcastUserLeft(documentId, userInfo, connectionId)
    }
  }

  /**
   * 发送内容变更
   * @param {string} connectionId 连接ID
   * @param {object} operation 操作信息
   */
  sendOperation(connectionId, operation) {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const { documentId } = connection
    const document = this.documents.get(documentId)
    if (!document) return

    // 更新文档版本
    document.version++
    document.operations.push({
      ...operation,
      version: document.version,
      timestamp: Date.now(),
      userId: connection.userInfo.id
    })

    // 应用操作到文档内容
    this.applyOperation(documentId, operation)

    // 广播操作到其他协作者
    this.broadcastOperation(documentId, operation, connectionId)
  }

  /**
   * 应用操作到文档内容
   * @param {string} documentId 文档ID
   * @param {object} operation 操作信息
   */
  applyOperation(documentId, operation) {
    const document = this.documents.get(documentId)
    if (!document) return

    switch (operation.type) {
      case 'insert':
        document.content = 
          document.content.slice(0, operation.position) + 
          operation.content + 
          document.content.slice(operation.position)
        break
      case 'delete':
        document.content = 
          document.content.slice(0, operation.position) + 
          document.content.slice(operation.position + operation.length)
        break
      case 'replace':
        document.content = operation.content
        break
    }
  }

  /**
   * 广播操作到其他协作者
   * @param {string} documentId 文档ID
   * @param {object} operation 操作信息
   * @param {string} excludeConnectionId 排除的连接ID
   */
  broadcastOperation(documentId, operation, excludeConnectionId) {
    this.connections.forEach((connection, connectionId) => {
      if (connection.documentId === documentId && connectionId !== excludeConnectionId) {
        this.emitEvent(connectionId, 'operation', operation)
      }
    })
  }

  /**
   * 广播用户加入事件
   * @param {string} documentId 文档ID
   * @param {object} userInfo 用户信息
   * @param {string} excludeConnectionId 排除的连接ID
   */
  broadcastUserJoined(documentId, userInfo, excludeConnectionId) {
    this.connections.forEach((connection, connectionId) => {
      if (connection.documentId === documentId && connectionId !== excludeConnectionId) {
        this.emitEvent(connectionId, 'userJoined', userInfo)
      }
    })
  }

  /**
   * 广播用户离开事件
   * @param {string} documentId 文档ID
   * @param {object} userInfo 用户信息
   * @param {string} excludeConnectionId 排除的连接ID
   */
  broadcastUserLeft(documentId, userInfo, excludeConnectionId) {
    this.connections.forEach((connection, connectionId) => {
      if (connection.documentId === documentId && connectionId !== excludeConnectionId) {
        this.emitEvent(connectionId, 'userLeft', userInfo)
      }
    })
  }

  /**
   * 获取文档的协作者列表
   * @param {string} documentId 文档ID
   * @returns {Array} 协作者列表
   */
  getCollaborators(documentId) {
    const collaborators = []
    this.connections.forEach((connection) => {
      if (connection.documentId === documentId && connection.isActive) {
        collaborators.push({
          ...connection.userInfo,
          isActive: true,
          isEditing: Date.now() - connection.lastActivity < 30000 // 30秒内有活动算作正在编辑
        })
      }
    })
    return collaborators
  }

  /**
   * 获取文档内容
   * @param {string} documentId 文档ID
   * @returns {object} 文档信息
   */
  getDocument(documentId) {
    return this.documents.get(documentId) || null
  }

  /**
   * 设置文档内容
   * @param {string} documentId 文档ID
   * @param {string} content 文档内容
   */
  setDocumentContent(documentId, content) {
    if (!this.documents.has(documentId)) {
      this.documents.set(documentId, {
        content,
        version: 0,
        collaborators: new Set(),
        operations: []
      })
    } else {
      const document = this.documents.get(documentId)
      document.content = content
      document.version++
    }
  }

  /**
   * 监听事件
   * @param {string} connectionId 连接ID
   * @param {string} event 事件名称
   * @param {function} callback 回调函数
   */
  on(connectionId, event, callback) {
    if (!this.eventListeners.has(connectionId)) {
      this.eventListeners.set(connectionId, new Map())
    }
    
    const listeners = this.eventListeners.get(connectionId)
    if (!listeners.has(event)) {
      listeners.set(event, [])
    }
    
    listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听器
   * @param {string} connectionId 连接ID
   * @param {string} event 事件名称
   * @param {function} callback 回调函数
   */
  off(connectionId, event, callback) {
    const listeners = this.eventListeners.get(connectionId)
    if (!listeners || !listeners.has(event)) return

    const eventListeners = listeners.get(event)
    const index = eventListeners.indexOf(callback)
    if (index > -1) {
      eventListeners.splice(index, 1)
    }
  }

  /**
   * 触发事件
   * @param {string} connectionId 连接ID
   * @param {string} event 事件名称
   * @param {any} data 事件数据
   */
  emitEvent(connectionId, event, data) {
    const listeners = this.eventListeners.get(connectionId)
    if (!listeners || !listeners.has(event)) return

    const eventListeners = listeners.get(event)
    eventListeners.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('Event listener error:', error)
      }
    })
  }

  /**
   * 更新用户活动时间
   * @param {string} connectionId 连接ID
   */
  updateActivity(connectionId) {
    const connection = this.connections.get(connectionId)
    if (connection) {
      connection.lastActivity = Date.now()
    }
  }

  /**
   * 清理过期连接
   */
  cleanup() {
    const now = Date.now()
    const timeout = 5 * 60 * 1000 // 5分钟超时

    this.connections.forEach((connection, connectionId) => {
      if (now - connection.lastActivity > timeout) {
        this.disconnect(connectionId)
      }
    })
  }
}

// 创建全局协作服务实例
const collaborationService = new CollaborationService()

// 定期清理过期连接
setInterval(() => {
  collaborationService.cleanup()
}, 60000) // 每分钟清理一次

export default collaborationService
