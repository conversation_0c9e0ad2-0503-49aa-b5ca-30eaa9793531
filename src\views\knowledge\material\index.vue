<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 左侧文献树 -->
      <el-col :span="literatureTreeSpan" class="literature-tree-col">
        <el-card class="literature-tree-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-document"></i> 文献列表</span>
            <el-button size="mini" type="text" @click="refreshLiteratureTree">
              <i class="el-icon-refresh"></i>
            </el-button>
          </div>
          <div class="tree-container">
            <el-input v-model="literatureFilterText" placeholder="搜索文献" size="small" prefix-icon="el-icon-search"
              clearable class="tree-filter">
            </el-input>
            <el-tree ref="literatureTree" :data="literatureTreeData" :props="treeProps"
              :filter-node-method="filterLiteratureNode" node-key="id" :expand-on-click-node="false"
              :highlight-current="true" @node-click="handleLiteratureSelect" class="literature-tree">
              <span class="custom-tree-node" slot-scope="{ node, data }">
                <span class="node-label">
                  <i :class="getTreeNodeIcon(data)" class="node-icon"></i>
                  <span :title="node.label">{{ node.label }}</span>
                </span>
                <span class="node-count" v-if="data.materialCount">
                  ({{ data.materialCount }})
                </span>
              </span>
            </el-tree>
          </div>
        </el-card>
        <!-- 拖拽调整宽度的分隔线 -->
        <div class="resize-handle"></div>
      </el-col>

      <!-- 右侧原辅料列表 -->
      <el-col :span="24 - literatureTreeSpan">
        <div class="material-content">
          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/" class="breadcrumb-nav" v-if="selectedLiterature">
            <el-breadcrumb-item>文献查询</el-breadcrumb-item>
            <el-breadcrumb-item>{{ selectedLiterature.title }}</el-breadcrumb-item>
          </el-breadcrumb>

          <!-- 搜索表单 -->
          <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
            label-width="68px">
            <el-form-item label="名称" prop="name">
              <el-input v-model="queryParams.name" placeholder="请输入原辅料名称" clearable @keyup.enter.native="handleQuery" />
            </el-form-item>
            <el-form-item label="类别" prop="materialType">
              <el-select v-model="queryParams.materialType" placeholder="请选择类别" clearable>
                <el-option label="原料" value="raw" />
                <el-option label="辅料" value="auxiliary" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <!-- 操作按钮 -->
          <el-row :gutter="10" class="mb8">
            <el-col :span="1.5">
              <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
                v-hasPermi="['knowledge:material:add']">新增</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="info" plain icon="el-icon-upload2" size="mini" @click="handleImport"
                v-hasPermi="['knowledge:material:import']">导入</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
                v-hasPermi="['knowledge:material:edit']">修改</el-button>
            </el-col>
            <el-col :span="1.5">
              <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                @click="handleDelete" v-hasPermi="['knowledge:material:remove']">删除</el-button>
            </el-col>
            <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
          </el-row>

          <!-- 数据表格 -->
          <el-table v-loading="loading" :data="materialList" @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="名称" align="center" prop="name" :show-overflow-tooltip="true" />
            <el-table-column label="类别" align="center" prop="materialType">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.materialType === 'raw'" type="primary">原料</el-tag>
                <el-tag v-else-if="scope.row.materialType === 'auxiliary'" type="success">辅料</el-tag>
                <span v-else>{{ scope.row.materialType }}</span>
              </template>
            </el-table-column>
            <el-table-column label="类型" align="center" prop="type" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.type" :type="getTypeTagType(scope.row.type)">
                  {{ scope.row.type }}
                </el-tag>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column label="简介描述" align="center" prop="description" :show-overflow-tooltip="true" />
            <!-- <el-table-column label="性状" align="center" prop="trait" :show-overflow-tooltip="true" /> -->
            <!-- <el-table-column label="分子式" align="center" prop="molecularFormula" />
            <el-table-column label="分子质量" align="center" prop="molecularWeight" /> -->
            <el-table-column label="二维码" align="center" width="100">
              <template slot-scope="scope">
                <div class="qrcode-container">
                  <canvas :ref="`qrcode-${scope.row.id}`" class="qrcode-canvas" @click="showQRCodeDialog(scope.row)"
                    title="点击查看大图"></canvas>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
              <template slot-scope="scope">
                <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(scope.row)"
                  v-hasPermi="['knowledge:material:query']">详情</el-button>
                <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['knowledge:material:edit']">修改</el-button>
                <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['knowledge:material:remove']">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize" @pagination="getList" />
        </div>
      </el-col>
    </el-row>

    <!-- 添加或修改原辅料对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="类别" prop="materialType">
              <el-select v-model="form.materialType" placeholder="请选择类别">
                <el-option label="原料" value="raw" />
                <el-option label="辅料" value="auxiliary" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="关联文献" prop="literatureId">
              <el-select v-model="form.literatureId" placeholder="请选择关联文献" clearable filterable>
                <el-option v-for="item in literatureOptions" :key="item.id" :label="item.name" :value="item.id">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="名称" prop="name">
              <el-input v-model="form.name" placeholder="请输入原辅料名称" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="分子式" prop="molecularFormula">
              <el-input v-model="form.molecularFormula" placeholder="请输入分子式" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="分子质量" prop="molecularWeight">
              <el-input v-model="form.molecularWeight" placeholder="请输入分子质量" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="简介描述" prop="description">
              <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入简介描述" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 原辅料导入对话框 -->
    <el-dialog title="原辅料导入" :visible.sync="importDialog.open" width="700px" append-to-body class="import-dialog"
      :close-on-click-modal="false">
      <el-form ref="importForm" :model="importDialog.form" :rules="importDialog.rules" label-width="100px">
        <el-form-item label="关联文献" prop="literatureId">
          <el-select v-model="importDialog.form.literatureId" placeholder="请选择关联文献" clearable filterable
            style="width: 100%">
            <el-option v-for="item in literatureOptions" :key="item.id" :label="item.name" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="导入内容" prop="content">
          <el-input v-model="importDialog.form.content" type="textarea" :rows="12"
            placeholder="请输入原辅料信息，支持批量导入，每行一个原辅料名称或详细信息" maxlength="5000" show-word-limit class="import-textarea" />
        </el-form-item>
        <el-form-item>
          <div class="import-tips">
            <div class="tips-header">
              <i class="el-icon-info-filled"></i>
              <span>导入说明</span>
            </div>
            <div class="tips-content">
              <div class="tip-item">
                <i class="el-icon-check"></i>
                <span>支持纯文本格式导入</span>
              </div>
              <div class="tip-item">
                <i class="el-icon-check"></i>
                <span>每行一个原辅料信息</span>
              </div>
              <div class="tip-item">
                <i class="el-icon-check"></i>
                <span>系统将自动解析并创建原辅料记录</span>
              </div>
              <div class="tip-item">
                <i class="el-icon-check"></i>
                <span>建议先选择关联文献</span>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelImport" class="cancel-btn">取 消</el-button>
        <el-button type="primary" @click="submitImport" :loading="importDialog.loading" class="submit-btn">
          <i class="el-icon-upload2" v-if="!importDialog.loading"></i>
          确 定
        </el-button>
      </div>
    </el-dialog>

    <!-- 二维码预览对话框 -->
    <el-dialog title="二维码预览" :visible.sync="qrcodeDialog.visible" width="400px" append-to-body>
      <div class="qrcode-preview">
        <div class="qrcode-info">
          <h4>{{ qrcodeDialog.materialName }}</h4>
          <p>扫描二维码查看详情</p>
        </div>
        <div class="qrcode-large">
          <canvas ref="qrcodeLarge" class="qrcode-canvas-large"></canvas>
        </div>
        <div class="qrcode-url">
          <el-input v-model="qrcodeDialog.url" readonly>
            <el-button slot="append" @click="copyUrl" icon="el-icon-document-copy">复制</el-button>
          </el-input>
        </div>
        <div class="qrcode-actions">
          <el-button type="primary" @click="printQRCode" icon="el-icon-printer">打印二维码</el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listMaterial, getMaterial, delMaterial, addMaterial, updateMaterial, importMaterial } from "@/api/knowledge/material"
import { listLiterature } from "@/api/knowledge/literature"
import cache from "@/plugins/cache"
import QRCode from 'qrcode'
import settings from '@/settings'

export default {
  name: "Material",
  data () {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 原辅料表格数据
      materialList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        materialType: null,
        literatureId: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        materialType: [
          { required: true, message: "类别不能为空", trigger: "change" }
        ],
        name: [
          { required: true, message: "名称不能为空", trigger: "blur" }
        ],
        description: [
          { required: true, message: "简介描述不能为空", trigger: "blur" }
        ]
      },
      // 文献树相关
      literatureTreeData: [],
      literatureFilterText: '',
      selectedLiterature: null,
      literatureOptions: [],
      treeProps: {
        children: 'children',
        label: 'title'
      },
      // 文献树的宽度
      literatureTreeSpan: 4,
      // 导入对话框相关
      importDialog: {
        open: false,
        loading: false,
        form: {
          literatureId: null,
          content: ''
        },
        rules: {
          content: [
            { required: true, message: "导入内容不能为空", trigger: "blur" },
            { min: 1, max: 5000, message: "内容长度在 1 到 5000 个字符", trigger: "blur" }
          ]
        }
      },
      // 缓存键名
      CACHE_KEY: {
        SELECTED_LITERATURE: 'material_selected_literature'
      },
      // 二维码对话框
      qrcodeDialog: {
        visible: false,
        materialName: '',
        url: ''
      }
    }
  },
  watch: {
    literatureFilterText (val) {
      this.$refs.literatureTree.filter(val)
    }
  },
  created () {
    this.initPage()
  },
  mounted () {
    this.initResizeHandle()
  },
  methods: {
    /** 初始化页面数据 */
    async initPage () {
      try {
        // 先获取文献选项
        await this.getLiteratureOptions()
        // 再构建文献树
        this.getLiteratureTree()
        // 恢复缓存的文献选择
        this.restoreSelectedLiterature()
      } catch (error) {
        console.error('页面初始化失败:', error)
        this.$message.error('页面数据加载失败')
      }
    },

    /** 查询原辅料列表 */
    getList () {
      this.loading = true

      // 构建查询参数
      const queryParams = { ...this.queryParams }

      // 如果选择了文献，添加文献ID参数
      if (this.selectedLiterature) {
        queryParams.literatureId = this.selectedLiterature.id
      }

      // 如果选择了类别，添加materialType参数
      if (this.queryParams.materialType) {
        queryParams.materialType = this.queryParams.materialType
      }

      listMaterial(queryParams).then(response => {
        this.materialList = response.rows || response.data || []
        this.total = response.total || this.materialList.length
        this.loading = false
        // 生成二维码
        this.generateAllQRCodes()
      }).catch(() => {
        this.loading = false
        this.$message.error('查询失败')
      })
    },

    /** 获取文献树 */
    getLiteratureTree () {
      if (this.literatureOptions.length === 0) {
        console.warn('文献选项为空，无法构建文献树')
        return
      }

      this.literatureTreeData = this.buildLiteratureTree(this.literatureOptions)

      // 在下一个tick中选择第一个文献，确保DOM已更新
      this.$nextTick(() => {
        this.selectFirstLiterature()
      })
    },

    /** 构建文献树结构 */
    buildLiteratureTree (literatureList) {
      const typeGroups = {
        'pharmacopoeia': { title: '药典', children: [], icon: 'el-icon-notebook-1' },
        'standard': { title: '标准', children: [], icon: 'el-icon-files' },
        'manual': { title: '手册', children: [], icon: 'el-icon-reading' },
        'other': { title: '其他', children: [], icon: 'el-icon-document' }
      }

      literatureList.forEach(item => {
        const typeGroup = typeGroups[item.type]
        if (typeGroup) {
          // 从remark中提取原辅料数量信息
          let materialCount = 0
          if (item.remark) {
            const match = item.remark.match(/原料数量:\s*(\d+),\s*辅料数量:\s*(\d+)/)
            if (match) {
              materialCount = parseInt(match[1]) + parseInt(match[2])
            }
          }

          typeGroup.children.push({
            id: item.id,
            title: item.name,
            author: item.author,
            version: item.version,
            type: item.type,
            isLiterature: true,
            materialCount: materialCount
          })
        }
      })

      return Object.keys(typeGroups)
        .filter(key => typeGroups[key].children.length > 0)
        .map(key => ({
          id: key,
          title: typeGroups[key].title,
          children: typeGroups[key].children,
          icon: typeGroups[key].icon,
          isGroup: true
        }))
    },

    /** 获取文献选项 */
    getLiteratureOptions () {
      return new Promise((resolve, reject) => {
        listLiterature({ pageNum: 1, pageSize: 1000, status: 1 }).then(response => {
          this.literatureOptions = response.rows || []
          resolve(response)
        }).catch(error => {
          reject(error)
        })
      })
    },

    /** 文献树节点点击 */
    handleLiteratureSelect (data) {
      if (data.isLiterature) {
        this.selectedLiterature = data
        this.queryParams.pageNum = 1
        // 缓存选中的文献
        this.cacheSelectedLiterature()
        this.getList()
      } else if (data.isGroup) {
        // 点击分组节点，只展开不查询
        const node = this.$refs.literatureTree.getNode(data.id)
        if (node) {
          node.expanded = !node.expanded
        }
      }
    },

    /** 刷新文献树 */
    async refreshLiteratureTree () {
      try {
        // 重新获取文献数据
        await this.getLiteratureOptions()
        // 重新构建文献树
        this.getLiteratureTree()
        this.$message.success('文献树刷新成功')
      } catch (error) {
        console.error('刷新文献树失败:', error)
        this.$message.error('刷新文献树失败')
      }
    },

    /** 文献树过滤 */
    filterLiteratureNode (value, data) {
      if (!value) return true
      return data.title.indexOf(value) !== -1
    },

    /** 获取树节点图标 */
    getTreeNodeIcon (data) {
      if (data.isGroup) {
        return data.icon
      } else {
        const iconMap = {
          'pharmacopoeia': 'el-icon-notebook-1',
          'standard': 'el-icon-files',
          'manual': 'el-icon-reading',
          'other': 'el-icon-document'
        }
        return iconMap[data.type] || 'el-icon-document'
      }
    },

    // 表单重置
    reset () {
      this.form = {
        id: null,
        name: null,
        description: null,
        trait: null,
        identification: null,
        inspection: null,
        assay: null,
        materialType: null, // 改为materialType
        storage: null,
        preparation: null,
        actionAndUse: null,
        usageAndDosage: null,
        adverseReaction: null,
        precautions: null,
        withdrawalPeriod: null,
        specification: null,
        molecularFormula: null,
        molecularWeight: null,
        structureImage: null,
        literatureId: this.selectedLiterature ? this.selectedLiterature.id : null
      }
      this.resetForm("form")
    },

    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParams.pageNum = 1
      this.getList()
    },

    /** 重置按钮操作 */
    resetQuery () {
      this.resetForm("queryForm")
      // 不清空选中的文献，保持面包屑导航
      // this.selectedLiterature = null
      this.handleQuery()
    },

    // 多选框选中数据
    handleSelectionChange (selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },

    /** 新增按钮操作 */
    handleAdd () {
      this.reset()
      this.open = true
      this.title = "添加原辅料"
    },

    /** 修改按钮操作 */
    handleUpdate (row) {
      this.reset()
      const id = row.id || this.ids
      getMaterial(id).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改原辅料"
      })
    },

    /** 详情按钮操作 */
    handleDetail (row) {
      this.$router.push('/knowledge/material/detail/' + row.id)
    },

    /** 提交按钮 */
    submitForm () {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateMaterial(this.form).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
              this.refreshLiteratureTree()
            })
          } else {
            addMaterial(this.form).then(response => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
              this.refreshLiteratureTree()
            })
          }
        }
      })
    },

    /** 删除按钮操作 */
    handleDelete (row) {
      const ids = row.id || this.ids
      this.$modal.confirm('是否确认删除原辅料编号为"' + ids + '"的数据项？').then(() => {
        return delMaterial(ids)
      }).then(() => {
        this.getList()
        this.refreshLiteratureTree()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => { })
    },

    // 取消按钮
    cancel () {
      this.open = false
      this.reset()
    },

    /** 默认选中第一个文献 */
    selectFirstLiterature () {
      // 如果有缓存的文献选择，优先使用缓存
      const cachedLiterature = cache.session.getJSON(this.CACHE_KEY.SELECTED_LITERATURE)
      if (cachedLiterature && this.isValidCachedLiterature(cachedLiterature)) {
        this.selectedLiterature = cachedLiterature
        this.$nextTick(() => {
          this.setTreeSelection(cachedLiterature.id)
          this.getList()
        })
        return
      }

      // 没有有效缓存时选择第一个文献
      if (this.literatureTreeData.length > 0) {
        const firstGroup = this.literatureTreeData[0]
        if (firstGroup.children && firstGroup.children.length > 0) {
          const firstLiterature = firstGroup.children[0]
          this.selectedLiterature = firstLiterature
          this.$nextTick(() => {
            this.setTreeSelection(firstLiterature.id)
            this.expandParentGroup(firstGroup.id)
            this.cacheSelectedLiterature()
            this.getList()
          })
        }
      }
    },

    /** 验证缓存的文献是否仍然有效 */
    isValidCachedLiterature (cachedLiterature) {
      if (!cachedLiterature || !cachedLiterature.id) return false

      // 检查缓存的文献是否还存在于当前文献列表中
      return this.literatureOptions.some(item => item.id === cachedLiterature.id)
    },

    /** 设置树的选中状态 */
    setTreeSelection (literatureId) {
      if (this.$refs.literatureTree) {
        this.$refs.literatureTree.setCurrentKey(literatureId)
        // 展开对应的分组
        const parentGroup = this.findParentGroup(literatureId)
        if (parentGroup) {
          this.expandParentGroup(parentGroup.id)
        }
      }
    },

    /** 展开父分组 */
    expandParentGroup (groupId) {
      if (this.$refs.literatureTree) {
        const node = this.$refs.literatureTree.getNode(groupId)
        if (node) {
          node.expanded = true
        }
      }
    },

    /** 查找文献所属的父分组 */
    findParentGroup (literatureId) {
      for (const group of this.literatureTreeData) {
        if (group.children) {
          const found = group.children.find(item => item.id === literatureId)
          if (found) {
            return group
          }
        }
      }
      return null
    },

    /** 初始化拖拽调整宽度功能 */
    initResizeHandle () {
      this.$nextTick(() => {
        const resizeHandle = document.querySelector('.resize-handle')
        const container = document.querySelector('.app-container')

        if (resizeHandle && container) {
          resizeHandle.addEventListener('mousedown', (e) => {
            e.preventDefault()

            const startX = e.clientX
            const containerWidth = container.offsetWidth
            const startSpan = this.literatureTreeSpan

            // 添加拖拽状态样式
            document.body.style.cursor = 'col-resize'
            document.body.style.userSelect = 'none'

            const onMouseMove = (e) => {
              // 使用 requestAnimationFrame 优化性能
              requestAnimationFrame(() => {
                const deltaX = e.clientX - startX
                const deltaPercent = (deltaX / containerWidth) * 24
                let newSpan = Math.round(startSpan + deltaPercent)

                // 限制最小和最大宽度
                newSpan = Math.max(2, Math.min(8, newSpan))

                // 只在值发生变化时更新
                if (newSpan !== this.literatureTreeSpan) {
                  this.literatureTreeSpan = newSpan
                }
              })
            }

            const onMouseUp = () => {
              // 恢复样式
              document.body.style.cursor = ''
              document.body.style.userSelect = ''

              document.removeEventListener('mousemove', onMouseMove)
              document.removeEventListener('mouseup', onMouseUp)
            }

            document.addEventListener('mousemove', onMouseMove)
            document.addEventListener('mouseup', onMouseUp)
          })
        }
      })
    },
    /** 导入按钮操作 */
    handleImport () {
      this.resetImportForm()
      // 如果当前选中了文献，默认设置为导入的文献
      if (this.selectedLiterature) {
        this.importDialog.form.literatureId = this.selectedLiterature.id
      }
      this.importDialog.open = true
    },

    /** 重置导入表单 */
    resetImportForm () {
      this.importDialog.form = {
        literatureId: null,
        content: ''
      }
      if (this.$refs.importForm) {
        this.$refs.importForm.resetFields()
      }
    },

    /** 提交导入 */
    submitImport () {
      this.$refs.importForm.validate(valid => {
        if (valid) {
          this.importDialog.loading = true
          importMaterial(this.importDialog.form).then(response => {
            this.$modal.msgSuccess(response.msg || "导入成功")
            this.importDialog.open = false
            this.importDialog.loading = false
            // 导入成功后刷新数据，但保持当前文献选择
            this.getList()
            this.refreshLiteratureTree()
          }).catch(() => {
            this.importDialog.loading = false
          })
        }
      })
    },

    /** 取消导入 */
    cancelImport () {
      this.importDialog.open = false
      this.resetImportForm()
    },

    /** 缓存选中的文献 */
    cacheSelectedLiterature () {
      if (this.selectedLiterature) {
        cache.session.setJSON(this.CACHE_KEY.SELECTED_LITERATURE, this.selectedLiterature)
      }
    },

    /** 恢复缓存的文献选择 */
    restoreSelectedLiterature () {
      const cachedLiterature = cache.session.getJSON(this.CACHE_KEY.SELECTED_LITERATURE)
      if (cachedLiterature && this.isValidCachedLiterature(cachedLiterature)) {
        this.selectedLiterature = cachedLiterature
        // 等待文献树渲染完成后设置选中状态
        this.$nextTick(() => {
          this.setTreeSelection(cachedLiterature.id)
        })
      }
    },

    /** 清除文献缓存 */
    clearLiteratureCache () {
      cache.session.remove(this.CACHE_KEY.SELECTED_LITERATURE)
    },
    // 获取类型标签样式 - 根据字段值的首字母或特征分配颜色
    getTypeTagType (type) {
      if (!type) return ''

      // 根据type值的哈希来分配颜色，确保相同值总是相同颜色
      const hash = type.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0)
        return a & a
      }, 0)

      const colors = ['primary', 'success', 'info', 'warning', '']
      return colors[Math.abs(hash) % colors.length]
    },

    /** 获取二维码基础URL */
    getQRCodeBaseUrl () {
      return settings.qrcode.baseUrl || window.location.origin
    },

    /** 生成二维码 */
    async generateQRCode (materialId, canvasRef) {
      try {
        const baseUrl = this.getQRCodeBaseUrl()
        const url = `${baseUrl}/mobile/material/${materialId}`

        const canvas = this.$refs[canvasRef]
        // 修复canvas引用问题：如果是数组取第一个，否则直接使用
        const canvasElement = Array.isArray(canvas) ? canvas[0] : canvas

        if (canvasElement) {
          await QRCode.toCanvas(canvasElement, url, {
            width: settings.qrcode.size.small,
            height: settings.qrcode.size.small,
            margin: settings.qrcode.style.margin,
            color: settings.qrcode.style.color
          })
        }
      } catch (error) {
        console.error('生成二维码失败:', error)
      }
    },

    /** 显示二维码对话框 */
    async showQRCodeDialog (row) {
      const baseUrl = this.getQRCodeBaseUrl()
      const url = `${baseUrl}/mobile/material/${row.id}`

      this.qrcodeDialog.materialName = row.name
      this.qrcodeDialog.url = url
      this.qrcodeDialog.visible = true

      // 等待对话框渲染完成后生成大尺寸二维码
      this.$nextTick(async () => {
        try {
          const canvas = this.$refs.qrcodeLarge
          if (canvas) {
            await QRCode.toCanvas(canvas, url, {
              width: settings.qrcode.size.large,
              height: settings.qrcode.size.large,
              margin: settings.qrcode.style.margin,
              color: settings.qrcode.style.color
            })
          }
        } catch (error) {
          console.error('生成大尺寸二维码失败:', error)
        }
      })
    },

    /** 复制URL */
    copyUrl () {
      navigator.clipboard.writeText(this.qrcodeDialog.url).then(() => {
        this.$message.success('URL已复制到剪贴板')
      }).catch(() => {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = this.qrcodeDialog.url
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('URL已复制到剪贴板')
      })
    },

    /** 生成所有可见行的二维码 */
    generateAllQRCodes () {
      this.$nextTick(() => {
        this.materialList.forEach(item => {
          this.generateQRCode(item.id, `qrcode-${item.id}`)
        })
      })
    },

    /** 打印二维码 */
    async printQRCode () {
      try {
        const baseUrl = this.getQRCodeBaseUrl()
        const url = this.qrcodeDialog.url
        const materialName = this.qrcodeDialog.materialName

        // 创建一个临时canvas用于生成打印尺寸的二维码
        const tempCanvas = document.createElement('canvas')
        await QRCode.toCanvas(tempCanvas, url, {
          width: settings.qrcode.size.print,
          height: settings.qrcode.size.print,
          margin: settings.qrcode.style.margin,
          color: settings.qrcode.style.color
        })

        // 创建打印窗口
        const printWindow = window.open('', '_blank')
        const qrCodeDataURL = tempCanvas.toDataURL('image/png')

        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
          <head>
            <title>打印二维码 - ${materialName}</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                text-align: center;
                padding: 20px;
                margin: 0;
              }
              .print-container {
                max-width: 400px;
                margin: 0 auto;
              }
              .material-name {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
                color: #333;
              }
              .qrcode-image {
                margin: 20px 0;
              }
              .qrcode-url {
                font-size: 12px;
                color: #666;
                word-break: break-all;
                margin-top: 10px;
              }
              .print-info {
                font-size: 10px;
                color: #999;
                margin-top: 20px;
              }
              @media print {
                body { margin: 0; padding: 10px; }
                .print-info { display: none; }
              }
            </style>
          </head>
          <body>
            <div class="print-container">
              <div class="material-name">${materialName}</div>
              <div class="qrcode-image">
                <img src="${qrCodeDataURL}" alt="二维码" />
              </div>
              <div class="qrcode-url">${url}</div>
              <div class="print-info">扫描二维码查看详细信息</div>
            </div>
            <script>
              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };
            <\/script>
          </body>
          </html>
        `)

        printWindow.document.close()
      } catch (error) {
        console.error('打印二维码失败:', error)
        this.$message.error('打印失败，请稍后重试')
      }
    }
  },
  beforeDestroy () {
    // 页面销毁时可以选择是否清除缓存
    // this.clearLiteratureCache()
  }
}
</script>

<style lang="scss" scoped>
.literature-tree-col {
  position: relative;

  .resize-handle {
    position: absolute;
    right: -10px;
    top: 0;
    bottom: 0;
    width: 20px;
    cursor: col-resize;
    z-index: 10;

    &::after {
      content: '';
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 40px;
      background: #e4e7ed;
      border-radius: 2px;
      transition: background-color 0.2s;
    }

    &:hover::after {
      background: #409eff;
    }
  }
}

.literature-tree-card {
  height: calc(100vh - 120px);

  .tree-container {
    height: calc(100% - 60px);
    overflow: auto;
    font-size: 13px; // 缩小字体
  }
}

.material-content {
  .breadcrumb-nav {
    margin-bottom: 16px;
    padding: 8px 0;
    border-bottom: 1px solid #EBEEF5;
  }
}


.custom-tree-node {
  font-size: 13px; // 缩小树节点字体

  .node-label {
    font-size: 13px;

    .node-icon {
      font-size: 14px;
      margin-right: 6px;
    }
  }

  .node-count {
    font-size: 12px;
    color: #909399;
  }
}

.tree-filter {
  font-size: 13px; // 缩小搜索框字体
  margin-bottom: 8px;
}

// 优化拖拽性能
.literature-tree-col {
  position: relative;
  transition: none; // 移除过渡动画提升性能

  .resize-handle {
    position: absolute;
    right: -10px;
    top: 0;
    bottom: 0;
    width: 20px;
    cursor: col-resize;
    z-index: 10;

    &::after {
      content: '';
      position: absolute;
      right: 8px;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 40px;
      background: #e4e7ed;
      border-radius: 2px;
      transition: background-color 0.2s;
    }

    &:hover::after {
      background: #409eff;
    }
  }
}

// 覆盖Element UI树组件的默认字体大小
::v-deep .el-tree-node__content {
  height: 28px; // 减小行高
  font-size: 13px;

  &:hover {
    background-color: #F5F7FA;
  }
}

::v-deep .el-tree-node.is-current>.el-tree-node__content {
  background-color: #E6F7FF;
  color: #1890ff;
  font-weight: 500;
}

::v-deep .el-input__inner {
  font-size: 13px; // 搜索框输入字体
}

.import-dialog {

  // 保留特殊的导入文本域样式
  .import-textarea {
    .el-textarea__inner {
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace !important;
      font-size: 13px !important;
      line-height: 1.5 !important;
    }
  }
}

// 二维码相关样式
.qrcode-container {
  display: flex;
  justify-content: center;
  align-items: center;

  .qrcode-canvas {
    cursor: pointer;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;

    &:hover {
      transform: scale(1.1);
    }
  }
}

.qrcode-preview {
  text-align: center;

  .qrcode-info {
    margin-bottom: 20px;

    h4 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 16px;
    }

    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .qrcode-large {
    margin-bottom: 20px;
    display: flex;
    justify-content: center;

    .qrcode-canvas-large {
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
  }

  .qrcode-url {
    .el-input-group__append {
      padding: 0 15px;
    }
  }

  .qrcode-actions {
    margin-top: 15px;
    text-align: center;

    .el-button {
      min-width: 120px;
    }
  }
}
</style>
