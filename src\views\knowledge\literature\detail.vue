<template>
  <div class="app-container literature-detail">
    <!-- 页面头部 -->
    <div class="detail-header">
      <el-page-header @back="goBack" :content="pageTitle">
        <template slot="content">
          <span class="detail-title">{{ pageTitle }}</span>
          <el-tag v-if="literatureData.type" :type="getTypeTagType(literatureData.type)" class="type-tag">
            {{ getTypeLabel(literatureData.type) }}
          </el-tag>
        </template>
      </el-page-header>
      <div class="header-actions">
        <el-button size="small" type="primary" icon="el-icon-edit" @click="handleEdit"
          v-hasPermi="['knowledge:literature:edit']">编辑</el-button>
      </div>
    </div>

    <!-- 详情内容 -->
    <el-card class="detail-card" v-loading="loading">
      <div class="detail-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>文献名称：</label>
              <span>{{ literatureData.name || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>文献类型：</label>
              <el-tag v-if="literatureData.type" :type="getTypeTagType(literatureData.type)">
                {{ getTypeLabel(literatureData.type) }}
              </el-tag>
              <span v-else>-</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>作者：</label>
              <span>{{ literatureData.author || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>编辑：</label>
              <span>{{ literatureData.editor || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>版本号：</label>
              <span>{{ literatureData.version || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>出版社：</label>
              <span>{{ literatureData.publisher || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>出版日期：</label>
              <span>{{ parseTime(literatureData.publishDate, '{y}-{m}-{d}') || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>ISBN号：</label>
              <span>{{ literatureData.isbn || '-' }}</span>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <div class="info-item">
              <label>文件大小：</label>
              <span>{{ formatFileSize(literatureData.fileSize) || '-' }}</span>
            </div>
          </el-col>
          <el-col :span="12">
            <div class="info-item">
              <label>状态：</label>
              <el-tag :type="literatureData.status === '1' ? 'success' : 'danger'">
                {{ literatureData.status === '1' ? '启用' : '禁用' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <div class="info-item">
              <label>描述：</label>
              <div class="description-content">{{ literatureData.description || '-' }}</div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="literatureData.coverImage">
          <el-col :span="24">
            <div class="info-item">
              <label>封面图片：</label>
              <div class="cover-image">
                <el-image
                  :src="literatureData.coverImage"
                  style="width: 200px; height: 280px"
                  fit="cover"
                  :preview-src-list="[literatureData.coverImage]">
                </el-image>
              </div>
            </div>
          </el-col>
        </el-row>

        <el-row :gutter="20" v-if="literatureData.filePath">
          <el-col :span="24">
            <div class="info-item">
              <label>文件链接：</label>
              <div class="file-link">
                <el-link :href="literatureData.filePath" target="_blank" type="primary" icon="el-icon-download">
                  下载文件
                </el-link>
                <span class="file-path">{{ literatureData.filePath }}</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getLiterature } from "@/api/knowledge/literature"

export default {
  name: "LiteratureDetail",
  data() {
    return {
      loading: true,
      literatureData: {},
      literatureId: null
    }
  },
  computed: {
    pageTitle() {
      if (!this.literatureData.name) return '文献详情'
      const title = this.literatureData.name.length > 30
        ? this.literatureData.name.substring(0, 30) + '...'
        : this.literatureData.name
      return title
    }
  },
  created() {
    this.literatureId = this.$route.params.id
    this.getLiteratureDetail()
  },
  methods: {
    /** 获取文献详情 */
    getLiteratureDetail() {
      this.loading = true
      getLiterature(this.literatureId).then(response => {
        this.literatureData = response.data
        this.loading = false
      }).catch(() => {
        this.loading = false
        this.$message.error('获取详情失败')
      })
    },
    /** 返回列表页 */
    goBack() {
      this.$router.go(-1)
    },
    /** 编辑按钮 */
    handleEdit() {
      this.$router.push('/knowledge/literature')
    },
    /** 格式化文件大小 */
    formatFileSize(size) {
      if (!size) return null
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return size.toFixed(2) + ' ' + units[index]
    },
    // 获取类型标签样式
    getTypeTagType(type) {
      const typeMap = {
        'pharmacopoeia': 'primary',
        'standard': 'success',
        'manual': 'info',
        'other': 'warning'
      }
      return typeMap[type] || ''
    },
    // 获取类型标签文本
    getTypeLabel(type) {
      const typeMap = {
        'pharmacopoeia': '药典',
        'standard': '标准',
        'manual': '手册',
        'other': '其他'
      }
      return typeMap[type] || type
    }
  }
}
</script>

<style lang="scss" scoped>
.literature-detail {
  .detail-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .detail-title {
      font-size: 18px;
      font-weight: 600;
      margin-right: 12px;
    }

    .type-tag {
      margin-left: 12px;
    }

    .header-actions {
      display: flex;
      gap: 8px;
    }
  }

  .detail-card {
    .detail-content {
      .info-item {
        margin-bottom: 16px;
        display: flex;
        align-items: flex-start;

        label {
          font-weight: 600;
          color: #606266;
          min-width: 100px;
          margin-right: 12px;
        }

        span {
          color: #303133;
          flex: 1;
        }

        .description-content {
          color: #303133;
          line-height: 1.6;
          text-align: justify;
          flex: 1;
        }

        .cover-image {
          flex: 1;
        }

        .file-link {
          flex: 1;
          display: flex;
          flex-direction: column;
          gap: 8px;

          .file-path {
            color: #909399;
            font-size: 12px;
            word-break: break-all;
          }
        }
      }
    }
  }
}
</style>
