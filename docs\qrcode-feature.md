# 二维码功能说明

## 功能概述

原辅料管理系统新增了二维码功能，支持为每个原辅料记录生成二维码，用户可以通过扫描二维码快速访问原辅料的详细信息。

## 主要特性

### 1. 二维码显示
- 在原辅料列表页面的表格中新增了"二维码"列
- 每行显示一个小尺寸的二维码图片
- 点击二维码可以查看大图预览

### 2. 二维码预览对话框
- 显示原辅料名称
- 显示大尺寸二维码图片
- 显示完整的访问URL
- 提供复制URL功能
- 提供打印二维码功能

### 3. 手机端适配页面
- 专门为移动设备优化的原辅料详情页面
- 响应式设计，适配不同屏幕尺寸
- 显示原辅料的完整信息
- 支持分享功能

### 4. 打印功能
- 支持打印二维码标签
- 包含原辅料名称、二维码图片和访问URL
- 自动调用浏览器打印功能

## 配置说明

### 1. 基础URL配置

二维码的访问URL可以通过环境变量进行配置：

#### 开发环境配置 (.env.development)
```bash
# 二维码基础URL配置（可选，如果不设置则使用当前页面域名）
# 示例：VUE_APP_QRCODE_BASE_URL = http://*************:8090
# 示例：VUE_APP_QRCODE_BASE_URL = https://your-domain.com
VUE_APP_QRCODE_BASE_URL =
```

#### 生产环境配置 (.env.production)
```bash
# 二维码基础URL配置（生产环境请设置为实际域名）
# 示例：VUE_APP_QRCODE_BASE_URL = https://your-production-domain.com
VUE_APP_QRCODE_BASE_URL =
```

### 2. 系统配置 (src/settings.js)

在 `settings.js` 文件中可以配置二维码的详细参数：

```javascript
qrcode: {
  // 二维码访问域名，用于生成二维码链接
  baseUrl: process.env.VUE_APP_QRCODE_BASE_URL || '',
  
  // 二维码尺寸配置
  size: {
    small: 60,    // 表格中显示的小尺寸
    large: 200,   // 预览对话框中的大尺寸
    print: 150    // 打印时的尺寸
  },
  
  // 二维码样式配置
  style: {
    margin: 1,
    color: {
      dark: '#000000',
      light: '#FFFFFF'
    }
  }
}
```

## 使用方法

### 1. 查看二维码
1. 进入原辅料管理页面
2. 在表格的"二维码"列中可以看到每个原辅料的二维码
3. 点击二维码图片可以打开预览对话框

### 2. 打印二维码
1. 点击表格中的二维码图片
2. 在弹出的预览对话框中点击"打印二维码"按钮
3. 系统会自动打开打印预览窗口
4. 确认打印设置后即可打印

### 3. 扫码访问
1. 使用手机扫描二维码
2. 自动跳转到手机端的原辅料详情页面
3. 可以查看完整的原辅料信息

### 4. 分享功能
1. 在手机端详情页面点击"分享"按钮
2. 支持原生分享API（如果浏览器支持）
3. 或者自动复制链接到剪贴板

## 技术实现

### 1. 依赖库
- `qrcode`: 用于生成二维码图片

### 2. 核心文件
- `src/views/knowledge/material/index.vue`: 主要的原辅料列表页面
- `src/views/mobile/material/detail.vue`: 手机端详情页面
- `src/settings.js`: 系统配置文件
- `src/router/index.js`: 路由配置

### 3. 路由配置
```javascript
{
  path: '/mobile/material/:id',
  component: () => import('@/views/mobile/material/detail'),
  name: 'MobileMaterialDetail',
  hidden: true,
  meta: { title: '原辅料详情' }
}
```

## 注意事项

1. **URL配置**: 生产环境部署时，请确保在 `.env.production` 中配置正确的域名，否则二维码可能无法正常访问。

2. **网络访问**: 手机扫码访问需要确保手机能够访问到系统的网络地址。

3. **浏览器兼容性**: 打印功能依赖浏览器的打印API，建议使用现代浏览器。

4. **移动端适配**: 手机端页面已经过响应式优化，支持不同尺寸的移动设备。

5. **性能考虑**: 二维码是在页面加载完成后异步生成的，大量数据时可能会有轻微延迟。

## 故障排除

### 1. 二维码不显示
- 检查控制台是否有JavaScript错误
- 确认qrcode库是否正确安装
- 检查网络连接是否正常

### 2. 扫码无法访问
- 检查VUE_APP_QRCODE_BASE_URL配置是否正确
- 确认手机能够访问配置的URL地址
- 检查路由配置是否正确

### 3. 打印功能异常
- 确认浏览器支持window.print()方法
- 检查弹窗是否被浏览器拦截
- 尝试手动允许弹窗

## 更新日志

- v1.0.0: 初始版本，支持基础的二维码生成和显示功能
- v1.1.0: 新增打印功能和手机端适配页面
- v1.2.0: 支持自定义URL配置和系统参数配置
