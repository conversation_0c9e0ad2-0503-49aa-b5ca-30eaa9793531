/**
 * 协作服务 - 处理实时协同编辑功能
 */

class CollaborationService {
  constructor() {
    this.socket = null
    this.documentId = null
    this.userId = null
    this.userName = null
    this.callbacks = {
      contentChange: [],
      cursorChange: [],
      userJoin: [],
      userLeave: [],
      comment: [],
      version: []
    }
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
  }

  /**
   * 初始化协作服务
   * @param {Object} config - 配置对象
   * @param {string} config.documentId - 文档ID
   * @param {string} config.userId - 用户ID
   * @param {string} config.userName - 用户名
   * @param {string} config.wsUrl - WebSocket服务器地址
   */
  initialize(config) {
    this.documentId = config.documentId
    this.userId = config.userId
    this.userName = config.userName
    this.wsUrl = config.wsUrl || 'ws://localhost:3001'
    
    this.connect()
  }

  /**
   * 连接WebSocket服务器
   */
  connect() {
    try {
      // 在实际项目中，这里会连接真实的WebSocket服务器
      // this.socket = new WebSocket(`${this.wsUrl}/collaboration/${this.documentId}`)
      
      // 模拟WebSocket连接
      this.socket = {
        send: (data) => {
          console.log('发送数据:', JSON.parse(data))
          this.simulateServerResponse(JSON.parse(data))
        },
        close: () => {
          this.isConnected = false
        }
      }

      this.setupEventHandlers()
      this.isConnected = true
      this.reconnectAttempts = 0
      
      // 加入文档协作
      this.joinDocument()
      
    } catch (error) {
      console.error('WebSocket连接失败:', error)
      this.handleReconnect()
    }
  }

  /**
   * 设置事件处理器
   */
  setupEventHandlers() {
    // 在实际项目中，这里会设置真实的WebSocket事件处理器
    // this.socket.onopen = () => { ... }
    // this.socket.onmessage = (event) => { ... }
    // this.socket.onclose = () => { ... }
    // this.socket.onerror = (error) => { ... }
  }

  /**
   * 模拟服务器响应（用于演示）
   */
  simulateServerResponse(data) {
    setTimeout(() => {
      switch (data.type) {
        case 'join_document':
          this.handleUserJoin({
            type: 'user_joined',
            data: {
              userId: 'user_' + Math.random().toString(36).substr(2, 9),
              userName: '协作用户' + Math.floor(Math.random() * 100),
              cursorColor: this.generateRandomColor()
            }
          })
          break
          
        case 'content_change':
          // 模拟其他用户的内容变化
          this.handleContentChange({
            type: 'content_changed',
            data: {
              ...data.data,
              userId: 'other_user',
              timestamp: Date.now()
            }
          })
          break
          
        case 'cursor_change':
          // 模拟其他用户的光标变化
          this.handleCursorChange({
            type: 'cursor_changed',
            data: {
              ...data.data,
              userId: 'other_user',
              userName: '其他用户',
              cursorColor: '#ff6b6b'
            }
          })
          break
      }
    }, 100)
  }

  /**
   * 加入文档协作
   */
  joinDocument() {
    const message = {
      type: 'join_document',
      data: {
        documentId: this.documentId,
        userId: this.userId,
        userName: this.userName
      }
    }
    
    this.send(message)
  }

  /**
   * 发送消息
   */
  send(message) {
    if (this.socket && this.isConnected) {
      this.socket.send(JSON.stringify(message))
    }
  }

  /**
   * 同步内容变化
   * @param {Object} change - 内容变化对象
   * @param {string} change.content - 变化后的内容
   * @param {Object} change.delta - 变化的增量
   * @param {number} change.timestamp - 时间戳
   */
  syncContentChange(change) {
    const message = {
      type: 'content_change',
      data: {
        documentId: this.documentId,
        userId: this.userId,
        content: change.content,
        delta: change.delta,
        timestamp: change.timestamp || Date.now()
      }
    }
    
    this.send(message)
  }

  /**
   * 同步光标位置
   * @param {Object} cursor - 光标位置对象
   * @param {number} cursor.index - 光标位置索引
   * @param {number} cursor.length - 选择长度
   */
  syncCursorPosition(cursor) {
    const message = {
      type: 'cursor_change',
      data: {
        documentId: this.documentId,
        userId: this.userId,
        userName: this.userName,
        cursor: cursor,
        timestamp: Date.now()
      }
    }
    
    this.send(message)
  }

  /**
   * 发送评论
   * @param {Object} comment - 评论对象
   */
  sendComment(comment) {
    const message = {
      type: 'comment',
      data: {
        documentId: this.documentId,
        userId: this.userId,
        userName: this.userName,
        comment: comment,
        timestamp: Date.now()
      }
    }
    
    this.send(message)
  }

  /**
   * 创建版本
   * @param {Object} version - 版本对象
   */
  createVersion(version) {
    const message = {
      type: 'create_version',
      data: {
        documentId: this.documentId,
        userId: this.userId,
        userName: this.userName,
        version: version,
        timestamp: Date.now()
      }
    }
    
    this.send(message)
  }

  /**
   * 处理用户加入
   */
  handleUserJoin(message) {
    this.callbacks.userJoin.forEach(callback => {
      callback(message.data)
    })
  }

  /**
   * 处理用户离开
   */
  handleUserLeave(message) {
    this.callbacks.userLeave.forEach(callback => {
      callback(message.data)
    })
  }

  /**
   * 处理内容变化
   */
  handleContentChange(message) {
    this.callbacks.contentChange.forEach(callback => {
      callback(message.data)
    })
  }

  /**
   * 处理光标变化
   */
  handleCursorChange(message) {
    this.callbacks.cursorChange.forEach(callback => {
      callback(message.data)
    })
  }

  /**
   * 处理评论
   */
  handleComment(message) {
    this.callbacks.comment.forEach(callback => {
      callback(message.data)
    })
  }

  /**
   * 处理版本变化
   */
  handleVersion(message) {
    this.callbacks.version.forEach(callback => {
      callback(message.data)
    })
  }

  /**
   * 注册事件回调
   * @param {string} event - 事件类型
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (this.callbacks[event]) {
      this.callbacks[event].push(callback)
    }
  }

  /**
   * 移除事件回调
   * @param {string} event - 事件类型
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    if (this.callbacks[event]) {
      const index = this.callbacks[event].indexOf(callback)
      if (index > -1) {
        this.callbacks[event].splice(index, 1)
      }
    }
  }

  /**
   * 处理重连
   */
  handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      const delay = Math.pow(2, this.reconnectAttempts) * 1000 // 指数退避
      
      setTimeout(() => {
        console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`)
        this.connect()
      }, delay)
    } else {
      console.error('达到最大重连次数，停止重连')
    }
  }

  /**
   * 生成随机颜色
   */
  generateRandomColor() {
    const colors = [
      '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', 
      '#ffeaa7', '#dda0dd', '#98d8c8', '#f7dc6f'
    ]
    return colors[Math.floor(Math.random() * colors.length)]
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.socket) {
      this.socket.close()
      this.socket = null
      this.isConnected = false
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      documentId: this.documentId,
      userId: this.userId,
      userName: this.userName
    }
  }
}

// 创建单例实例
const collaborationService = new CollaborationService()

export default collaborationService

/**
 * 操作转换工具类
 * 用于处理并发编辑时的操作冲突
 */
export class OperationalTransform {
  /**
   * 转换操作
   * @param {Object} op1 - 操作1
   * @param {Object} op2 - 操作2
   * @returns {Object} 转换后的操作
   */
  static transform(op1, op2) {
    // 简化的操作转换实现
    // 实际项目中需要更复杂的OT算法
    
    if (op1.type === 'insert' && op2.type === 'insert') {
      if (op1.index <= op2.index) {
        return {
          ...op2,
          index: op2.index + op1.text.length
        }
      }
    }
    
    if (op1.type === 'delete' && op2.type === 'insert') {
      if (op1.index < op2.index) {
        return {
          ...op2,
          index: op2.index - op1.length
        }
      }
    }
    
    return op2
  }

  /**
   * 应用操作
   * @param {string} content - 原始内容
   * @param {Object} operation - 操作对象
   * @returns {string} 应用操作后的内容
   */
  static apply(content, operation) {
    switch (operation.type) {
      case 'insert':
        return content.slice(0, operation.index) + 
               operation.text + 
               content.slice(operation.index)
               
      case 'delete':
        return content.slice(0, operation.index) + 
               content.slice(operation.index + operation.length)
               
      case 'retain':
        return content
        
      default:
        return content
    }
  }
}

/**
 * 版本控制工具类
 */
export class VersionControl {
  constructor() {
    this.versions = []
    this.currentVersion = null
  }

  /**
   * 创建版本
   * @param {Object} versionData - 版本数据
   */
  createVersion(versionData) {
    const version = {
      id: Date.now().toString(),
      title: versionData.title || `版本 ${this.versions.length + 1}`,
      content: versionData.content,
      author: versionData.author,
      createdAt: new Date().toISOString(),
      changes: versionData.changes || []
    }
    
    this.versions.unshift(version)
    this.currentVersion = version
    
    return version
  }

  /**
   * 获取版本列表
   */
  getVersions() {
    return this.versions
  }

  /**
   * 获取版本
   * @param {string} versionId - 版本ID
   */
  getVersion(versionId) {
    return this.versions.find(v => v.id === versionId)
  }

  /**
   * 比较版本
   * @param {string} version1Id - 版本1 ID
   * @param {string} version2Id - 版本2 ID
   */
  compareVersions(version1Id, version2Id) {
    const version1 = this.getVersion(version1Id)
    const version2 = this.getVersion(version2Id)
    
    if (!version1 || !version2) {
      throw new Error('版本不存在')
    }
    
    // 简化的版本比较实现
    return {
      version1: version1,
      version2: version2,
      diff: this.generateDiff(version1.content, version2.content)
    }
  }

  /**
   * 生成差异
   * @param {string} content1 - 内容1
   * @param {string} content2 - 内容2
   */
  generateDiff(content1, content2) {
    // 简化的差异生成实现
    // 实际项目中可以使用专业的diff库如diff2html
    
    const lines1 = content1.split('\n')
    const lines2 = content2.split('\n')
    const diff = []
    
    const maxLines = Math.max(lines1.length, lines2.length)
    
    for (let i = 0; i < maxLines; i++) {
      const line1 = lines1[i] || ''
      const line2 = lines2[i] || ''
      
      if (line1 !== line2) {
        if (line1 && !line2) {
          diff.push({ type: 'removed', content: line1, lineNumber: i + 1 })
        } else if (!line1 && line2) {
          diff.push({ type: 'added', content: line2, lineNumber: i + 1 })
        } else {
          diff.push({ type: 'modified', oldContent: line1, newContent: line2, lineNumber: i + 1 })
        }
      }
    }
    
    return diff
  }

  /**
   * 恢复版本
   * @param {string} versionId - 版本ID
   */
  restoreVersion(versionId) {
    const version = this.getVersion(versionId)
    if (!version) {
      throw new Error('版本不存在')
    }
    
    this.currentVersion = version
    return version
  }
}
