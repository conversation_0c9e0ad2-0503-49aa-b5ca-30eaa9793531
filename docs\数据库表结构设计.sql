-- =============================================
-- 工作空间文档管理系统数据库表结构设计
-- =============================================

-- 1. 工作空间表
CREATE TABLE `workspaces` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '工作空间ID',
  `name` varchar(100) NOT NULL COMMENT '工作空间名称',
  `description` text COMMENT '工作空间描述',
  `owner_id` bigint(20) NOT NULL COMMENT '所有者用户ID',
  `avatar` varchar(255) DEFAULT NULL COMMENT '工作空间头像',
  `settings` json DEFAULT NULL COMMENT '工作空间设置',
  `storage_limit` bigint(20) DEFAULT 10737418240 COMMENT '存储限制(字节)',
  `storage_used` bigint(20) DEFAULT 0 COMMENT '已使用存储(字节)',
  `member_limit` int(11) DEFAULT 100 COMMENT '成员数量限制',
  `status` tinyint(4) DEFAULT 1 COMMENT '状态：1-正常，2-禁用，3-删除',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_owner_id` (`owner_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作空间表';

-- 2. 工作空间成员表
CREATE TABLE `workspace_members` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '成员ID',
  `workspace_id` bigint(20) NOT NULL COMMENT '工作空间ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `role` varchar(20) NOT NULL DEFAULT 'member' COMMENT '角色：owner-所有者，admin-管理员，member-成员，viewer-查看者',
  `permissions` json DEFAULT NULL COMMENT '权限配置',
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `status` tinyint(4) DEFAULT 1 COMMENT '状态：1-正常，2-禁用',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_workspace_user` (`workspace_id`, `user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工作空间成员表';

-- 3. 文件夹表
CREATE TABLE `folders` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件夹ID',
  `workspace_id` bigint(20) NOT NULL COMMENT '工作空间ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父文件夹ID',
  `name` varchar(255) NOT NULL COMMENT '文件夹名称',
  `path` varchar(1000) NOT NULL COMMENT '文件夹路径',
  `description` text COMMENT '文件夹描述',
  `creator_id` bigint(20) NOT NULL COMMENT '创建者ID',
  `permissions` json DEFAULT NULL COMMENT '权限配置',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  `is_deleted` tinyint(4) DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_workspace_id` (`workspace_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件夹表';

-- 4. 文档表
CREATE TABLE `documents` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文档ID',
  `workspace_id` bigint(20) NOT NULL COMMENT '工作空间ID',
  `folder_id` bigint(20) DEFAULT NULL COMMENT '文件夹ID',
  `title` varchar(255) NOT NULL COMMENT '文档标题',
  `content` longtext COMMENT '文档内容',
  `content_type` varchar(50) DEFAULT 'markdown' COMMENT '内容类型：markdown，html，text',
  `author_id` bigint(20) NOT NULL COMMENT '作者ID',
  `template_id` bigint(20) DEFAULT NULL COMMENT '模板ID',
  `cover_image` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `summary` text COMMENT '文档摘要',
  `tags` json DEFAULT NULL COMMENT '标签列表',
  `word_count` int(11) DEFAULT 0 COMMENT '字数统计',
  `character_count` int(11) DEFAULT 0 COMMENT '字符数统计',
  `reading_time` int(11) DEFAULT 0 COMMENT '预计阅读时间(分钟)',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `edit_count` int(11) DEFAULT 0 COMMENT '编辑次数',
  `comment_count` int(11) DEFAULT 0 COMMENT '评论数量',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数量',
  `share_count` int(11) DEFAULT 0 COMMENT '分享次数',
  `is_public` tinyint(4) DEFAULT 0 COMMENT '是否公开：0-否，1-是',
  `is_template` tinyint(4) DEFAULT 0 COMMENT '是否为模板：0-否，1-是',
  `is_favorite` tinyint(4) DEFAULT 0 COMMENT '是否收藏：0-否，1-是',
  `is_archived` tinyint(4) DEFAULT 0 COMMENT '是否归档：0-否，1-是',
  `is_deleted` tinyint(4) DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `last_edited_at` timestamp NULL DEFAULT NULL COMMENT '最后编辑时间',
  `last_editor_id` bigint(20) DEFAULT NULL COMMENT '最后编辑者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_workspace_id` (`workspace_id`),
  KEY `idx_folder_id` (`folder_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_is_deleted` (`is_deleted`),
  KEY `idx_created_at` (`created_at`),
  FULLTEXT KEY `ft_title_content` (`title`, `content`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档表';

-- 5. 文档版本表
CREATE TABLE `document_versions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `document_id` bigint(20) NOT NULL COMMENT '文档ID',
  `version_number` int(11) NOT NULL COMMENT '版本号',
  `title` varchar(255) NOT NULL COMMENT '版本标题',
  `content` longtext NOT NULL COMMENT '版本内容',
  `content_hash` varchar(64) NOT NULL COMMENT '内容哈希值',
  `author_id` bigint(20) NOT NULL COMMENT '作者ID',
  `message` text COMMENT '版本说明',
  `changes_summary` json DEFAULT NULL COMMENT '变更摘要',
  `parent_version_id` bigint(20) DEFAULT NULL COMMENT '父版本ID',
  `size` bigint(20) DEFAULT 0 COMMENT '内容大小(字节)',
  `word_count` int(11) DEFAULT 0 COMMENT '字数统计',
  `is_major` tinyint(4) DEFAULT 0 COMMENT '是否主要版本：0-否，1-是',
  `is_published` tinyint(4) DEFAULT 0 COMMENT '是否发布：0-否，1-是',
  `metadata` json DEFAULT NULL COMMENT '元数据',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_document_version` (`document_id`, `version_number`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_parent_version_id` (`parent_version_id`),
  KEY `idx_content_hash` (`content_hash`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档版本表';

-- 6. 版本变更记录表
CREATE TABLE `version_changes` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '变更ID',
  `version_id` bigint(20) NOT NULL COMMENT '版本ID',
  `change_type` varchar(20) NOT NULL COMMENT '变更类型：add-新增，delete-删除，modify-修改',
  `line_number` int(11) DEFAULT NULL COMMENT '行号',
  `old_line_number` int(11) DEFAULT NULL COMMENT '原行号',
  `content` text COMMENT '变更内容',
  `old_content` text COMMENT '原内容',
  `position` int(11) DEFAULT 0 COMMENT '位置',
  `length` int(11) DEFAULT 0 COMMENT '长度',
  PRIMARY KEY (`id`),
  KEY `idx_version_id` (`version_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本变更记录表';

-- 7. 版本标签表
CREATE TABLE `version_tags` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `document_id` bigint(20) NOT NULL COMMENT '文档ID',
  `version_id` bigint(20) NOT NULL COMMENT '版本ID',
  `name` varchar(100) NOT NULL COMMENT '标签名称',
  `description` text COMMENT '标签描述',
  `color` varchar(7) DEFAULT '#409EFF' COMMENT '标签颜色',
  `creator_id` bigint(20) NOT NULL COMMENT '创建者ID',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_document_tag_name` (`document_id`, `name`),
  KEY `idx_version_id` (`version_id`),
  KEY `idx_creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='版本标签表';

-- 8. 文档分支表
CREATE TABLE `document_branches` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分支ID',
  `document_id` bigint(20) NOT NULL COMMENT '文档ID',
  `name` varchar(100) NOT NULL COMMENT '分支名称',
  `description` text COMMENT '分支描述',
  `from_version_id` bigint(20) NOT NULL COMMENT '起始版本ID',
  `current_version_id` bigint(20) NOT NULL COMMENT '当前版本ID',
  `creator_id` bigint(20) NOT NULL COMMENT '创建者ID',
  `is_default` tinyint(4) DEFAULT 0 COMMENT '是否默认分支：0-否，1-是',
  `is_protected` tinyint(4) DEFAULT 0 COMMENT '是否受保护：0-否，1-是',
  `is_merged` tinyint(4) DEFAULT 0 COMMENT '是否已合并：0-否，1-是',
  `merged_at` timestamp NULL DEFAULT NULL COMMENT '合并时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_document_branch_name` (`document_id`, `name`),
  KEY `idx_from_version_id` (`from_version_id`),
  KEY `idx_current_version_id` (`current_version_id`),
  KEY `idx_creator_id` (`creator_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档分支表';

-- 9. 协作会话表
CREATE TABLE `collaboration_sessions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `document_id` bigint(20) NOT NULL COMMENT '文档ID',
  `session_key` varchar(100) NOT NULL COMMENT '会话标识',
  `title` varchar(255) DEFAULT NULL COMMENT '会话标题',
  `description` text COMMENT '会话描述',
  `creator_id` bigint(20) NOT NULL COMMENT '创建者ID',
  `max_participants` int(11) DEFAULT 10 COMMENT '最大参与者数量',
  `current_participants` int(11) DEFAULT 0 COMMENT '当前参与者数量',
  `is_active` tinyint(4) DEFAULT 1 COMMENT '是否活跃：0-否，1-是',
  `is_public` tinyint(4) DEFAULT 0 COMMENT '是否公开：0-否，1-是',
  `settings` json DEFAULT NULL COMMENT '会话设置',
  `started_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `ended_at` timestamp NULL DEFAULT NULL COMMENT '结束时间',
  `last_activity_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_key` (`session_key`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='协作会话表';

-- 10. 协作参与者表
CREATE TABLE `collaboration_participants` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '参与者ID',
  `session_id` bigint(20) NOT NULL COMMENT '会话ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `connection_id` varchar(100) NOT NULL COMMENT '连接ID',
  `nickname` varchar(100) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `cursor_position` int(11) DEFAULT 0 COMMENT '光标位置',
  `selection_start` int(11) DEFAULT NULL COMMENT '选择开始位置',
  `selection_end` int(11) DEFAULT NULL COMMENT '选择结束位置',
  `cursor_color` varchar(7) DEFAULT '#409EFF' COMMENT '光标颜色',
  `permissions` json DEFAULT NULL COMMENT '权限配置',
  `is_online` tinyint(4) DEFAULT 1 COMMENT '是否在线：0-否，1-是',
  `joined_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '加入时间',
  `left_at` timestamp NULL DEFAULT NULL COMMENT '离开时间',
  `last_activity_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后活动时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_session_connection` (`session_id`, `connection_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_is_online` (`is_online`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='协作参与者表';

-- 11. 协作操作记录表
CREATE TABLE `collaboration_operations` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '操作ID',
  `session_id` bigint(20) NOT NULL COMMENT '会话ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `connection_id` varchar(100) NOT NULL COMMENT '连接ID',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型：content-change，cursor-update，selection-change',
  `operation_data` json NOT NULL COMMENT '操作数据',
  `position` int(11) DEFAULT 0 COMMENT '操作位置',
  `length` int(11) DEFAULT 0 COMMENT '操作长度',
  `content` text COMMENT '操作内容',
  `version_vector` json DEFAULT NULL COMMENT '版本向量',
  `timestamp` bigint(20) NOT NULL COMMENT '时间戳',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_timestamp` (`timestamp`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='协作操作记录表';

-- 12. 评论表
CREATE TABLE `comments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `document_id` bigint(20) NOT NULL COMMENT '文档ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父评论ID',
  `author_id` bigint(20) NOT NULL COMMENT '作者ID',
  `content` text NOT NULL COMMENT '评论内容',
  `content_html` text COMMENT '评论HTML内容',
  `position` int(11) DEFAULT 0 COMMENT '评论位置',
  `selection_start` int(11) DEFAULT NULL COMMENT '选择开始位置',
  `selection_end` int(11) DEFAULT NULL COMMENT '选择结束位置',
  `selected_text` text COMMENT '选中的文本',
  `mentions` json DEFAULT NULL COMMENT '提醒的用户列表',
  `attachments` json DEFAULT NULL COMMENT '附件列表',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数量',
  `reply_count` int(11) DEFAULT 0 COMMENT '回复数量',
  `is_resolved` tinyint(4) DEFAULT 0 COMMENT '是否已解决：0-否，1-是',
  `resolved_by` bigint(20) DEFAULT NULL COMMENT '解决者ID',
  `resolved_at` timestamp NULL DEFAULT NULL COMMENT '解决时间',
  `is_deleted` tinyint(4) DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_is_resolved` (`is_resolved`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评论表';

-- 13. 提醒记录表
CREATE TABLE `mentions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '提醒ID',
  `workspace_id` bigint(20) NOT NULL COMMENT '工作空间ID',
  `document_id` bigint(20) DEFAULT NULL COMMENT '文档ID',
  `comment_id` bigint(20) DEFAULT NULL COMMENT '评论ID',
  `mentioned_user_id` bigint(20) NOT NULL COMMENT '被提醒用户ID',
  `mentioner_id` bigint(20) NOT NULL COMMENT '提醒者ID',
  `mention_type` varchar(20) NOT NULL COMMENT '提醒类型：comment-评论，document-文档，reply-回复',
  `content` text COMMENT '提醒内容',
  `is_read` tinyint(4) DEFAULT 0 COMMENT '是否已读：0-否，1-是',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_workspace_id` (`workspace_id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_mentioned_user_id` (`mentioned_user_id`),
  KEY `idx_mentioner_id` (`mentioner_id`),
  KEY `idx_is_read` (`is_read`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='提醒记录表';

-- 14. 模板表
CREATE TABLE `templates` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `workspace_id` bigint(20) NOT NULL COMMENT '工作空间ID',
  `category_id` bigint(20) DEFAULT NULL COMMENT '分类ID',
  `name` varchar(255) NOT NULL COMMENT '模板名称',
  `description` text COMMENT '模板描述',
  `content` longtext NOT NULL COMMENT '模板内容',
  `preview_image` varchar(255) DEFAULT NULL COMMENT '预览图片',
  `icon` varchar(100) DEFAULT 'el-icon-document' COMMENT '图标',
  `author_id` bigint(20) NOT NULL COMMENT '作者ID',
  `tags` json DEFAULT NULL COMMENT '标签列表',
  `variables` json DEFAULT NULL COMMENT '模板变量',
  `usage_count` int(11) DEFAULT 0 COMMENT '使用次数',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数量',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `rating` decimal(3,2) DEFAULT 0.00 COMMENT '评分',
  `rating_count` int(11) DEFAULT 0 COMMENT '评分次数',
  `is_public` tinyint(4) DEFAULT 0 COMMENT '是否公开：0-否，1-是',
  `is_featured` tinyint(4) DEFAULT 0 COMMENT '是否推荐：0-否，1-是',
  `is_official` tinyint(4) DEFAULT 0 COMMENT '是否官方：0-否，1-是',
  `is_deleted` tinyint(4) DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `published_at` timestamp NULL DEFAULT NULL COMMENT '发布时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_workspace_id` (`workspace_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_is_public` (`is_public`),
  KEY `idx_is_featured` (`is_featured`),
  KEY `idx_usage_count` (`usage_count`),
  FULLTEXT KEY `ft_name_description` (`name`, `description`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板表';

-- 15. 模板分类表
CREATE TABLE `template_categories` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `workspace_id` bigint(20) NOT NULL COMMENT '工作空间ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `icon` varchar(100) DEFAULT NULL COMMENT '分类图标',
  `color` varchar(7) DEFAULT '#409EFF' COMMENT '分类颜色',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  `template_count` int(11) DEFAULT 0 COMMENT '模板数量',
  `is_system` tinyint(4) DEFAULT 0 COMMENT '是否系统分类：0-否，1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_workspace_id` (`workspace_id`),
  KEY `idx_parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板分类表';

-- 16. 模板标签表
CREATE TABLE `template_tags` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '标签ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `name` varchar(50) NOT NULL COMMENT '标签名称',
  `color` varchar(7) DEFAULT '#909399' COMMENT '标签颜色',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板标签表';

-- 17. 模板版本表
CREATE TABLE `template_versions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '版本ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `version_number` varchar(20) NOT NULL COMMENT '版本号',
  `content` longtext NOT NULL COMMENT '版本内容',
  `changelog` text COMMENT '更新日志',
  `author_id` bigint(20) NOT NULL COMMENT '作者ID',
  `is_current` tinyint(4) DEFAULT 0 COMMENT '是否当前版本：0-否，1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_template_version` (`template_id`, `version_number`),
  KEY `idx_author_id` (`author_id`),
  KEY `idx_is_current` (`is_current`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板版本表';

-- 18. 模板评论表
CREATE TABLE `template_comments` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `template_id` bigint(20) NOT NULL COMMENT '模板ID',
  `parent_id` bigint(20) DEFAULT NULL COMMENT '父评论ID',
  `author_id` bigint(20) NOT NULL COMMENT '作者ID',
  `content` text NOT NULL COMMENT '评论内容',
  `rating` tinyint(4) DEFAULT NULL COMMENT '评分：1-5',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数量',
  `is_deleted` tinyint(4) DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_template_id` (`template_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_author_id` (`author_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模板评论表';

-- 19. 文档权限表
CREATE TABLE `document_permissions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '权限ID',
  `document_id` bigint(20) NOT NULL COMMENT '文档ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `group_id` bigint(20) DEFAULT NULL COMMENT '用户组ID',
  `permission_type` varchar(20) NOT NULL COMMENT '权限类型：read-读取，write-写入，comment-评论，share-分享，admin-管理',
  `granted_by` bigint(20) NOT NULL COMMENT '授权者ID',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档权限表';

-- 20. 文档分享表
CREATE TABLE `document_shares` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '分享ID',
  `document_id` bigint(20) NOT NULL COMMENT '文档ID',
  `share_key` varchar(100) NOT NULL COMMENT '分享密钥',
  `title` varchar(255) DEFAULT NULL COMMENT '分享标题',
  `description` text COMMENT '分享描述',
  `creator_id` bigint(20) NOT NULL COMMENT '创建者ID',
  `permissions` json NOT NULL COMMENT '分享权限',
  `password` varchar(100) DEFAULT NULL COMMENT '访问密码',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `max_views` int(11) DEFAULT NULL COMMENT '最大查看次数',
  `is_active` tinyint(4) DEFAULT 1 COMMENT '是否激活：0-否，1-是',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_key` (`share_key`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_creator_id` (`creator_id`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档分享表';

-- 21. 文档收藏表
CREATE TABLE `document_favorites` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `document_id` bigint(20) NOT NULL COMMENT '文档ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `folder_name` varchar(100) DEFAULT '默认收藏夹' COMMENT '收藏夹名称',
  `notes` text COMMENT '收藏备注',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_document_user` (`document_id`, `user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档收藏表';

-- 22. 文档访问记录表
CREATE TABLE `document_access_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '访问ID',
  `document_id` bigint(20) NOT NULL COMMENT '文档ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `access_type` varchar(20) NOT NULL COMMENT '访问类型：view-查看，edit-编辑，download-下载，share-分享',
  `duration` int(11) DEFAULT 0 COMMENT '访问时长(秒)',
  `referrer` varchar(500) DEFAULT NULL COMMENT '来源页面',
  `device_type` varchar(20) DEFAULT NULL COMMENT '设备类型：desktop-桌面，mobile-移动，tablet-平板',
  `browser` varchar(50) DEFAULT NULL COMMENT '浏览器',
  `os` varchar(50) DEFAULT NULL COMMENT '操作系统',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '访问时间',
  PRIMARY KEY (`id`),
  KEY `idx_document_id` (`document_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_access_type` (`access_type`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档访问记录表';

-- 23. 文档统计表
CREATE TABLE `document_statistics` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `document_id` bigint(20) NOT NULL COMMENT '文档ID',
  `date` date NOT NULL COMMENT '统计日期',
  `view_count` int(11) DEFAULT 0 COMMENT '查看次数',
  `unique_view_count` int(11) DEFAULT 0 COMMENT '独立查看次数',
  `edit_count` int(11) DEFAULT 0 COMMENT '编辑次数',
  `comment_count` int(11) DEFAULT 0 COMMENT '评论次数',
  `share_count` int(11) DEFAULT 0 COMMENT '分享次数',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `collaboration_time` int(11) DEFAULT 0 COMMENT '协作时长(分钟)',
  `active_users` int(11) DEFAULT 0 COMMENT '活跃用户数',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_document_date` (`document_id`, `date`),
  KEY `idx_date` (`date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文档统计表';

-- 24. 文件上传表
CREATE TABLE `file_uploads` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '文件ID',
  `workspace_id` bigint(20) NOT NULL COMMENT '工作空间ID',
  `folder_id` bigint(20) DEFAULT NULL COMMENT '文件夹ID',
  `original_name` varchar(255) NOT NULL COMMENT '原始文件名',
  `file_name` varchar(255) NOT NULL COMMENT '存储文件名',
  `file_path` varchar(500) NOT NULL COMMENT '文件路径',
  `file_size` bigint(20) NOT NULL COMMENT '文件大小(字节)',
  `file_type` varchar(100) NOT NULL COMMENT '文件类型',
  `mime_type` varchar(100) NOT NULL COMMENT 'MIME类型',
  `file_hash` varchar(64) NOT NULL COMMENT '文件哈希值',
  `thumbnail_path` varchar(500) DEFAULT NULL COMMENT '缩略图路径',
  `uploader_id` bigint(20) NOT NULL COMMENT '上传者ID',
  `download_count` int(11) DEFAULT 0 COMMENT '下载次数',
  `is_public` tinyint(4) DEFAULT 0 COMMENT '是否公开：0-否，1-是',
  `is_deleted` tinyint(4) DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `metadata` json DEFAULT NULL COMMENT '文件元数据',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_workspace_id` (`workspace_id`),
  KEY `idx_folder_id` (`folder_id`),
  KEY `idx_uploader_id` (`uploader_id`),
  KEY `idx_file_hash` (`file_hash`),
  KEY `idx_file_type` (`file_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件上传表';

-- 25. 活动日志表
CREATE TABLE `activity_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `workspace_id` bigint(20) NOT NULL COMMENT '工作空间ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `action` varchar(50) NOT NULL COMMENT '操作类型',
  `resource_type` varchar(50) NOT NULL COMMENT '资源类型：document，template，folder，comment',
  `resource_id` bigint(20) NOT NULL COMMENT '资源ID',
  `resource_title` varchar(255) DEFAULT NULL COMMENT '资源标题',
  `description` text COMMENT '操作描述',
  `metadata` json DEFAULT NULL COMMENT '操作元数据',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_workspace_id` (`workspace_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action` (`action`),
  KEY `idx_resource_type` (`resource_type`),
  KEY `idx_resource_id` (`resource_id`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='活动日志表';

-- 26. 通知表
CREATE TABLE `notifications` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '通知ID',
  `workspace_id` bigint(20) NOT NULL COMMENT '工作空间ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `type` varchar(50) NOT NULL COMMENT '通知类型：mention，comment，share，collaboration',
  `title` varchar(255) NOT NULL COMMENT '通知标题',
  `content` text COMMENT '通知内容',
  `data` json DEFAULT NULL COMMENT '通知数据',
  `sender_id` bigint(20) DEFAULT NULL COMMENT '发送者ID',
  `resource_type` varchar(50) DEFAULT NULL COMMENT '关联资源类型',
  `resource_id` bigint(20) DEFAULT NULL COMMENT '关联资源ID',
  `is_read` tinyint(4) DEFAULT 0 COMMENT '是否已读：0-否，1-是',
  `read_at` timestamp NULL DEFAULT NULL COMMENT '阅读时间',
  `is_deleted` tinyint(4) DEFAULT 0 COMMENT '是否删除：0-否，1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_workspace_id` (`workspace_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  KEY `idx_is_read` (`is_read`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='通知表';

-- 27. 系统配置表
CREATE TABLE `system_configs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_key` varchar(100) NOT NULL COMMENT '配置键',
  `config_value` text COMMENT '配置值',
  `config_type` varchar(20) DEFAULT 'string' COMMENT '配置类型：string，number，boolean，json',
  `description` text COMMENT '配置描述',
  `is_public` tinyint(4) DEFAULT 0 COMMENT '是否公开：0-否，1-是',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_config_key` (`config_key`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统配置表';

-- =============================================
-- 索引优化建议
-- =============================================

-- 复合索引
ALTER TABLE `documents` ADD INDEX `idx_workspace_folder_status` (`workspace_id`, `folder_id`, `is_deleted`);
ALTER TABLE `document_versions` ADD INDEX `idx_document_created` (`document_id`, `created_at`);
ALTER TABLE `comments` ADD INDEX `idx_document_position` (`document_id`, `position`);
ALTER TABLE `collaboration_operations` ADD INDEX `idx_session_timestamp` (`session_id`, `timestamp`);
ALTER TABLE `activity_logs` ADD INDEX `idx_workspace_user_created` (`workspace_id`, `user_id`, `created_at`);

-- =============================================
-- 初始化数据
-- =============================================

-- 插入默认模板分类
INSERT INTO `template_categories` (`workspace_id`, `name`, `description`, `icon`, `is_system`) VALUES
(0, '会议记录', '会议相关的模板', 'el-icon-chat-dot-round', 1),
(0, '项目管理', '项目管理相关的模板', 'el-icon-s-management', 1),
(0, '技术文档', '技术文档相关的模板', 'el-icon-document', 1),
(0, '报告总结', '报告和总结相关的模板', 'el-icon-s-data', 1),
(0, '流程规范', '流程和规范相关的模板', 'el-icon-s-order', 1);

-- 插入默认系统配置
INSERT INTO `system_configs` (`config_key`, `config_value`, `config_type`, `description`, `is_public`) VALUES
('max_file_size', '104857600', 'number', '最大文件上传大小(字节)', 1),
('allowed_file_types', '["jpg","jpeg","png","gif","pdf","doc","docx","xls","xlsx","ppt","pptx","txt","md"]', 'json', '允许上传的文件类型', 1),
('max_workspace_members', '100', 'number', '工作空间最大成员数', 1),
('collaboration_timeout', '300', 'number', '协作会话超时时间(秒)', 1),
('version_retention_days', '365', 'number', '版本保留天数', 1),
('enable_public_sharing', 'true', 'boolean', '是否启用公开分享', 1),
('enable_guest_access', 'false', 'boolean', '是否启用访客访问', 1),
('default_document_permissions', '["read","write","comment"]', 'json', '默认文档权限', 1);

-- =============================================
-- 视图定义
-- =============================================

-- 文档详情视图
CREATE VIEW `v_document_details` AS
SELECT
  d.id,
  d.workspace_id,
  d.folder_id,
  d.title,
  d.content_type,
  d.author_id,
  u1.name as author_name,
  d.template_id,
  t.name as template_name,
  d.word_count,
  d.character_count,
  d.view_count,
  d.edit_count,
  d.comment_count,
  d.like_count,
  d.is_public,
  d.is_favorite,
  d.last_edited_at,
  d.last_editor_id,
  u2.name as last_editor_name,
  d.created_at,
  d.updated_at,
  (SELECT COUNT(*) FROM document_versions dv WHERE dv.document_id = d.id) as version_count,
  (SELECT COUNT(*) FROM collaboration_sessions cs WHERE cs.document_id = d.id AND cs.is_active = 1) as active_collaborations
FROM documents d
LEFT JOIN users u1 ON d.author_id = u1.id
LEFT JOIN users u2 ON d.last_editor_id = u2.id
LEFT JOIN templates t ON d.template_id = t.id
WHERE d.is_deleted = 0;

-- 工作空间统计视图
CREATE VIEW `v_workspace_stats` AS
SELECT
  w.id as workspace_id,
  w.name as workspace_name,
  w.storage_used,
  w.storage_limit,
  (SELECT COUNT(*) FROM workspace_members wm WHERE wm.workspace_id = w.id AND wm.status = 1) as member_count,
  (SELECT COUNT(*) FROM documents d WHERE d.workspace_id = w.id AND d.is_deleted = 0) as document_count,
  (SELECT COUNT(*) FROM templates t WHERE t.workspace_id = w.id AND t.is_deleted = 0) as template_count,
  (SELECT COUNT(*) FROM folders f WHERE f.workspace_id = w.id AND f.is_deleted = 0) as folder_count,
  (SELECT COUNT(*) FROM collaboration_sessions cs
   JOIN documents d ON cs.document_id = d.id
   WHERE d.workspace_id = w.id AND cs.is_active = 1) as active_collaborations,
  w.created_at,
  w.updated_at
FROM workspaces w
WHERE w.status = 1;
