import request from '@/utils/request'

// 获取模板列表
export function getTemplates(workspaceId, query) {
  return request({
    url: `/workspace/${workspaceId}/templates`,
    method: 'get',
    params: query
  })
}

// 获取模板详情
export function getTemplate(workspaceId, templateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}`,
    method: 'get'
  })
}

// 创建模板
export function createTemplate(workspaceId, templateData) {
  return request({
    url: `/workspace/${workspaceId}/templates`,
    method: 'post',
    data: templateData
  })
}

// 更新模板
export function updateTemplate(workspaceId, templateId, templateData) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}`,
    method: 'put',
    data: templateData
  })
}

// 删除模板
export function deleteTemplate(workspaceId, templateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}`,
    method: 'delete'
  })
}

// 复制模板
export function duplicateTemplate(workspaceId, templateId, templateData) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/duplicate`,
    method: 'post',
    data: templateData
  })
}

// 使用模板创建文档
export function createDocumentFromTemplate(workspaceId, templateId, documentData) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/create-document`,
    method: 'post',
    data: documentData
  })
}

// 获取模板分类
export function getTemplateCategories(workspaceId) {
  return request({
    url: `/workspace/${workspaceId}/templates/categories`,
    method: 'get'
  })
}

// 创建模板分类
export function createTemplateCategory(workspaceId, categoryData) {
  return request({
    url: `/workspace/${workspaceId}/templates/categories`,
    method: 'post',
    data: categoryData
  })
}

// 更新模板分类
export function updateTemplateCategory(workspaceId, categoryId, categoryData) {
  return request({
    url: `/workspace/${workspaceId}/templates/categories/${categoryId}`,
    method: 'put',
    data: categoryData
  })
}

// 删除模板分类
export function deleteTemplateCategory(workspaceId, categoryId) {
  return request({
    url: `/workspace/${workspaceId}/templates/categories/${categoryId}`,
    method: 'delete'
  })
}

// 获取模板标签
export function getTemplateTags(workspaceId) {
  return request({
    url: `/workspace/${workspaceId}/templates/tags`,
    method: 'get'
  })
}

// 为模板添加标签
export function addTemplateTag(workspaceId, templateId, tagData) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/tags`,
    method: 'post',
    data: tagData
  })
}

// 移除模板标签
export function removeTemplateTag(workspaceId, templateId, tagId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/tags/${tagId}`,
    method: 'delete'
  })
}

// 搜索模板
export function searchTemplates(workspaceId, searchQuery) {
  return request({
    url: `/workspace/${workspaceId}/templates/search`,
    method: 'get',
    params: searchQuery
  })
}

// 获取热门模板
export function getPopularTemplates(workspaceId, limit = 10) {
  return request({
    url: `/workspace/${workspaceId}/templates/popular`,
    method: 'get',
    params: { limit }
  })
}

// 获取最近使用的模板
export function getRecentTemplates(workspaceId, limit = 10) {
  return request({
    url: `/workspace/${workspaceId}/templates/recent`,
    method: 'get',
    params: { limit }
  })
}

// 收藏模板
export function favoriteTemplate(workspaceId, templateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/favorite`,
    method: 'post'
  })
}

// 取消收藏模板
export function unfavoriteTemplate(workspaceId, templateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/unfavorite`,
    method: 'post'
  })
}

// 获取收藏的模板
export function getFavoriteTemplates(workspaceId) {
  return request({
    url: `/workspace/${workspaceId}/templates/favorites`,
    method: 'get'
  })
}

// 分享模板
export function shareTemplate(workspaceId, templateId, shareData) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/share`,
    method: 'post',
    data: shareData
  })
}

// 取消分享模板
export function unshareTemplate(workspaceId, templateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/unshare`,
    method: 'post'
  })
}

// 获取模板分享信息
export function getTemplateShareInfo(workspaceId, templateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/share`,
    method: 'get'
  })
}

// 导出模板
export function exportTemplate(workspaceId, templateId, format = 'json') {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/export`,
    method: 'get',
    params: { format },
    responseType: 'blob'
  })
}

// 导入模板
export function importTemplate(workspaceId, templateFile) {
  return request({
    url: `/workspace/${workspaceId}/templates/import`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: templateFile
  })
}

// 批量导入模板
export function batchImportTemplates(workspaceId, templatesFile) {
  return request({
    url: `/workspace/${workspaceId}/templates/batch-import`,
    method: 'post',
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    data: templatesFile
  })
}

// 获取模板使用统计
export function getTemplateUsageStats(workspaceId, templateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/stats`,
    method: 'get'
  })
}

// 获取模板评论
export function getTemplateComments(workspaceId, templateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/comments`,
    method: 'get'
  })
}

// 添加模板评论
export function addTemplateComment(workspaceId, templateId, commentData) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/comments`,
    method: 'post',
    data: commentData
  })
}

// 删除模板评论
export function deleteTemplateComment(workspaceId, templateId, commentId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/comments/${commentId}`,
    method: 'delete'
  })
}

// 模板评分
export function rateTemplate(workspaceId, templateId, rating) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/rate`,
    method: 'post',
    data: { rating }
  })
}

// 获取模板评分
export function getTemplateRating(workspaceId, templateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/rating`,
    method: 'get'
  })
}

// 发布模板到公共库
export function publishTemplateToPublic(workspaceId, templateId, publishData) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/publish`,
    method: 'post',
    data: publishData
  })
}

// 从公共库取消发布模板
export function unpublishTemplateFromPublic(workspaceId, templateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/unpublish`,
    method: 'post'
  })
}

// 获取公共模板库
export function getPublicTemplates(query) {
  return request({
    url: '/public/templates',
    method: 'get',
    params: query
  })
}

// 从公共库安装模板
export function installPublicTemplate(workspaceId, publicTemplateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/install/${publicTemplateId}`,
    method: 'post'
  })
}

// 获取模板版本历史
export function getTemplateVersions(workspaceId, templateId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/versions`,
    method: 'get'
  })
}

// 创建模板版本
export function createTemplateVersion(workspaceId, templateId, versionData) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/versions`,
    method: 'post',
    data: versionData
  })
}

// 恢复模板版本
export function restoreTemplateVersion(workspaceId, templateId, versionId) {
  return request({
    url: `/workspace/${workspaceId}/templates/${templateId}/versions/${versionId}/restore`,
    method: 'post'
  })
}
