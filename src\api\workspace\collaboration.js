import request from '@/utils/request'

// 加入协作会话
export function joinCollaboration(workspaceId, documentId, userInfo) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/join`,
    method: 'post',
    data: userInfo
  })
}

// 离开协作会话
export function leaveCollaboration(workspaceId, documentId, sessionId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/leave`,
    method: 'post',
    data: { sessionId }
  })
}

// 获取协作者列表
export function getCollaborators(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/users`,
    method: 'get'
  })
}

// 发送协作操作
export function sendCollaborationOperation(workspaceId, documentId, operation) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/operation`,
    method: 'post',
    data: operation
  })
}

// 获取协作操作历史
export function getCollaborationHistory(workspaceId, documentId, query) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/history`,
    method: 'get',
    params: query
  })
}

// 更新光标位置
export function updateCursorPosition(workspaceId, documentId, cursorData) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/cursor`,
    method: 'put',
    data: cursorData
  })
}

// 获取所有光标位置
export function getCursorPositions(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/cursors`,
    method: 'get'
  })
}

// 添加评论
export function addComment(workspaceId, documentId, commentData) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/comments`,
    method: 'post',
    data: commentData
  })
}

// 获取评论列表
export function getComments(workspaceId, documentId, query) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/comments`,
    method: 'get',
    params: query
  })
}

// 回复评论
export function replyComment(workspaceId, documentId, commentId, replyData) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/comments/${commentId}/replies`,
    method: 'post',
    data: replyData
  })
}

// 删除评论
export function deleteComment(workspaceId, documentId, commentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/comments/${commentId}`,
    method: 'delete'
  })
}

// 更新评论
export function updateComment(workspaceId, documentId, commentId, commentData) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/comments/${commentId}`,
    method: 'put',
    data: commentData
  })
}

// 解决评论
export function resolveComment(workspaceId, documentId, commentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/comments/${commentId}/resolve`,
    method: 'post'
  })
}

// 获取提醒列表
export function getMentions(workspaceId, userId) {
  return request({
    url: `/workspace/${workspaceId}/mentions/${userId}`,
    method: 'get'
  })
}

// 标记提醒为已读
export function markMentionAsRead(workspaceId, mentionId) {
  return request({
    url: `/workspace/${workspaceId}/mentions/${mentionId}/read`,
    method: 'post'
  })
}

// 获取协作统计信息
export function getCollaborationStats(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/stats`,
    method: 'get'
  })
}

// 设置协作权限
export function setCollaborationPermissions(workspaceId, documentId, permissions) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/permissions`,
    method: 'put',
    data: permissions
  })
}

// 获取协作权限
export function getCollaborationPermissions(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/permissions`,
    method: 'get'
  })
}

// 邀请协作者
export function inviteCollaborator(workspaceId, documentId, inviteData) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/invite`,
    method: 'post',
    data: inviteData
  })
}

// 移除协作者
export function removeCollaborator(workspaceId, documentId, userId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/users/${userId}`,
    method: 'delete'
  })
}

// 获取协作会话信息
export function getCollaborationSession(workspaceId, documentId, sessionId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/sessions/${sessionId}`,
    method: 'get'
  })
}

// 创建协作会话
export function createCollaborationSession(workspaceId, documentId, sessionData) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/sessions`,
    method: 'post',
    data: sessionData
  })
}

// 结束协作会话
export function endCollaborationSession(workspaceId, documentId, sessionId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/sessions/${sessionId}/end`,
    method: 'post'
  })
}

// 获取协作冲突
export function getCollaborationConflicts(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/conflicts`,
    method: 'get'
  })
}

// 解决协作冲突
export function resolveCollaborationConflict(workspaceId, documentId, conflictId, resolution) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/conflicts/${conflictId}/resolve`,
    method: 'post',
    data: resolution
  })
}

// 获取协作活动日志
export function getCollaborationActivity(workspaceId, documentId, query) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/activity`,
    method: 'get',
    params: query
  })
}

// 设置协作偏好
export function setCollaborationPreferences(workspaceId, userId, preferences) {
  return request({
    url: `/workspace/${workspaceId}/users/${userId}/collaboration/preferences`,
    method: 'put',
    data: preferences
  })
}

// 获取协作偏好
export function getCollaborationPreferences(workspaceId, userId) {
  return request({
    url: `/workspace/${workspaceId}/users/${userId}/collaboration/preferences`,
    method: 'get'
  })
}

// 同步文档状态
export function syncDocumentState(workspaceId, documentId, stateData) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/sync`,
    method: 'post',
    data: stateData
  })
}

// 获取文档锁定状态
export function getDocumentLockStatus(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/lock`,
    method: 'get'
  })
}

// 锁定文档
export function lockDocument(workspaceId, documentId, lockData) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/lock`,
    method: 'post',
    data: lockData
  })
}

// 解锁文档
export function unlockDocument(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/unlock`,
    method: 'post'
  })
}

// 获取实时协作状态
export function getRealtimeCollaborationStatus(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/realtime/status`,
    method: 'get'
  })
}

// 启用实时协作
export function enableRealtimeCollaboration(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/realtime/enable`,
    method: 'post'
  })
}

// 禁用实时协作
export function disableRealtimeCollaboration(workspaceId, documentId) {
  return request({
    url: `/workspace/${workspaceId}/documents/${documentId}/collaboration/realtime/disable`,
    method: 'post'
  })
}
