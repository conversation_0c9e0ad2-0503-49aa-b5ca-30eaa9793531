# 文件管理 API 接口文档

## 📋 概述

文件管理模块提供文件上传、下载、预览、管理等功能，支持多种文件格式和批量操作。

## 🔗 基础信息

- **基础URL**: `/api/v1/workspace/{workspaceId}/files`
- **认证方式**: Bearer <PERSON>ken
- **数据格式**: JSON/Multipart
- **字符编码**: UTF-8

## 📁 文件基础 API

### 1. 获取文件列表

```http
GET /workspace/{workspaceId}/files
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| folderId | String | 否 | 文件夹ID |
| page | Number | 否 | 页码，默认1 |
| pageSize | Number | 否 | 每页数量，默认20 |
| keyword | String | 否 | 搜索关键词 |
| type | String | 否 | 文件类型：image/document/video/audio |
| sortBy | String | 否 | 排序字段：name/size/modified/created |
| order | String | 否 | 排序方向：asc/desc |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "files": [
      {
        "id": "file_123456",
        "name": "产品设计图.png",
        "originalName": "product_design_v2.png",
        "type": "image",
        "mimeType": "image/png",
        "size": 2048576,
        "folderId": "folder_123",
        "folderPath": "/设计文件/产品图片",
        "url": "https://cdn.example.com/files/file_123456.png",
        "thumbnailUrl": "https://cdn.example.com/thumbnails/file_123456_thumb.png",
        "previewUrl": "https://cdn.example.com/previews/file_123456_preview.png",
        "downloadUrl": "https://api.example.com/files/file_123456/download",
        "uploaderId": "user_123",
        "uploaderName": "张三",
        "uploaderAvatar": "avatar.jpg",
        "isPublic": false,
        "downloadCount": 15,
        "viewCount": 45,
        "tags": ["设计", "产品"],
        "metadata": {
          "width": 1920,
          "height": 1080,
          "format": "PNG",
          "colorSpace": "sRGB"
        },
        "hash": "abc123def456",
        "status": "uploaded",
        "createdAt": "2024-01-20T15:30:00Z",
        "updatedAt": "2024-01-20T15:30:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20,
    "storageUsed": 104857600,
    "storageLimit": 10737418240
  }
}
```

### 2. 获取文件详情

```http
GET /workspace/{workspaceId}/files/{fileId}
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "file_123456",
    "name": "产品设计图.png",
    "originalName": "product_design_v2.png",
    "description": "产品主页设计图第二版",
    "type": "image",
    "mimeType": "image/png",
    "size": 2048576,
    "folderId": "folder_123",
    "folderPath": "/设计文件/产品图片",
    "url": "https://cdn.example.com/files/file_123456.png",
    "thumbnailUrl": "https://cdn.example.com/thumbnails/file_123456_thumb.png",
    "previewUrl": "https://cdn.example.com/previews/file_123456_preview.png",
    "downloadUrl": "https://api.example.com/files/file_123456/download",
    "uploaderId": "user_123",
    "uploaderName": "张三",
    "uploaderAvatar": "avatar.jpg",
    "permissions": {
      "canView": true,
      "canDownload": true,
      "canEdit": false,
      "canDelete": false,
      "canShare": true
    },
    "statistics": {
      "downloadCount": 15,
      "viewCount": 45,
      "shareCount": 3
    },
    "metadata": {
      "width": 1920,
      "height": 1080,
      "format": "PNG",
      "colorSpace": "sRGB",
      "dpi": 72,
      "hasAlpha": true
    },
    "versions": [
      {
        "id": "version_123",
        "version": "1.0",
        "size": 2048576,
        "uploadedAt": "2024-01-20T15:30:00Z"
      }
    ],
    "relatedFiles": [
      {
        "id": "file_789",
        "name": "产品设计图_v1.png",
        "type": "image"
      }
    ],
    "createdAt": "2024-01-20T15:30:00Z",
    "updatedAt": "2024-01-20T15:30:00Z"
  }
}
```

### 3. 上传文件

```http
POST /workspace/{workspaceId}/files/upload
```

**请求体 (multipart/form-data)**

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| file | File | 是 | 文件对象 |
| folderId | String | 否 | 目标文件夹ID |
| description | String | 否 | 文件描述 |
| tags | String | 否 | 标签，逗号分隔 |
| isPublic | Boolean | 否 | 是否公开，默认false |

**响应示例**

```json
{
  "code": 200,
  "message": "文件上传成功",
  "data": {
    "id": "file_789012",
    "name": "新文件.pdf",
    "type": "document",
    "size": 1024000,
    "url": "https://cdn.example.com/files/file_789012.pdf",
    "thumbnailUrl": "https://cdn.example.com/thumbnails/file_789012_thumb.png",
    "uploaderId": "user_123",
    "createdAt": "2024-01-20T16:00:00Z"
  }
}
```

### 4. 分块上传文件

```http
POST /workspace/{workspaceId}/files/upload/chunk
```

**请求体 (multipart/form-data)**

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| chunk | File | 是 | 文件块 |
| uploadId | String | 是 | 上传会话ID |
| chunkIndex | Number | 是 | 块索引 |
| totalChunks | Number | 是 | 总块数 |
| chunkSize | Number | 是 | 块大小 |

### 5. 完成分块上传

```http
POST /workspace/{workspaceId}/files/upload/complete
```

**请求体**

```json
{
  "uploadId": "upload_123456",
  "fileName": "大文件.zip",
  "totalSize": 104857600,
  "folderId": "folder_123",
  "description": "项目文件压缩包"
}
```

### 6. 更新文件信息

```http
PUT /workspace/{workspaceId}/files/{fileId}
```

**请求体**

```json
{
  "name": "新文件名.pdf",
  "description": "更新后的描述",
  "tags": ["新标签"],
  "folderId": "folder_456"
}
```

### 7. 删除文件

```http
DELETE /workspace/{workspaceId}/files/{fileId}
```

**响应示例**

```json
{
  "code": 200,
  "message": "文件删除成功"
}
```

### 8. 批量删除文件

```http
DELETE /workspace/{workspaceId}/files/batch
```

**请求体**

```json
{
  "fileIds": ["file_123", "file_456", "file_789"]
}
```

## 📥 文件下载 API

### 1. 下载文件

```http
GET /workspace/{workspaceId}/files/{fileId}/download
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| version | String | 否 | 版本ID |
| inline | Boolean | 否 | 是否内联显示 |

### 2. 批量下载文件

```http
POST /workspace/{workspaceId}/files/download/batch
```

**请求体**

```json
{
  "fileIds": ["file_123", "file_456", "file_789"],
  "format": "zip",
  "fileName": "批量下载文件.zip"
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "打包成功",
  "data": {
    "downloadUrl": "https://api.example.com/downloads/batch_123456.zip",
    "fileName": "批量下载文件.zip",
    "size": 10485760,
    "expireAt": "2024-01-21T16:00:00Z"
  }
}
```

## 🖼️ 文件预览 API

### 1. 获取预览信息

```http
GET /workspace/{workspaceId}/files/{fileId}/preview
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "fileId": "file_123456",
    "type": "image",
    "previewUrl": "https://cdn.example.com/previews/file_123456_preview.png",
    "thumbnailUrl": "https://cdn.example.com/thumbnails/file_123456_thumb.png",
    "canPreview": true,
    "previewType": "image",
    "metadata": {
      "width": 1920,
      "height": 1080,
      "pages": 1
    },
    "supportedFormats": ["png", "jpg", "webp"]
  }
}
```

### 2. 生成预览

```http
POST /workspace/{workspaceId}/files/{fileId}/preview/generate
```

**请求体**

```json
{
  "format": "png",
  "quality": 80,
  "width": 800,
  "height": 600,
  "page": 1
}
```

## 🔗 文件分享 API

### 1. 创建分享链接

```http
POST /workspace/{workspaceId}/files/{fileId}/share
```

**请求体**

```json
{
  "expireTime": "2024-02-01T00:00:00Z",
  "password": "123456",
  "allowDownload": true,
  "allowPreview": true,
  "downloadLimit": 100
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "分享链接创建成功",
  "data": {
    "shareId": "share_123456",
    "shareUrl": "https://example.com/share/files/share_123456",
    "password": "123456",
    "expireTime": "2024-02-01T00:00:00Z",
    "allowDownload": true,
    "allowPreview": true,
    "downloadLimit": 100,
    "createdAt": "2024-01-20T16:00:00Z"
  }
}
```

### 2. 获取分享信息

```http
GET /workspace/{workspaceId}/files/{fileId}/share
```

### 3. 取消分享

```http
DELETE /workspace/{workspaceId}/files/{fileId}/share
```

## 📊 存储统计 API

### 1. 获取存储统计

```http
GET /workspace/{workspaceId}/storage/stats
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "overview": {
      "totalFiles": 1250,
      "totalSize": 2147483648,
      "storageLimit": 10737418240,
      "usagePercentage": 20.0,
      "availableSpace": 8589934592
    },
    "byType": {
      "images": {
        "count": 450,
        "size": 1073741824,
        "percentage": 50.0
      },
      "documents": {
        "count": 300,
        "size": 536870912,
        "percentage": 25.0
      },
      "videos": {
        "count": 50,
        "size": 429496729,
        "percentage": 20.0
      },
      "others": {
        "count": 450,
        "size": 107374182,
        "percentage": 5.0
      }
    },
    "byFolder": [
      {
        "folderId": "folder_123",
        "folderName": "设计文件",
        "fileCount": 200,
        "size": 536870912,
        "percentage": 25.0
      }
    ],
    "timeline": [
      {
        "date": "2024-01-20",
        "uploadCount": 15,
        "uploadSize": 52428800
      }
    ]
  }
}
```

### 2. 获取文件类型统计

```http
GET /workspace/{workspaceId}/files/statistics/types
```

### 3. 获取上传统计

```http
GET /workspace/{workspaceId}/files/statistics/uploads
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| period | String | 否 | 统计周期：day/week/month |
| dateFrom | String | 否 | 开始日期 |
| dateTo | String | 否 | 结束日期 |

## 🔍 文件搜索 API

### 1. 搜索文件

```http
GET /workspace/{workspaceId}/files/search
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| q | String | 是 | 搜索关键词 |
| type | String | 否 | 文件类型筛选 |
| uploader | String | 否 | 上传者筛选 |
| tags | String | 否 | 标签筛选 |
| sizeMin | Number | 否 | 最小文件大小 |
| sizeMax | Number | 否 | 最大文件大小 |
| dateFrom | String | 否 | 开始日期 |
| dateTo | String | 否 | 结束日期 |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "results": [
      {
        "id": "file_123456",
        "name": "产品设计图.png",
        "type": "image",
        "size": 2048576,
        "uploaderName": "张三",
        "thumbnailUrl": "https://cdn.example.com/thumbnails/file_123456_thumb.png",
        "createdAt": "2024-01-20T15:30:00Z",
        "relevanceScore": 0.95,
        "matchedFields": ["name", "tags"]
      }
    ],
    "total": 25,
    "searchTime": 0.08
  }
}
```

## 🚨 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 文件不存在 |
| 409 | 文件名冲突 |
| 413 | 文件过大 |
| 415 | 不支持的文件类型 |
| 422 | 文件格式错误 |
| 429 | 请求过于频繁 |
| 507 | 存储空间不足 |
| 500 | 服务器内部错误 |

## 📝 使用示例

### JavaScript 文件上传

```javascript
// 单文件上传
const uploadFile = async (workspaceId, file, options = {}) => {
  const formData = new FormData()
  formData.append('file', file)
  
  if (options.folderId) formData.append('folderId', options.folderId)
  if (options.description) formData.append('description', options.description)
  if (options.tags) formData.append('tags', options.tags.join(','))
  
  try {
    const response = await axios.post(
      `/api/v1/workspace/${workspaceId}/files/upload`,
      formData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'multipart/form-data'
        },
        onUploadProgress: (progressEvent) => {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          console.log(`上传进度: ${progress}%`)
        }
      }
    )
    return response.data
  } catch (error) {
    console.error('文件上传失败:', error)
    throw error
  }
}

// 分块上传大文件
const uploadLargeFile = async (workspaceId, file, options = {}) => {
  const chunkSize = 1024 * 1024 * 5 // 5MB per chunk
  const totalChunks = Math.ceil(file.size / chunkSize)
  const uploadId = 'upload_' + Date.now()
  
  for (let i = 0; i < totalChunks; i++) {
    const start = i * chunkSize
    const end = Math.min(start + chunkSize, file.size)
    const chunk = file.slice(start, end)
    
    const formData = new FormData()
    formData.append('chunk', chunk)
    formData.append('uploadId', uploadId)
    formData.append('chunkIndex', i)
    formData.append('totalChunks', totalChunks)
    formData.append('chunkSize', chunk.size)
    
    await axios.post(`/api/v1/workspace/${workspaceId}/files/upload/chunk`, formData)
  }
  
  // 完成上传
  const response = await axios.post(`/api/v1/workspace/${workspaceId}/files/upload/complete`, {
    uploadId,
    fileName: file.name,
    totalSize: file.size,
    ...options
  })
  
  return response.data
}
```
