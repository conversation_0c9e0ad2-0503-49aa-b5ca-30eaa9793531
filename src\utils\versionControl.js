/**
 * 版本控制系统
 * 支持历史版本管理、对比、回滚、变更记录等功能
 */

class VersionControlService {
  constructor() {
    this.documents = new Map() // 文档版本存储
    this.maxVersions = 50 // 最大版本数量
  }

  /**
   * 初始化文档版本控制
   * @param {string} documentId 文档ID
   * @param {string} initialContent 初始内容
   * @param {object} author 作者信息
   */
  initDocument(documentId, initialContent = '', author) {
    if (this.documents.has(documentId)) {
      return this.documents.get(documentId)
    }

    const document = {
      id: documentId,
      versions: [],
      currentVersion: null,
      branches: new Map(),
      tags: new Map()
    }

    // 创建初始版本
    const initialVersion = this.createVersion(documentId, {
      content: initialContent,
      title: '初始版本',
      author: author || { id: 'system', name: '系统' },
      message: '文档创建'
    })

    document.currentVersion = initialVersion.id
    this.documents.set(documentId, document)

    return document
  }

  /**
   * 创建新版本
   * @param {string} documentId 文档ID
   * @param {object} versionData 版本数据
   * @returns {object} 新版本
   */
  createVersion(documentId, versionData) {
    const document = this.documents.get(documentId)
    if (!document) {
      throw new Error('Document not found')
    }

    const previousVersion = this.getCurrentVersion(documentId)
    const versionNumber = document.versions.length + 1

    const version = {
      id: `v${versionNumber}_${Date.now()}`,
      number: versionNumber,
      title: versionData.title || `版本 ${versionNumber}`,
      content: versionData.content,
      author: versionData.author,
      message: versionData.message || '',
      createdAt: new Date().toISOString(),
      parentId: previousVersion ? previousVersion.id : null,
      changes: previousVersion ? this.calculateChanges(previousVersion.content, versionData.content) : [],
      metadata: versionData.metadata || {},
      size: new Blob([versionData.content]).size,
      hash: this.generateHash(versionData.content)
    }

    document.versions.push(version)
    document.currentVersion = version.id

    // 限制版本数量
    if (document.versions.length > this.maxVersions) {
      document.versions.shift()
    }

    return version
  }

  /**
   * 获取当前版本
   * @param {string} documentId 文档ID
   * @returns {object|null} 当前版本
   */
  getCurrentVersion(documentId) {
    const document = this.documents.get(documentId)
    if (!document || !document.currentVersion) return null

    return document.versions.find(v => v.id === document.currentVersion)
  }

  /**
   * 获取版本列表
   * @param {string} documentId 文档ID
   * @param {object} options 选项
   * @returns {Array} 版本列表
   */
  getVersions(documentId, options = {}) {
    const document = this.documents.get(documentId)
    if (!document) return []

    let versions = [...document.versions]

    // 排序
    if (options.sortBy === 'date') {
      versions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    } else if (options.sortBy === 'number') {
      versions.sort((a, b) => b.number - a.number)
    }

    // 分页
    if (options.page && options.pageSize) {
      const start = (options.page - 1) * options.pageSize
      const end = start + options.pageSize
      versions = versions.slice(start, end)
    }

    // 限制数量
    if (options.limit) {
      versions = versions.slice(0, options.limit)
    }

    return versions
  }

  /**
   * 获取指定版本
   * @param {string} documentId 文档ID
   * @param {string} versionId 版本ID
   * @returns {object|null} 版本信息
   */
  getVersion(documentId, versionId) {
    const document = this.documents.get(documentId)
    if (!document) return null

    return document.versions.find(v => v.id === versionId)
  }

  /**
   * 回滚到指定版本
   * @param {string} documentId 文档ID
   * @param {string} versionId 版本ID
   * @param {object} author 操作者信息
   * @returns {object} 新版本
   */
  rollbackToVersion(documentId, versionId, author) {
    const targetVersion = this.getVersion(documentId, versionId)
    if (!targetVersion) {
      throw new Error('Target version not found')
    }

    const rollbackVersion = this.createVersion(documentId, {
      content: targetVersion.content,
      title: `回滚到版本 ${targetVersion.number}`,
      author,
      message: `回滚到版本 ${targetVersion.number} (${targetVersion.title})`,
      metadata: {
        isRollback: true,
        rollbackFrom: targetVersion.id
      }
    })

    return rollbackVersion
  }

  /**
   * 比较两个版本
   * @param {string} documentId 文档ID
   * @param {string} versionId1 版本1 ID
   * @param {string} versionId2 版本2 ID
   * @returns {object} 比较结果
   */
  compareVersions(documentId, versionId1, versionId2) {
    const version1 = this.getVersion(documentId, versionId1)
    const version2 = this.getVersion(documentId, versionId2)

    if (!version1 || !version2) {
      throw new Error('One or both versions not found')
    }

    const changes = this.calculateChanges(version1.content, version2.content)
    
    return {
      version1,
      version2,
      changes,
      summary: this.generateChangeSummary(changes)
    }
  }

  /**
   * 计算内容变更
   * @param {string} oldContent 旧内容
   * @param {string} newContent 新内容
   * @returns {Array} 变更列表
   */
  calculateChanges(oldContent, newContent) {
    const oldLines = oldContent.split('\n')
    const newLines = newContent.split('\n')
    const changes = []

    // 简单的行级别差异算法
    let oldIndex = 0
    let newIndex = 0

    while (oldIndex < oldLines.length || newIndex < newLines.length) {
      if (oldIndex >= oldLines.length) {
        // 新增行
        changes.push({
          type: 'add',
          lineNumber: newIndex + 1,
          content: newLines[newIndex],
          oldLineNumber: null
        })
        newIndex++
      } else if (newIndex >= newLines.length) {
        // 删除行
        changes.push({
          type: 'delete',
          lineNumber: null,
          content: oldLines[oldIndex],
          oldLineNumber: oldIndex + 1
        })
        oldIndex++
      } else if (oldLines[oldIndex] === newLines[newIndex]) {
        // 相同行
        changes.push({
          type: 'unchanged',
          lineNumber: newIndex + 1,
          content: newLines[newIndex],
          oldLineNumber: oldIndex + 1
        })
        oldIndex++
        newIndex++
      } else {
        // 修改行
        changes.push({
          type: 'modify',
          lineNumber: newIndex + 1,
          content: newLines[newIndex],
          oldContent: oldLines[oldIndex],
          oldLineNumber: oldIndex + 1
        })
        oldIndex++
        newIndex++
      }
    }

    return changes
  }

  /**
   * 生成变更摘要
   * @param {Array} changes 变更列表
   * @returns {object} 变更摘要
   */
  generateChangeSummary(changes) {
    const summary = {
      totalChanges: 0,
      additions: 0,
      deletions: 0,
      modifications: 0
    }

    changes.forEach(change => {
      switch (change.type) {
        case 'add':
          summary.additions++
          summary.totalChanges++
          break
        case 'delete':
          summary.deletions++
          summary.totalChanges++
          break
        case 'modify':
          summary.modifications++
          summary.totalChanges++
          break
      }
    })

    return summary
  }

  /**
   * 创建分支
   * @param {string} documentId 文档ID
   * @param {string} branchName 分支名称
   * @param {string} fromVersionId 起始版本ID
   * @returns {object} 分支信息
   */
  createBranch(documentId, branchName, fromVersionId) {
    const document = this.documents.get(documentId)
    if (!document) {
      throw new Error('Document not found')
    }

    if (document.branches.has(branchName)) {
      throw new Error('Branch already exists')
    }

    const fromVersion = this.getVersion(documentId, fromVersionId)
    if (!fromVersion) {
      throw new Error('Source version not found')
    }

    const branch = {
      name: branchName,
      createdAt: new Date().toISOString(),
      fromVersionId,
      currentVersionId: fromVersionId
    }

    document.branches.set(branchName, branch)
    return branch
  }

  /**
   * 创建标签
   * @param {string} documentId 文档ID
   * @param {string} tagName 标签名称
   * @param {string} versionId 版本ID
   * @param {string} description 描述
   * @returns {object} 标签信息
   */
  createTag(documentId, tagName, versionId, description = '') {
    const document = this.documents.get(documentId)
    if (!document) {
      throw new Error('Document not found')
    }

    if (document.tags.has(tagName)) {
      throw new Error('Tag already exists')
    }

    const version = this.getVersion(documentId, versionId)
    if (!version) {
      throw new Error('Version not found')
    }

    const tag = {
      name: tagName,
      versionId,
      description,
      createdAt: new Date().toISOString()
    }

    document.tags.set(tagName, tag)
    return tag
  }

  /**
   * 获取文档统计信息
   * @param {string} documentId 文档ID
   * @returns {object} 统计信息
   */
  getDocumentStats(documentId) {
    const document = this.documents.get(documentId)
    if (!document) return null

    const currentVersion = this.getCurrentVersion(documentId)
    const versions = document.versions

    return {
      totalVersions: versions.length,
      currentVersion: currentVersion ? currentVersion.number : 0,
      totalSize: versions.reduce((sum, v) => sum + v.size, 0),
      authors: [...new Set(versions.map(v => v.author.id))].length,
      branches: document.branches.size,
      tags: document.tags.size,
      createdAt: versions[0] ? versions[0].createdAt : null,
      lastModified: currentVersion ? currentVersion.createdAt : null
    }
  }

  /**
   * 生成内容哈希
   * @param {string} content 内容
   * @returns {string} 哈希值
   */
  generateHash(content) {
    let hash = 0
    if (content.length === 0) return hash.toString()
    
    for (let i = 0; i < content.length; i++) {
      const char = content.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // 转换为32位整数
    }
    
    return Math.abs(hash).toString(16)
  }

  /**
   * 清理旧版本
   * @param {string} documentId 文档ID
   * @param {number} keepCount 保留版本数量
   */
  cleanupVersions(documentId, keepCount = 10) {
    const document = this.documents.get(documentId)
    if (!document) return

    if (document.versions.length > keepCount) {
      const versionsToRemove = document.versions.length - keepCount
      document.versions.splice(0, versionsToRemove)
    }
  }

  /**
   * 导出版本历史
   * @param {string} documentId 文档ID
   * @returns {object} 导出数据
   */
  exportHistory(documentId) {
    const document = this.documents.get(documentId)
    if (!document) return null

    return {
      documentId,
      versions: document.versions,
      branches: Object.fromEntries(document.branches),
      tags: Object.fromEntries(document.tags),
      currentVersion: document.currentVersion,
      exportedAt: new Date().toISOString()
    }
  }

  /**
   * 导入版本历史
   * @param {object} historyData 历史数据
   */
  importHistory(historyData) {
    const document = {
      id: historyData.documentId,
      versions: historyData.versions,
      currentVersion: historyData.currentVersion,
      branches: new Map(Object.entries(historyData.branches || {})),
      tags: new Map(Object.entries(historyData.tags || {}))
    }

    this.documents.set(historyData.documentId, document)
  }
}

// 创建单例实例
const versionControlService = new VersionControlService()

export default versionControlService
