/**
 * 增强的协作服务
 * 支持实时协同编辑、光标位置同步、评论@提醒等功能
 */

class EnhancedCollaborationService {
  constructor() {
    this.connections = new Map()
    this.documents = new Map()
    this.cursors = new Map()
    this.comments = new Map()
    this.eventListeners = new Map()
    this.isConnected = false
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
  }

  /**
   * 连接到协作服务
   * @param {string} documentId 文档ID
   * @param {object} userInfo 用户信息
   * @returns {string} 连接ID
   */
  connect (documentId, userInfo) {
    const connectionId = `${documentId}_${userInfo.id}_${Date.now()}`

    const connection = {
      id: connectionId,
      documentId,
      userInfo,
      lastActivity: Date.now(),
      isActive: true,
      cursor: { position: 0, selection: null }
    }

    this.connections.set(connectionId, connection)

    // 初始化文档
    if (!this.documents.has(documentId)) {
      this.documents.set(documentId, {
        id: documentId,
        content: '',
        version: 0,
        operations: [],
        collaborators: new Set(),
        cursors: new Map(),
        comments: []
      })
    }

    const document = this.documents.get(documentId)
    document.collaborators.add(connectionId)
    document.cursors.set(connectionId, connection.cursor)

    // 初始化光标映射
    if (!this.cursors.has(documentId)) {
      this.cursors.set(documentId, new Map())
    }
    this.cursors.get(documentId).set(connectionId, connection.cursor)

    // 初始化评论映射
    if (!this.comments.has(documentId)) {
      this.comments.set(documentId, [])
    }

    this.isConnected = true
    this.reconnectAttempts = 0

    // 通知其他用户有新用户加入
    this.broadcastUserEvent(documentId, 'userJoined', userInfo, connectionId)

    return connectionId
  }

  /**
   * 断开连接
   * @param {string} connectionId 连接ID
   */
  disconnect (connectionId) {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const { documentId, userInfo } = connection
    const document = this.documents.get(documentId)

    if (document) {
      document.collaborators.delete(connectionId)
      document.cursors.delete(connectionId)
    }

    if (this.cursors.has(documentId)) {
      this.cursors.get(documentId).delete(connectionId)
    }

    this.connections.delete(connectionId)

    // 通知其他用户有用户离开
    this.broadcastUserEvent(documentId, 'userLeft', userInfo, connectionId)
  }

  /**
   * 发送操作
   * @param {string} connectionId 连接ID
   * @param {object} operation 操作信息
   */
  sendOperation (connectionId, operation) {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const { documentId } = connection
    const document = this.documents.get(documentId)
    if (!document) return

    // 更新文档版本
    document.version++
    const versionedOperation = {
      ...operation,
      version: document.version,
      timestamp: Date.now(),
      userId: connection.userInfo.id,
      connectionId
    }

    document.operations.push(versionedOperation)

    // 应用操作到文档内容
    this.applyOperation(documentId, versionedOperation)

    // 广播操作到其他协作者
    this.broadcastOperation(documentId, versionedOperation, connectionId)
  }

  /**
   * 更新光标位置
   * @param {string} connectionId 连接ID
   * @param {object} cursor 光标信息
   */
  updateCursor (connectionId, cursor) {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const { documentId } = connection
    connection.cursor = cursor
    connection.lastActivity = Date.now()

    // 更新文档中的光标信息
    const document = this.documents.get(documentId)
    if (document) {
      document.cursors.set(connectionId, cursor)
    }

    if (this.cursors.has(documentId)) {
      this.cursors.get(documentId).set(connectionId, cursor)
    }

    // 广播光标位置到其他协作者
    this.broadcastCursor(documentId, connectionId, cursor)
  }

  /**
   * 添加评论
   * @param {string} connectionId 连接ID
   * @param {object} comment 评论信息
   */
  addComment (connectionId, comment) {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const { documentId, userInfo } = connection
    const document = this.documents.get(documentId)
    if (!document) return

    const newComment = {
      id: Date.now().toString(),
      content: comment.content,
      position: comment.position,
      author: userInfo,
      createdAt: new Date().toISOString(),
      mentions: this.extractMentions(comment.content),
      replies: []
    }

    document.comments.push(newComment)

    if (!this.comments.has(documentId)) {
      this.comments.set(documentId, [])
    }
    this.comments.get(documentId).push(newComment)

    // 处理@提醒
    this.handleMentions(documentId, newComment)

    // 广播新评论
    this.broadcastComment(documentId, newComment, connectionId)

    return newComment
  }

  /**
   * 回复评论
   * @param {string} connectionId 连接ID
   * @param {string} commentId 评论ID
   * @param {object} reply 回复内容
   */
  replyComment (connectionId, commentId, reply) {
    const connection = this.connections.get(connectionId)
    if (!connection) return

    const { documentId, userInfo } = connection
    const document = this.documents.get(documentId)
    if (!document) return

    const comment = document.comments.find(c => c.id === commentId)
    if (!comment) return

    const newReply = {
      id: Date.now().toString(),
      content: reply.content,
      author: userInfo,
      createdAt: new Date().toISOString(),
      mentions: this.extractMentions(reply.content)
    }

    comment.replies.push(newReply)

    // 处理@提醒
    this.handleMentions(documentId, newReply)

    // 广播回复
    this.broadcastCommentReply(documentId, commentId, newReply, connectionId)

    return newReply
  }

  /**
   * 获取协作者列表
   * @param {string} documentId 文档ID
   * @returns {Array} 协作者列表
   */
  getCollaborators (documentId) {
    const document = this.documents.get(documentId)
    if (!document) return []

    const collaborators = []
    document.collaborators.forEach(connectionId => {
      const connection = this.connections.get(connectionId)
      if (connection && connection.isActive) {
        collaborators.push({
          ...connection.userInfo,
          connectionId,
          cursor: connection.cursor,
          lastActivity: connection.lastActivity,
          isActive: Date.now() - connection.lastActivity < 30000 // 30秒内活跃
        })
      }
    })

    return collaborators
  }

  /**
   * 获取光标位置
   * @param {string} documentId 文档ID
   * @returns {Map} 光标位置映射
   */
  getCursors (documentId) {
    return this.cursors.get(documentId) || new Map()
  }

  /**
   * 获取评论列表
   * @param {string} documentId 文档ID
   * @returns {Array} 评论列表
   */
  getComments (documentId) {
    return this.comments.get(documentId) || []
  }

  /**
   * 监听事件
   * @param {string} connectionId 连接ID
   * @param {string} event 事件名称
   * @param {Function} callback 回调函数
   */
  on (connectionId, event, callback) {
    if (!this.eventListeners.has(connectionId)) {
      this.eventListeners.set(connectionId, new Map())
    }

    const listeners = this.eventListeners.get(connectionId)
    if (!listeners.has(event)) {
      listeners.set(event, [])
    }

    listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听
   * @param {string} connectionId 连接ID
   * @param {string} event 事件名称
   * @param {Function} callback 回调函数
   */
  off (connectionId, event, callback) {
    const listeners = this.eventListeners.get(connectionId)
    if (!listeners || !listeners.has(event)) return

    const eventListeners = listeners.get(event)
    const index = eventListeners.indexOf(callback)
    if (index > -1) {
      eventListeners.splice(index, 1)
    }
  }

  /**
   * 触发事件
   * @param {string} connectionId 连接ID
   * @param {string} event 事件名称
   * @param {*} data 事件数据
   */
  emit (connectionId, event, data) {
    const listeners = this.eventListeners.get(connectionId)
    if (!listeners || !listeners.has(event)) return

    listeners.get(event).forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('Event listener error:', error)
      }
    })
  }

  /**
   * 更新用户活动时间
   * @param {string} connectionId 连接ID
   */
  updateActivity (connectionId) {
    const connection = this.connections.get(connectionId)
    if (connection) {
      connection.lastActivity = Date.now()
    }
  }

  /**
   * 应用操作到文档内容
   * @param {string} documentId 文档ID
   * @param {object} operation 操作信息
   */
  applyOperation (documentId, operation) {
    const document = this.documents.get(documentId)
    if (!document) return

    switch (operation.type) {
      case 'content-change':
        document.content = operation.content
        break
      case 'insert':
        document.content = document.content.slice(0, operation.position) +
          operation.content +
          document.content.slice(operation.position)
        break
      case 'delete':
        document.content = document.content.slice(0, operation.position) +
          document.content.slice(operation.position + operation.length)
        break
    }
  }

  /**
   * 广播操作到其他协作者
   * @param {string} documentId 文档ID
   * @param {object} operation 操作信息
   * @param {string} excludeConnectionId 排除的连接ID
   */
  broadcastOperation (documentId, operation, excludeConnectionId) {
    const document = this.documents.get(documentId)
    if (!document) return

    document.collaborators.forEach(connectionId => {
      if (connectionId !== excludeConnectionId) {
        this.emit(connectionId, 'operation', operation)
      }
    })
  }

  /**
   * 广播光标位置
   * @param {string} documentId 文档ID
   * @param {string} connectionId 连接ID
   * @param {object} cursor 光标信息
   */
  broadcastCursor (documentId, connectionId, cursor) {
    const document = this.documents.get(documentId)
    if (!document) return

    const connection = this.connections.get(connectionId)
    if (!connection) return

    const cursorData = {
      connectionId,
      userInfo: connection.userInfo,
      cursor
    }

    document.collaborators.forEach(id => {
      if (id !== connectionId) {
        this.emit(id, 'cursorUpdate', cursorData)
      }
    })
  }

  /**
   * 广播评论
   * @param {string} documentId 文档ID
   * @param {object} comment 评论信息
   * @param {string} excludeConnectionId 排除的连接ID
   */
  broadcastComment (documentId, comment, excludeConnectionId) {
    const document = this.documents.get(documentId)
    if (!document) return

    document.collaborators.forEach(connectionId => {
      if (connectionId !== excludeConnectionId) {
        this.emit(connectionId, 'commentAdded', comment)
      }
    })
  }

  /**
   * 广播评论回复
   * @param {string} documentId 文档ID
   * @param {string} commentId 评论ID
   * @param {object} reply 回复信息
   * @param {string} excludeConnectionId 排除的连接ID
   */
  broadcastCommentReply (documentId, commentId, reply, excludeConnectionId) {
    const document = this.documents.get(documentId)
    if (!document) return

    const replyData = { commentId, reply }

    document.collaborators.forEach(connectionId => {
      if (connectionId !== excludeConnectionId) {
        this.emit(connectionId, 'commentReply', replyData)
      }
    })
  }

  /**
   * 广播用户事件
   * @param {string} documentId 文档ID
   * @param {string} event 事件类型
   * @param {object} userInfo 用户信息
   * @param {string} excludeConnectionId 排除的连接ID
   */
  broadcastUserEvent (documentId, event, userInfo, excludeConnectionId) {
    const document = this.documents.get(documentId)
    if (!document) return

    document.collaborators.forEach(connectionId => {
      if (connectionId !== excludeConnectionId) {
        this.emit(connectionId, event, userInfo)
      }
    })
  }

  /**
   * 提取@提醒
   * @param {string} content 内容
   * @returns {Array} 提醒的用户列表
   */
  extractMentions (content) {
    const mentionRegex = /@(\w+)/g
    const mentions = []
    let match

    while ((match = mentionRegex.exec(content)) !== null) {
      mentions.push(match[1])
    }

    return mentions
  }

  /**
   * 处理@提醒
   * @param {string} documentId 文档ID
   * @param {object} comment 评论或回复
   */
  handleMentions (documentId, comment) {
    if (!comment.mentions || comment.mentions.length === 0) return

    const document = this.documents.get(documentId)
    if (!document) return

    // 查找被提醒的用户
    const mentionedUsers = []
    document.collaborators.forEach(connectionId => {
      const connection = this.connections.get(connectionId)
      if (connection && comment.mentions.includes(connection.userInfo.nickname)) {
        mentionedUsers.push(connection)
      }
    })

    // 发送提醒通知
    mentionedUsers.forEach(connection => {
      this.emit(connection.id, 'mention', {
        comment,
        mentionedBy: comment.author
      })
    })
  }

  /**
   * 获取文档状态
   * @param {string} documentId 文档ID
   * @returns {object} 文档状态
   */
  getDocumentStatus (documentId) {
    const document = this.documents.get(documentId)
    if (!document) return null

    return {
      id: documentId,
      version: document.version,
      collaboratorCount: document.collaborators.size,
      lastActivity: Math.max(...Array.from(document.collaborators).map(id => {
        const connection = this.connections.get(id)
        return connection ? connection.lastActivity : 0
      }))
    }
  }

  /**
   * 清理过期连接
   */
  cleanup () {
    const now = Date.now()
    const timeout = 5 * 60 * 1000 // 5分钟超时

    this.connections.forEach((connection, connectionId) => {
      if (now - connection.lastActivity > timeout) {
        this.disconnect(connectionId)
      }
    })
  }

  /**
   * 获取连接状态
   * @param {string} connectionId 连接ID
   * @returns {object} 连接状态
   */
  getConnectionStatus (connectionId) {
    const connection = this.connections.get(connectionId)
    if (!connection) return null

    return {
      isConnected: true,
      documentId: connection.documentId,
      userId: connection.userInfo.id,
      userName: connection.userInfo.nickname,
      lastActivity: connection.lastActivity,
      isActive: connection.isActive
    }
  }
}

// 创建单例实例
const enhancedCollaborationService = new EnhancedCollaborationService()

// 定期清理过期连接
setInterval(() => {
  enhancedCollaborationService.cleanup()
}, 60000) // 每分钟清理一次

export default enhancedCollaborationService
