# 模块化数据库设计说明

## 📋 概述

本文档介绍工作空间文档管理系统的模块化数据库设计。采用模块化架构，每个功能模块独立设计，便于维护、扩展和部署。

## 🏗️ 模块化架构优势

### 1. 独立性 (Independence)
- 每个模块可以独立开发、测试和部署
- 模块间通过标准接口通信
- 降低模块间的耦合度

### 2. 可扩展性 (Scalability)
- 可以根据需要单独扩展某个模块
- 支持微服务架构
- 便于水平扩展

### 3. 可维护性 (Maintainability)
- 模块职责清晰，便于理解和维护
- 问题定位更加精确
- 代码复用性更高

### 4. 灵活性 (Flexibility)
- 可以选择性部署某些模块
- 支持不同的技术栈
- 便于功能定制

## 📦 模块划分

### 1. 核心工作空间模块 (Core Workspace Module)
**文件**: `模块化数据库设计.sql`

**功能范围**:
- 工作空间管理
- 成员管理
- 文件夹结构
- 文档基础管理
- 权限控制
- 文件上传
- 系统支持

**核心表**:
```sql
workspaces              -- 工作空间主表
workspace_members       -- 工作空间成员表
folders                 -- 文件夹表
documents              -- 文档表
document_permissions   -- 文档权限表
document_shares        -- 文档分享表
document_favorites     -- 文档收藏表
document_access_logs   -- 文档访问记录表
document_statistics    -- 文档统计表
file_uploads           -- 文件上传表
activity_logs          -- 活动日志表
notifications          -- 通知表
system_configs         -- 系统配置表
```

### 2. 版本控制模块 (Version Control Module)
**文件**: `版本控制模块.sql`

**功能范围**:
- 版本管理
- 变更追踪
- 分支管理
- 标签管理
- 合并请求
- 版本对比
- 权限控制
- 审核发布

**核心表**:
```sql
vc_resources           -- 版本控制资源表
vc_versions           -- 版本表
vc_changes            -- 版本变更记录表
vc_tags               -- 版本标签表
vc_branches           -- 分支表
vc_merge_requests     -- 合并请求表
vc_comparisons        -- 版本比较表
vc_permissions        -- 版本权限表
vc_reviews            -- 版本审核表
vc_releases           -- 版本发布表
```

### 3. 协作模块 (Collaboration Module)
**文件**: `协作模块.sql`

**功能范围**:
- 实时协作会话
- 参与者管理
- 操作同步
- 冲突解决
- 协作快照
- 邀请管理
- 权限控制
- 统计分析

**核心表**:
```sql
collab_sessions        -- 协作会话表
collab_participants    -- 协作参与者表
collab_operations      -- 协作操作记录表
collab_conflicts       -- 协作冲突表
collab_snapshots       -- 协作快照表
collab_invitations     -- 协作邀请表
collab_permissions     -- 协作权限表
collab_statistics      -- 协作统计表
```

### 4. 评论系统模块 (Comment System Module)
**文件**: `评论系统模块.sql`

**功能范围**:
- 评论管理
- 回复系统
- 提醒机制
- 反应系统
- 附件支持
- 权限控制
- 审核管理
- 举报处理

**核心表**:
```sql
comments               -- 评论表
comment_reactions      -- 评论反应表
mentions              -- 提醒记录表
comment_attachments   -- 评论附件表
comment_tags          -- 评论标签表
comment_permissions   -- 评论权限表
comment_moderations   -- 评论审核表
comment_reports       -- 评论举报表
comment_statistics    -- 评论统计表
```

### 5. 模板系统模块 (Template System Module)
**文件**: `模板系统模块.sql`

**功能范围**:
- 模板管理
- 分类体系
- 版本控制
- 评论评分
- 收藏分享
- 使用统计
- 权限控制
- 商业化支持

**核心表**:
```sql
templates              -- 模板表
template_categories    -- 模板分类表
template_tags         -- 模板标签表
template_versions     -- 模板版本表
template_comments     -- 模板评论表
template_favorites    -- 模板收藏表
template_usage_logs   -- 模板使用记录表
template_shares       -- 模板分享表
template_permissions  -- 模板权限表
template_statistics   -- 模板统计表
```

## 🔗 模块间关系

### 1. 核心依赖关系

```mermaid
graph TD
    A[核心工作空间模块] --> B[版本控制模块]
    A --> C[协作模块]
    A --> D[评论系统模块]
    A --> E[模板系统模块]
    
    B --> C
    C --> D
    E --> B
```

### 2. 数据关联

**通过资源标识关联**:
```sql
-- 版本控制模块关联文档
vc_resources.resource_type = 'document'
vc_resources.resource_id = documents.id

-- 协作模块关联文档
collab_sessions.resource_type = 'document'
collab_sessions.resource_id = documents.id

-- 评论系统关联文档
comments.resource_type = 'document'
comments.resource_id = documents.id
```

**通过工作空间关联**:
```sql
-- 所有模块都通过workspace_id关联
vc_resources.workspace_id = workspaces.id
collab_sessions.workspace_id = workspaces.id
comments.workspace_id = workspaces.id
templates.workspace_id = workspaces.id
```

## 🚀 部署策略

### 1. 单体部署 (Monolithic Deployment)
**适用场景**: 小型项目、快速原型

```sql
-- 创建所有模块的表
SOURCE 模块化数据库设计.sql;
SOURCE 版本控制模块.sql;
SOURCE 协作模块.sql;
SOURCE 评论系统模块.sql;
SOURCE 模板系统模块.sql;
```

### 2. 微服务部署 (Microservices Deployment)
**适用场景**: 大型项目、高并发

```yaml
# docker-compose.yml
services:
  workspace-db:
    image: mysql:8.0
    volumes:
      - ./模块化数据库设计.sql:/docker-entrypoint-initdb.d/
  
  version-control-db:
    image: mysql:8.0
    volumes:
      - ./版本控制模块.sql:/docker-entrypoint-initdb.d/
  
  collaboration-db:
    image: mysql:8.0
    volumes:
      - ./协作模块.sql:/docker-entrypoint-initdb.d/
```

### 3. 混合部署 (Hybrid Deployment)
**适用场景**: 中型项目、渐进式架构

```sql
-- 核心模块使用主数据库
-- 高频模块使用独立数据库
```

## 📊 性能优化策略

### 1. 分库分表

**按模块分库**:
```sql
-- 核心数据库
CREATE DATABASE workspace_core;

-- 版本控制数据库
CREATE DATABASE workspace_version;

-- 协作数据库
CREATE DATABASE workspace_collab;
```

**按业务分表**:
```sql
-- 按时间分表
CREATE TABLE collab_operations_202401 LIKE collab_operations;
CREATE TABLE collab_operations_202402 LIKE collab_operations;

-- 按工作空间分表
CREATE TABLE documents_ws_1 LIKE documents;
CREATE TABLE documents_ws_2 LIKE documents;
```

### 2. 读写分离

```yaml
# 主从配置
master:
  host: master.db.local
  port: 3306
  
slaves:
  - host: slave1.db.local
    port: 3306
  - host: slave2.db.local
    port: 3306
```

### 3. 缓存策略

```redis
# 热点数据缓存
workspace:1:info
document:123:content
template:456:details
collab:session:789
```

## 🔒 安全设计

### 1. 数据隔离

**工作空间级别隔离**:
```sql
-- 所有查询都必须包含workspace_id
SELECT * FROM documents WHERE workspace_id = ? AND id = ?;
```

**用户级别隔离**:
```sql
-- 通过权限表控制访问
SELECT d.* FROM documents d
JOIN document_permissions p ON d.id = p.document_id
WHERE p.user_id = ? AND p.permission_type = 'read';
```

### 2. 审计日志

```sql
-- 统一的审计日志格式
INSERT INTO activity_logs (
  workspace_id, user_id, action, resource_type, resource_id
) VALUES (?, ?, ?, ?, ?);
```

### 3. 数据加密

```sql
-- 敏感字段加密
ALTER TABLE documents ADD COLUMN content_encrypted LONGBLOB;
ALTER TABLE templates ADD COLUMN content_encrypted LONGBLOB;
```

## 📈 监控指标

### 1. 业务指标

```sql
-- 活跃用户数
SELECT COUNT(DISTINCT user_id) FROM activity_logs 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 DAY);

-- 协作会话数
SELECT COUNT(*) FROM collab_sessions WHERE is_active = 1;

-- 版本创建频率
SELECT COUNT(*) FROM vc_versions 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR);
```

### 2. 性能指标

```sql
-- 慢查询监控
SELECT * FROM mysql.slow_log WHERE start_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR);

-- 表大小监控
SELECT table_name, ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size in MB'
FROM information_schema.tables WHERE table_schema = 'workspace';
```

## 🔧 维护建议

### 1. 定期维护

```sql
-- 清理过期数据
DELETE FROM collab_sessions WHERE ended_at < DATE_SUB(NOW(), INTERVAL 30 DAY);
DELETE FROM activity_logs WHERE created_at < DATE_SUB(NOW(), INTERVAL 90 DAY);

-- 优化表结构
OPTIMIZE TABLE documents;
ANALYZE TABLE vc_versions;
```

### 2. 备份策略

```bash
# 按模块备份
mysqldump workspace_core > backup_core_$(date +%Y%m%d).sql
mysqldump workspace_version > backup_version_$(date +%Y%m%d).sql
mysqldump workspace_collab > backup_collab_$(date +%Y%m%d).sql
```

### 3. 升级策略

```sql
-- 版本化的数据库迁移
CREATE TABLE schema_migrations (
  version VARCHAR(255) PRIMARY KEY,
  applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 模块独立升级
INSERT INTO schema_migrations VALUES ('version_control_v1.1.0', NOW());
```

## 📝 使用指南

### 1. 快速开始

```bash
# 1. 创建数据库
mysql -u root -p -e "CREATE DATABASE workspace;"

# 2. 导入核心模块
mysql -u root -p workspace < 模块化数据库设计.sql

# 3. 根据需要导入其他模块
mysql -u root -p workspace < 版本控制模块.sql
mysql -u root -p workspace < 协作模块.sql
```

### 2. 模块选择

**最小化部署** (仅核心功能):
- 核心工作空间模块

**标准部署** (常用功能):
- 核心工作空间模块
- 版本控制模块
- 评论系统模块

**完整部署** (全部功能):
- 所有模块

### 3. 自定义扩展

```sql
-- 添加自定义模块
CREATE TABLE custom_module_table (
  id BIGINT PRIMARY KEY AUTO_INCREMENT,
  workspace_id BIGINT NOT NULL,
  -- 自定义字段
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 关联到核心模块
ALTER TABLE custom_module_table 
ADD FOREIGN KEY (workspace_id) REFERENCES workspaces(id);
```

---

📧 如有问题，请联系开发团队或查看具体模块的SQL文件。
