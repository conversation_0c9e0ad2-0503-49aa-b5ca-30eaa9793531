<template>
  <div class="ai-chat">
    <!-- 侧边栏 -->
    <div class="chat-sidebar">
      <!-- 新建对话按钮 -->
      <div class="sidebar-header">
        <el-button type="primary" icon="el-icon-plus" @click="createNewSession" block>
          新建对话
        </el-button>
      </div>

      <!-- 对话列表 -->
      <div class="session-list">
        <div class="session-section">
          <h4>最近对话</h4>
          <div v-for="session in recentSessions" :key="session.id" class="session-item"
            :class="{ active: currentSessionId === session.id }" @click="switchSession(session.id)">
            <div class="session-info">
              <div class="session-title">{{ session.title }}</div>
              <div class="session-time">{{ formatTime(session.updateTime) }}</div>
            </div>
            <div class="session-actions">
              <el-dropdown @command="handleSessionCommand" trigger="click">
                <i class="el-icon-more"></i>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="{ action: 'rename', session }">重命名</el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'export', session }">导出</el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'delete', session }" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>

        <div class="session-section">
          <h4>历史对话</h4>
          <div v-for="session in historySessions" :key="session.id" class="session-item"
            :class="{ active: currentSessionId === session.id }" @click="switchSession(session.id)">
            <div class="session-info">
              <div class="session-title">{{ session.title }}</div>
              <div class="session-time">{{ formatTime(session.updateTime) }}</div>
            </div>
            <div class="session-actions">
              <el-dropdown @command="handleSessionCommand" trigger="click">
                <i class="el-icon-more"></i>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item :command="{ action: 'rename', session }">重命名</el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'export', session }">导出</el-dropdown-item>
                  <el-dropdown-item :command="{ action: 'delete', session }" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主聊天区域 -->
    <div class="chat-main">
      <!-- 聊天头部 -->
      <div class="chat-header">
        <div class="header-left">
          <h3>{{ (currentSession && currentSession.title) || 'AI智能问答' }}</h3>
          <div class="model-selector">
            <el-select v-model="selectedModel" size="small" @change="handleModelChange" style="width: 240px">
              <el-option v-for="model in availableModels" :key="model.id" :label="model.name" :value="model.id">
                <div class="model-option">
                  <div class="model-icon" :class="model.iconClass">
                    <span class="model-brand-text">{{ model.brandText }}</span>
                  </div>
                  <span class="model-name">{{ model.name }}</span>
                </div>
              </el-option>
            </el-select>
          </div>
        </div>
        <div class="header-right">
          <el-button-group size="small">
            <el-button icon="el-icon-refresh" @click="clearChat">清空对话</el-button>
            <el-button icon="el-icon-download" @click="exportChat">导出对话</el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 聊天内容区域 -->
      <div class="chat-content" ref="chatContent">
        <!-- 欢迎界面 -->
        <div v-if="messages.length === 0" class="welcome-screen">
          <div class="welcome-content">
            <div class="ai-avatar">
              <i class="el-icon-cpu"></i>
            </div>
            <h2>AI智能问答助手</h2>
            <p>我可以帮您解答关于工作空间文档和原辅料数据的问题</p>

            <!-- 快捷问题 -->
            <div class="quick-questions">
              <h4>试试这些问题：</h4>
              <div class="question-grid">
                <div v-for="question in recommendedQuestions" :key="question.id" class="question-card"
                  @click="askQuestion(question.text)">
                  <i :class="question.icon"></i>
                  <span>{{ question.text }}</span>
                </div>
              </div>
            </div>

            <!-- 功能介绍 -->
            <div class="features">
              <div class="feature-item">
                <i class="el-icon-document"></i>
                <div>
                  <h5>文档问答</h5>
                  <p>基于工作空间文档内容进行智能问答</p>
                </div>
              </div>
              <div class="feature-item">
                <i class="el-icon-s-grid"></i>
                <div>
                  <h5>数据分析</h5>
                  <p>分析原辅料数据，提供专业见解</p>
                </div>
              </div>
              <div class="feature-item">
                <i class="el-icon-search"></i>
                <div>
                  <h5>智能搜索</h5>
                  <p>自然语言搜索相关信息</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 聊天消息列表 -->
        <div v-else class="message-list">
          <div v-for="message in messages" :key="message.id" class="message-item"
            :class="{ 'user-message': message.role === 'user', 'ai-message': message.role === 'assistant' }">
            <div class="message-avatar">
              <el-avatar v-if="message.role === 'user'" :size="32" icon="el-icon-user" />
              <div v-else class="ai-avatar-small">
                <i class="el-icon-cpu"></i>
              </div>
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-role">{{ message.role === 'user' ? '您' : 'AI助手' }}</span>
                <span class="message-time">{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="message-text" v-html="formatMessage(message.content)"></div>

              <!-- AI消息的操作按钮 -->
              <div v-if="message.role === 'assistant'" class="message-actions">
                <el-button type="text" size="mini" @click="copyMessage(message.content)">
                  <i class="el-icon-copy-document"></i> 复制
                </el-button>
                <el-button type="text" size="mini" @click="likeMessage(message.id)">
                  <i class="el-icon-thumb"></i> 有用
                </el-button>
                <el-button type="text" size="mini" @click="dislikeMessage(message.id)">
                  <i class="el-icon-thumb" style="transform: rotate(180deg)"></i> 无用
                </el-button>
              </div>

              <!-- 相关文档引用 -->
              <div v-if="message.references && message.references.length > 0" class="message-references">
                <h6>参考文档：</h6>
                <div class="reference-list">
                  <div v-for="ref in message.references" :key="ref.id" class="reference-item"
                    @click="openReference(ref)">
                    <i :class="getFileIcon(ref.type)"></i>
                    <span>{{ ref.name }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 加载中状态 -->
          <div v-if="isLoading" class="message-item ai-message">
            <div class="message-avatar">
              <div class="ai-avatar-small">
                <i class="el-icon-cpu"></i>
              </div>
            </div>
            <div class="message-content">
              <div class="message-header">
                <span class="message-role">AI助手</span>
              </div>
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-input">
        <!-- 上下文选择 -->
        <div class="context-selector">
          <el-radio-group v-model="contextType" size="small">
            <el-radio-button label="general">通用问答</el-radio-button>
            <el-radio-button label="document">文档问答</el-radio-button>
            <el-radio-button label="material">原辅料数据</el-radio-button>
          </el-radio-group>

          <!-- 文档选择器 -->
          <el-select v-if="contextType === 'document'" v-model="selectedDocuments" multiple placeholder="选择相关文档"
            size="small" style="margin-left: 12px; width: 200px" @change="handleDocumentChange">
            <el-option v-for="doc in availableDocuments" :key="doc.id" :label="doc.name" :value="doc.id">
              <div class="document-option">
                <i :class="getFileIcon(doc.type)"></i>
                <span>{{ doc.name }}</span>
                <span class="file-size">{{ formatFileSize(doc.size) }}</span>
              </div>
            </el-option>
          </el-select>

          <!-- 原辅料选择器 -->
          <el-select v-if="contextType === 'material'" v-model="selectedMaterials" multiple placeholder="选择相关原辅料"
            size="small" style="margin-left: 12px; width: 200px" @change="handleMaterialChange">
            <el-option v-for="material in availableMaterials" :key="material.id" :label="material.name"
              :value="material.id">
              <div class="material-option">
                <span class="material-category-text">{{ material.category }}</span>
                <span>{{ material.name }}</span>
                <span class="material-cas">CAS: {{ material.cas }}</span>
              </div>
            </el-option>
          </el-select>
        </div>

        <!-- 选中的文档和原辅料显示 -->
        <div v-if="selectedDocuments.length > 0 || selectedMaterials.length > 0" class="selected-context">
          <!-- 选中的文档 -->
          <div v-if="selectedDocuments.length > 0" class="selected-documents">
            <h6><i class="el-icon-document"></i> 已选择文档：</h6>
            <div class="selected-items">
              <div v-for="docId in selectedDocuments" :key="docId" class="selected-item document-item">
                <div class="item-info">
                  <i :class="getFileIcon(getDocumentById(docId).type)"></i>
                  <span class="item-name" :title="getDocumentById(docId).name">{{ getDocumentById(docId).name }}</span>
                </div>
                <el-button type="text" size="mini" icon="el-icon-close" @click="removeDocument(docId)" />
              </div>
            </div>
          </div>

          <!-- 选中的原辅料 -->
          <div v-if="selectedMaterials.length > 0" class="selected-materials">
            <h6><i class="el-icon-s-grid"></i> 已选择原辅料：</h6>
            <div class="selected-items">
              <div v-for="materialId in selectedMaterials" :key="materialId" class="selected-item material-item">
                <div class="item-info">
                  <div class="material-category-small"
                    :style="{ backgroundColor: getMaterialById(materialId).categoryColor }">
                    {{ getMaterialById(materialId).category }}
                  </div>
                  <span class="item-name"
                    :title="getMaterialById(materialId).name + ' (CAS: ' + getMaterialById(materialId).cas + ')'">
                    {{ getMaterialById(materialId).name }}
                  </span>
                </div>
                <el-button type="text" size="mini" icon="el-icon-close" @click="removeMaterial(materialId)" />
              </div>
            </div>
          </div>
        </div>

        <!-- 输入框 -->
        <div class="input-area">
          <el-input v-model="inputMessage" type="textarea" :rows="3" placeholder="输入您的问题..."
            @keydown.native="handleKeydown" ref="messageInput" />
          <div class="input-actions">
            <div class="input-tips">
              <el-checkbox v-model="enterToSend" size="mini">Enter发送</el-checkbox>
              <span v-if="enterToSend">Enter 发送，Shift + Enter 换行</span>
              <span v-else>Ctrl + Enter 发送</span>
            </div>
            <el-button type="primary" :loading="isLoading" :disabled="!inputMessage.trim()" @click="sendMessage">
              发送
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  getAiModels,
  sendChatMessage,
  getChatHistory,
  createChatSession,
  deleteChatSession,
  getChatSessions,
  askDocument,
  askMaterial,
  getRecommendedQuestions,
  exportChatHistory
} from '@/api/ai/chat'

export default {
  name: 'AiChat',
  data () {
    return {
      // 会话管理
      currentSessionId: null,
      currentSession: null,
      recentSessions: [],
      historySessions: [],

      // 消息相关
      messages: [],
      inputMessage: '',
      isLoading: false,

      // 模型选择
      selectedModel: 'gpt-3.5-turbo',
      availableModels: [],

      // 上下文选择
      contextType: 'general',
      selectedDocuments: [],
      selectedMaterials: [],
      availableDocuments: [],
      availableMaterials: [],

      // 推荐问题
      recommendedQuestions: [],

      // 输入设置
      enterToSend: true // 默认开启Enter发送
    }
  },
  created () {
    this.loadModels()
    this.loadSessions()
    this.loadRecommendedQuestions()
    this.loadAvailableDocuments()
    this.loadAvailableMaterials()
    this.loadUserPreferences()
  },
  mounted () {
    this.scrollToBottom()
  },
  methods: {
    // 加载可用模型
    async loadModels () {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))

        this.availableModels = [
          {
            id: 'gpt-3.5-turbo',
            name: 'GPT-3.5 Turbo - OpenAI (免费)',
            displayName: 'GPT-3.5 Turbo',
            brandText: 'GPT',
            iconClass: 'openai-icon',
            provider: 'OpenAI',
            isPremium: false
          },
          {
            id: 'gpt-4',
            name: 'GPT-4 - OpenAI (Pro)',
            displayName: 'GPT-4',
            brandText: 'GP4',
            iconClass: 'openai-icon',
            provider: 'OpenAI',
            isPremium: true
          },
          {
            id: 'claude-3',
            name: 'Claude-3 - Anthropic (Pro)',
            displayName: 'Claude-3',
            brandText: 'CL',
            iconClass: 'anthropic-icon',
            provider: 'Anthropic',
            isPremium: true
          },
          {
            id: 'gemini-pro',
            name: 'Gemini Pro - Google (免费)',
            displayName: 'Gemini Pro',
            brandText: 'GM',
            iconClass: 'google-icon',
            provider: 'Google',
            isPremium: false
          },
          {
            id: 'qwen-turbo',
            name: '通义千问 - 阿里云 (免费)',
            displayName: '通义千问',
            brandText: '千问',
            iconClass: 'alibaba-icon',
            provider: '阿里云',
            isPremium: false
          },
          {
            id: 'ernie-bot',
            name: '文心一言 - 百度 (免费)',
            displayName: '文心一言',
            brandText: '文心',
            iconClass: 'baidu-icon',
            provider: '百度',
            isPremium: false
          },
          {
            id: 'chatglm',
            name: 'ChatGLM - 智谱AI (免费)',
            displayName: 'ChatGLM',
            brandText: 'GLM',
            iconClass: 'zhipu-icon',
            provider: '智谱AI',
            isPremium: false
          }
        ]
      } catch (error) {
        this.$message.error('加载模型列表失败')
      }
    },

    // 加载会话列表
    async loadSessions () {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))

        const mockSessions = [
          {
            id: '1',
            title: '原辅料成分分析',
            updateTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            messageCount: 8
          },
          {
            id: '2',
            title: '文档内容总结',
            updateTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            messageCount: 5
          },
          {
            id: '3',
            title: '技术规范咨询',
            updateTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            messageCount: 12
          }
        ]

        this.recentSessions = mockSessions.slice(0, 2)
        this.historySessions = mockSessions.slice(2)
      } catch (error) {
        this.$message.error('加载会话列表失败')
      }
    },

    // 加载推荐问题
    async loadRecommendedQuestions () {
      try {
        this.recommendedQuestions = [
          {
            id: 1,
            text: '帮我分析这个原辅料的成分',
            icon: 'el-icon-s-grid'
          },
          {
            id: 2,
            text: '总结一下项目文档的要点',
            icon: 'el-icon-document'
          },
          {
            id: 3,
            text: '这个配方有什么潜在风险？',
            icon: 'el-icon-warning'
          },
          {
            id: 4,
            text: '推荐类似的原辅料替代品',
            icon: 'el-icon-search'
          }
        ]
      } catch (error) {
        console.error('加载推荐问题失败:', error)
      }
    },

    // 加载可用文档
    async loadAvailableDocuments () {
      try {
        this.availableDocuments = [
          {
            id: 1,
            name: '项目需求文档.docx',
            type: 'word',
            size: 2048576,
            path: '/workspace/1/files/项目需求文档.docx'
          },
          {
            id: 2,
            name: '技术规范.pdf',
            type: 'pdf',
            size: 5242880,
            path: '/workspace/1/files/技术规范.pdf'
          },
          {
            id: 3,
            name: '用户手册.pdf',
            type: 'pdf',
            size: 3145728,
            path: '/workspace/1/files/用户手册.pdf'
          },
          {
            id: 4,
            name: '测试报告.xlsx',
            type: 'excel',
            size: 1048576,
            path: '/workspace/1/files/测试报告.xlsx'
          },
          {
            id: 5,
            name: 'API接口文档.md',
            type: 'markdown',
            size: 512000,
            path: '/workspace/1/files/API接口文档.md'
          },
          {
            id: 6,
            name: '系统架构图.png',
            type: 'image',
            size: 2097152,
            path: '/workspace/1/files/系统架构图.png'
          }
        ]
      } catch (error) {
        console.error('加载文档列表失败:', error)
      }
    },

    // 加载可用原辅料
    async loadAvailableMaterials () {
      try {
        this.availableMaterials = [
          {
            id: 1,
            name: '维生素C',
            cas: '50-81-7',
            category: '维生素',
            categoryColor: '#52c41a',
            specification: 'USP级',
            purity: '99.0%'
          },
          {
            id: 2,
            name: '柠檬酸',
            cas: '77-92-9',
            category: '酸化剂',
            categoryColor: '#1890ff',
            specification: 'FCC级',
            purity: '99.5%'
          },
          {
            id: 3,
            name: '碳酸钙',
            cas: '471-34-1',
            category: '填充剂',
            categoryColor: '#722ed1',
            specification: 'USP级',
            purity: '98.5%'
          },
          {
            id: 4,
            name: '硬脂酸镁',
            cas: '557-04-0',
            category: '润滑剂',
            categoryColor: '#fa8c16',
            specification: 'NF级',
            purity: '98.0%'
          },
          {
            id: 5,
            name: '微晶纤维素',
            cas: '9004-34-6',
            category: '填充剂',
            categoryColor: '#722ed1',
            specification: 'PH101',
            purity: '99.0%'
          },
          {
            id: 6,
            name: '交联聚维酮',
            cas: '25249-54-1',
            category: '崩解剂',
            categoryColor: '#eb2f96',
            specification: 'USP级',
            purity: '98.5%'
          }
        ]
      } catch (error) {
        console.error('加载原辅料列表失败:', error)
      }
    },

    // 创建新会话
    async createNewSession () {
      try {
        const sessionData = {
          title: '新对话',
          model: this.selectedModel
        }

        // 模拟创建会话
        const newSession = {
          id: Date.now().toString(),
          title: '新对话',
          updateTime: new Date().toISOString(),
          messageCount: 0
        }

        this.currentSessionId = newSession.id
        this.currentSession = newSession
        this.messages = []
        this.recentSessions.unshift(newSession)
      } catch (error) {
        this.$message.error('创建会话失败')
      }
    },

    // 切换会话
    async switchSession (sessionId) {
      try {
        this.currentSessionId = sessionId
        this.currentSession = [...this.recentSessions, ...this.historySessions]
          .find(s => s.id === sessionId)

        // 加载会话历史
        await this.loadChatHistory(sessionId)
      } catch (error) {
        this.$message.error('切换会话失败')
      }
    },

    // 加载聊天历史
    async loadChatHistory (sessionId) {
      try {
        // 模拟加载历史消息
        if (sessionId === '1') {
          this.messages = [
            {
              id: 1,
              role: 'user',
              content: '请帮我分析维生素C的成分和作用',
              timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString()
            },
            {
              id: 2,
              role: 'assistant',
              content: '维生素C（抗坏血酸）是一种重要的水溶性维生素，具有以下特点：\n\n**化学成分：**\n- 分子式：C₆H₈O₆\n- 分子量：176.12 g/mol\n- 白色结晶性粉末，易溶于水\n\n**主要作用：**\n1. 抗氧化作用\n2. 促进胶原蛋白合成\n3. 增强免疫力\n4. 促进铁吸收\n\n**应用领域：**\n- 保健品\n- 食品添加剂\n- 化妆品\n- 医药制剂',
              timestamp: new Date(Date.now() - 59 * 60 * 1000).toISOString(),
              references: [
                { id: 1, name: '维生素C技术规格书.pdf', type: 'pdf' },
                { id: 2, name: '原辅料数据库.xlsx', type: 'excel' }
              ]
            }
          ]
        } else {
          this.messages = []
        }
      } catch (error) {
        this.$message.error('加载聊天历史失败')
      }
    },

    // 处理键盘事件
    handleKeydown (event) {
      // 只处理Enter键
      if (event.key === 'Enter') {
        if (this.enterToSend) {
          // Enter发送模式
          if (event.shiftKey) {
            // Shift + Enter 换行，不阻止默认行为
            return
          } else {
            // 单独Enter发送消息
            event.preventDefault()
            this.sendMessage()
          }
        } else {
          // Ctrl + Enter发送模式
          if (event.ctrlKey || event.metaKey) {
            event.preventDefault()
            this.sendMessage()
          }
          // 单独Enter不做处理，保持默认换行行为
        }
      }
    },

    // 发送消息
    async sendMessage () {
      if (!this.inputMessage.trim()) return

      const userMessage = {
        id: Date.now(),
        role: 'user',
        content: this.inputMessage.trim(),
        timestamp: new Date().toISOString()
      }

      this.messages.push(userMessage)
      const question = this.inputMessage.trim()
      this.inputMessage = ''
      this.isLoading = true

      this.$nextTick(() => {
        this.scrollToBottom()
      })

      try {
        // 根据上下文类型调用不同的API
        let response
        if (this.contextType === 'document') {
          response = await this.askDocumentQuestion(question)
        } else if (this.contextType === 'material') {
          response = await this.askMaterialQuestion(question)
        } else {
          response = await this.askGeneralQuestion(question)
        }

        const aiMessage = {
          id: Date.now() + 1,
          role: 'assistant',
          content: response.content,
          timestamp: new Date().toISOString(),
          references: response.references || []
        }

        this.messages.push(aiMessage)
      } catch (error) {
        this.$message.error('发送消息失败')
        const errorMessage = {
          id: Date.now() + 1,
          role: 'assistant',
          content: '抱歉，我遇到了一些问题，请稍后再试。',
          timestamp: new Date().toISOString()
        }
        this.messages.push(errorMessage)
      } finally {
        this.isLoading = false
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 通用问答
    async askGeneralQuestion (question) {
      // 模拟AI回复
      await new Promise(resolve => setTimeout(resolve, 2000))

      const responses = {
        '你好': '您好！我是AI智能问答助手，很高兴为您服务。我可以帮您解答关于工作空间文档和原辅料数据的问题。',
        '帮助': '我可以为您提供以下帮助：\n\n1. **文档问答**：基于您的工作空间文档内容回答问题\n2. **原辅料分析**：分析原辅料的成分、性质和应用\n3. **数据查询**：快速查找相关信息\n4. **专业建议**：提供专业的技术建议\n\n请告诉我您需要什么帮助！'
      }

      return {
        content: responses[question] || `关于"${question}"的问题，我正在为您分析...\n\n基于我的知识库，我建议您：\n\n1. 首先明确具体的需求和目标\n2. 查看相关的技术文档和规范\n3. 考虑实际应用场景和限制条件\n4. 如需更详细的信息，请提供更多上下文\n\n如果您有具体的文档或原辅料数据需要分析，请选择相应的问答模式。`,
        references: []
      }
    },

    // 文档问答
    async askDocumentQuestion (question) {
      await new Promise(resolve => setTimeout(resolve, 2500))

      return {
        content: `基于您选择的文档，我为您分析"${question}"：\n\n**文档分析结果：**\n\n1. **相关内容摘要**\n   - 在技术规范文档中找到了相关描述\n   - 项目需求文档中有对应的功能说明\n\n2. **关键信息提取**\n   - 技术要求：符合行业标准\n   - 实施建议：按照文档中的步骤执行\n   - 注意事项：需要特别关注安全要求\n\n3. **建议和总结**\n   - 建议参考文档第3.2节的详细说明\n   - 可以结合实际情况进行适当调整\n   - 如有疑问，建议咨询相关专家\n\n需要我进一步解释文档中的某个具体部分吗？`,
        references: [
          { id: 1, name: '技术规范.pdf', type: 'pdf' },
          { id: 2, name: '项目需求文档.docx', type: 'word' }
        ]
      }
    },

    // 原辅料问答
    async askMaterialQuestion (question) {
      await new Promise(resolve => setTimeout(resolve, 2000))

      return {
        content: `关于原辅料"${question}"的分析：\n\n**成分分析：**\n- 主要成分：根据数据库记录\n- 纯度要求：≥99.0%\n- 物理性状：白色结晶粉末\n\n**质量标准：**\n- 符合USP标准\n- 重金属含量：≤10ppm\n- 微生物限度：符合要求\n\n**应用建议：**\n- 推荐用量：0.1-0.5%\n- 配伍禁忌：避免与强氧化剂接触\n- 储存条件：密闭、干燥、避光保存\n\n**风险评估：**\n- 安全等级：低风险\n- 注意事项：避免长期大量接触\n\n需要查看详细的技术参数或安全数据表吗？`,
        references: [
          { id: 1, name: '原辅料数据库.xlsx', type: 'excel' },
          { id: 2, name: '质量标准.pdf', type: 'pdf' }
        ]
      }
    },

    // 快速提问
    askQuestion (question) {
      this.inputMessage = question
      this.sendMessage()
    },

    // 处理会话命令
    handleSessionCommand (command) {
      const { action, session } = command
      switch (action) {
        case 'rename':
          this.renameSession(session)
          break
        case 'export':
          this.exportSession(session)
          break
        case 'delete':
          this.deleteSession(session)
          break
      }
    },

    // 重命名会话
    async renameSession (session) {
      try {
        const { value } = await this.$prompt('请输入新的会话名称', '重命名会话', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: session.title
        })

        session.title = value
        this.$message.success('重命名成功')
      } catch (error) {
        // 用户取消
      }
    },

    // 导出会话
    async exportSession (session) {
      try {
        this.$message.success('导出功能开发中...')
      } catch (error) {
        this.$message.error('导出失败')
      }
    },

    // 删除会话
    async deleteSession (session) {
      try {
        await this.$confirm('确定要删除这个会话吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 从列表中移除
        this.recentSessions = this.recentSessions.filter(s => s.id !== session.id)
        this.historySessions = this.historySessions.filter(s => s.id !== session.id)

        // 如果删除的是当前会话，清空消息
        if (this.currentSessionId === session.id) {
          this.currentSessionId = null
          this.currentSession = null
          this.messages = []
        }

        this.$message.success('删除成功')
      } catch (error) {
        // 用户取消
      }
    },

    // 模型切换
    handleModelChange () {
      const selectedModel = this.availableModels.find(m => m.id === this.selectedModel)
      const modelName = selectedModel ? selectedModel.name : '未知模型'
      this.$message.info(`已切换到 ${modelName}`)
    },

    // 清空对话
    clearChat () {
      this.messages = []
      this.$message.success('对话已清空')
    },

    // 导出对话
    exportChat () {
      this.$message.success('导出功能开发中...')
    },

    // 复制消息
    copyMessage (content) {
      navigator.clipboard.writeText(content).then(() => {
        this.$message.success('已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    // 点赞消息
    likeMessage (messageId) {
      this.$message.success('感谢您的反馈')
    },

    // 点踩消息
    dislikeMessage (messageId) {
      this.$message.info('我们会继续改进')
    },

    // 打开引用文档
    openReference (ref) {
      this.$message.info(`打开文档：${ref.name}`)
    },

    // 获取文件图标
    getFileIcon (type) {
      const iconMap = {
        pdf: 'el-icon-document',
        word: 'el-icon-document',
        excel: 'el-icon-s-grid',
        powerpoint: 'el-icon-present'
      }
      return iconMap[type] || 'el-icon-document'
    },

    // 格式化消息内容
    formatMessage (content) {
      return content
        .replace(/\n/g, '<br>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/`(.*?)`/g, '<code>$1</code>')
    },

    // 格式化时间
    formatTime (time) {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date

      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
      if (diff < 604800000) return Math.floor(diff / 86400000) + '天前'

      return date.toLocaleDateString()
    },

    // 滚动到底部
    scrollToBottom () {
      this.$nextTick(() => {
        const chatContent = this.$refs.chatContent
        if (chatContent) {
          chatContent.scrollTop = chatContent.scrollHeight
        }
      })
    },

    // 处理文档选择变化
    handleDocumentChange () {
      // 可以在这里添加文档选择变化的处理逻辑
    },

    // 处理原辅料选择变化
    handleMaterialChange () {
      // 可以在这里添加原辅料选择变化的处理逻辑
    },

    // 根据ID获取文档信息
    getDocumentById (id) {
      return this.availableDocuments.find(doc => doc.id === id) || {}
    },

    // 根据ID获取原辅料信息
    getMaterialById (id) {
      return this.availableMaterials.find(material => material.id === id) || {}
    },

    // 移除选中的文档
    removeDocument (docId) {
      this.selectedDocuments = this.selectedDocuments.filter(id => id !== docId)
    },

    // 移除选中的原辅料
    removeMaterial (materialId) {
      this.selectedMaterials = this.selectedMaterials.filter(id => id !== materialId)
    },

    // 格式化文件大小
    formatFileSize (size) {
      if (!size) return '-'
      const units = ['B', 'KB', 'MB', 'GB']
      let index = 0
      while (size >= 1024 && index < units.length - 1) {
        size /= 1024
        index++
      }
      return size.toFixed(1) + ' ' + units[index]
    },

    // 加载用户偏好设置
    loadUserPreferences () {
      const preferences = localStorage.getItem('ai-chat-preferences')
      if (preferences) {
        try {
          const parsed = JSON.parse(preferences)
          this.enterToSend = parsed.enterToSend !== undefined ? parsed.enterToSend : true
        } catch (error) {
          console.warn('Failed to parse user preferences:', error)
        }
      }
    },

    // 保存用户偏好设置
    saveUserPreferences () {
      const preferences = {
        enterToSend: this.enterToSend
      }
      localStorage.setItem('ai-chat-preferences', JSON.stringify(preferences))
    }
  },

  watch: {
    // 监听enterToSend变化，自动保存偏好
    enterToSend (newValue) {
      this.saveUserPreferences()
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-chat {
  display: flex;
  height: calc(100vh - 84px);
  background: #f5f7fa;
}

// 侧边栏样式
.chat-sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;

  .sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;
  }

  .session-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px 0;

    .session-section {
      margin-bottom: 24px;

      h4 {
        margin: 0 0 12px 0;
        padding: 0 16px;
        font-size: 14px;
        color: #909399;
        font-weight: 500;
      }

      .session-item {
        display: flex;
        align-items: center;
        padding: 12px 16px;
        cursor: pointer;
        transition: all 0.3s ease;

        &:hover {
          background: #f5f7fa;
        }

        &.active {
          background: #e6f7ff;
          border-right: 3px solid #409eff;
        }

        .session-info {
          flex: 1;
          min-width: 0;

          .session-title {
            font-size: 14px;
            color: #303133;
            margin-bottom: 4px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .session-time {
            font-size: 12px;
            color: #909399;
          }
        }

        .session-actions {
          opacity: 0;
          transition: opacity 0.3s ease;

          i {
            color: #909399;
            cursor: pointer;
            padding: 4px;

            &:hover {
              color: #409eff;
            }
          }
        }

        &:hover .session-actions {
          opacity: 1;
        }
      }
    }
  }
}

// 主聊天区域样式
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e4e7ed;
  background: white;

  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;

    h3 {
      margin: 0;
      font-size: 18px;
      color: #303133;
    }

    .model-selector {
      .model-option {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;

        .model-name {
          flex: 1;
          font-size: 14px;
          color: #303133;
        }

        .model-icon {
          width: 20px;
          height: 20px;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-size: 12px;

          &.openai-icon {
            background: linear-gradient(135deg, #10a37f 0%, #1a7f64 100%);
          }

          &.anthropic-icon {
            background: linear-gradient(135deg, #cc785c 0%, #b8654a 100%);
          }

          &.google-icon {
            background: linear-gradient(135deg, #4285f4 0%, #3367d6 100%);
          }

          &.alibaba-icon {
            background: linear-gradient(135deg, #ff6a00 0%, #e55a00 100%);
          }

          &.baidu-icon {
            background: linear-gradient(135deg, #2932e1 0%, #1e24a8 100%);
          }

          &.zhipu-icon {
            background: linear-gradient(135deg, #722ed1 0%, #5b23a7 100%);
          }

          .model-brand-text {
            font-size: 10px;
            font-weight: bold;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          }
        }
      }
    }
  }
}

.chat-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

// 欢迎界面样式
.welcome-screen {
  min-height: 100%;
  padding: 40px 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;

  .welcome-content {
    text-align: center;
    max-width: 600px;
    width: 100%;

    .ai-avatar {
      width: 64px;
      height: 64px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 20px auto;

      i {
        font-size: 32px;
        color: white;
      }
    }

    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
    }

    p {
      margin: 0 0 24px 0;
      color: #606266;
      font-size: 15px;
    }

    .quick-questions {
      margin-bottom: 32px;

      h4 {
        margin: 0 0 12px 0;
        color: #303133;
        font-size: 15px;
      }

      .question-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;

        .question-card {
          padding: 16px;
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          display: flex;
          align-items: center;
          gap: 12px;

          &:hover {
            border-color: #409eff;
            background: #f0f9ff;
          }

          i {
            font-size: 20px;
            color: #409eff;
          }

          span {
            font-size: 14px;
            color: #303133;
          }
        }
      }
    }

    .features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;

      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        text-align: left;

        i {
          font-size: 24px;
          color: #409eff;
          margin-top: 4px;
          flex-shrink: 0;
        }

        div {
          flex: 1;
          min-width: 0;
        }

        h5 {
          margin: 0 0 6px 0;
          color: #303133;
          font-size: 15px;
          font-weight: 600;
        }

        p {
          margin: 0;
          color: #606266;
          font-size: 13px;
          line-height: 1.5;
          word-wrap: break-word;
        }
      }
    }
  }
}

// 消息列表样式
.message-list {
  .message-item {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;
    gap: 10px;

    &.user-message {
      flex-direction: row-reverse;

      .message-content {
        background: #409eff;
        color: white;
        border-radius: 18px 18px 4px 18px;
      }
    }

    &.ai-message {
      .message-content {
        background: #f5f7fa;
        border-radius: 18px 18px 18px 4px;
      }
    }

    .message-avatar {
      flex-shrink: 0;

      .ai-avatar-small {
        width: 32px;
        height: 32px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 16px;
          color: white;
        }
      }
    }

    .message-content {
      max-width: 70%;
      padding: 10px 14px;

      .message-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .message-role {
          font-size: 12px;
          font-weight: 500;
          opacity: 0.8;
        }

        .message-time {
          font-size: 11px;
          opacity: 0.6;
        }
      }

      .message-text {
        font-size: 14px;
        line-height: 1.5;
        word-wrap: break-word;

        ::v-deep {
          strong {
            font-weight: 600;
          }

          em {
            font-style: italic;
          }

          code {
            background: rgba(0, 0, 0, 0.1);
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
            font-size: 13px;
          }

          pre {
            font-size: 13px;
            line-height: 1.4;
          }

          p {
            margin: 0 0 8px 0;
            font-size: 14px;

            &:last-child {
              margin-bottom: 0;
            }
          }

          ul,
          ol {
            margin: 8px 0;
            padding-left: 20px;
            font-size: 14px;

            li {
              margin-bottom: 4px;
            }
          }

          h1,
          h2,
          h3,
          h4,
          h5,
          h6 {
            margin: 12px 0 8px 0;
            font-size: 15px;
            font-weight: 600;

            &:first-child {
              margin-top: 0;
            }
          }

          blockquote {
            margin: 8px 0;
            padding: 8px 12px;
            border-left: 3px solid #ddd;
            background: rgba(0, 0, 0, 0.05);
            font-size: 14px;
          }
        }
      }

      .message-actions {
        margin-top: 8px;
        display: flex;
        gap: 8px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .message-references {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid rgba(0, 0, 0, 0.1);

        h6 {
          margin: 0 0 8px 0;
          font-size: 12px;
          color: #606266;
        }

        .reference-list {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;

          .reference-item {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 4px 8px;
            background: rgba(64, 158, 255, 0.1);
            border-radius: 12px;
            cursor: pointer;
            font-size: 12px;
            color: #409eff;
            transition: all 0.3s ease;

            &:hover {
              background: rgba(64, 158, 255, 0.2);
            }

            i {
              font-size: 12px;
            }
          }
        }
      }
    }

    &:hover .message-actions {
      opacity: 1;
    }
  }
}

// 输入中指示器
.typing-indicator {
  display: flex;
  gap: 4px;
  align-items: center;

  span {
    width: 8px;
    height: 8px;
    background: #409eff;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;

    &:nth-child(1) {
      animation-delay: -0.32s;
    }

    &:nth-child(2) {
      animation-delay: -0.16s;
    }
  }
}

@keyframes typing {

  0%,
  80%,
  100% {
    transform: scale(0);
    opacity: 0.5;
  }

  40% {
    transform: scale(1);
    opacity: 1;
  }
}

// 输入区域样式
.chat-input {
  border-top: 1px solid #e4e7ed;
  padding: 16px 20px;
  background: white;

  .context-selector {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    .document-option,
    .material-option {
      display: flex;
      align-items: center;
      gap: 8px;
      width: 100%;

      .file-size,
      .material-cas {
        font-size: 12px;
        color: #909399;
        margin-left: auto;
      }

      .material-category {
        padding: 2px 6px;
        border-radius: 10px;
        color: white;
        font-size: 10px;
        font-weight: bold;
      }

      .material-category-text {
        font-size: 12px;
        color: #409eff;
        font-weight: 500;
      }
    }
  }

  .selected-context {
    margin-bottom: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;

    .selected-documents,
    .selected-materials {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      h6 {
        margin: 0 0 8px 0;
        font-size: 13px;
        color: #495057;
        display: flex;
        align-items: center;
        gap: 4px;

        i {
          color: #6c757d;
        }
      }

      .selected-items {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;

        .selected-item {
          display: flex;
          align-items: center;
          gap: 6px;
          padding: 6px 10px;
          background: white;
          border-radius: 16px;
          border: 1px solid #dee2e6;
          font-size: 12px;
          max-width: 200px;

          .item-info {
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            min-width: 0;

            .item-name {
              font-size: 12px;
              color: #212529;
              font-weight: 500;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .item-meta {
              font-size: 10px;
              color: #6c757d;
              white-space: nowrap;
            }

            .material-category-small {
              padding: 2px 4px;
              border-radius: 6px;
              color: white;
              font-size: 8px;
              font-weight: bold;
              white-space: nowrap;
            }

            i {
              color: #6c757d;
              font-size: 12px;
              flex-shrink: 0;
            }
          }

          .el-button {
            opacity: 0.6;
            transition: opacity 0.3s ease;
            padding: 0;
            min-width: auto;
            width: 16px;
            height: 16px;
            border: none;
            background: transparent;

            &:hover {
              opacity: 1;
              background: #f5f5f5;
              border-radius: 50%;
            }

            i {
              font-size: 10px;
            }
          }
        }
      }
    }
  }

  .input-area {
    position: relative;

    .input-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: 8px;

      .input-tips {
        font-size: 12px;
        color: #909399;
        display: flex;
        align-items: center;
        gap: 12px;

        .el-checkbox {
          margin-right: 0;

          ::v-deep .el-checkbox__label {
            font-size: 12px;
            color: #606266;
          }
        }

        span {
          color: #909399;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-chat {
    flex-direction: column;
  }

  .chat-sidebar {
    width: 100%;
    height: 200px;
    border-right: none;
    border-bottom: 1px solid #e4e7ed;

    .session-list {
      .session-section {
        .session-item {
          .session-actions {
            opacity: 1;
          }
        }
      }
    }
  }

  .chat-main {
    height: calc(100vh - 284px);
  }

  .welcome-screen {
    padding: 20px 15px;

    .welcome-content {
      .ai-avatar {
        width: 56px;
        height: 56px;
        margin-bottom: 16px;

        i {
          font-size: 28px;
        }
      }

      h2 {
        font-size: 20px;
        margin-bottom: 6px;
      }

      p {
        font-size: 14px;
        margin-bottom: 20px;
      }

      .quick-questions {
        margin-bottom: 24px;

        h4 {
          font-size: 14px;
          margin-bottom: 10px;
        }

        .question-grid {
          grid-template-columns: 1fr;
          gap: 8px;
        }

        .question-card {
          padding: 12px;
        }
      }

      .features {
        grid-template-columns: 1fr;
        gap: 16px;

        .feature-item {
          gap: 10px;

          i {
            font-size: 20px;
          }

          h5 {
            font-size: 14px;
          }

          p {
            font-size: 12px;
          }
        }
      }
    }
  }

  .message-list {
    .message-item {
      .message-content {
        max-width: 85%;
      }

      .message-actions {
        opacity: 1;
      }
    }
  }
}

// 处理缩放情况的响应式设计
@media (max-height: 600px) {
  .welcome-screen {
    padding: 20px 15px;

    .welcome-content {
      .ai-avatar {
        width: 48px;
        height: 48px;
        margin-bottom: 12px;

        i {
          font-size: 24px;
        }
      }

      h2 {
        font-size: 20px;
        margin-bottom: 6px;
      }

      p {
        font-size: 14px;
        margin-bottom: 16px;
      }

      .quick-questions {
        margin-bottom: 20px;

        h4 {
          font-size: 14px;
          margin-bottom: 8px;
        }

        .question-grid {
          gap: 6px;
        }

        .question-card {
          padding: 10px;
          font-size: 13px;

          i {
            font-size: 16px;
          }
        }
      }

      .features {
        gap: 12px;

        .feature-item {
          gap: 8px;

          i {
            font-size: 18px;
            margin-top: 2px;
          }

          h5 {
            font-size: 13px;
            margin-bottom: 3px;
          }

          p {
            font-size: 11px;
            line-height: 1.3;
          }
        }
      }
    }
  }
}

// 处理极小屏幕或高缩放比例
@media (max-height: 480px) {
  .welcome-screen {
    padding: 15px 10px;

    .welcome-content {
      .ai-avatar {
        width: 40px;
        height: 40px;
        margin-bottom: 10px;

        i {
          font-size: 20px;
        }
      }

      h2 {
        font-size: 18px;
        margin-bottom: 4px;
      }

      p {
        font-size: 13px;
        margin-bottom: 12px;
      }

      .quick-questions {
        margin-bottom: 16px;

        h4 {
          font-size: 13px;
          margin-bottom: 6px;
        }

        .question-grid {
          grid-template-columns: 1fr;
          gap: 4px;
        }

        .question-card {
          padding: 8px;
          font-size: 12px;

          i {
            font-size: 14px;
          }
        }
      }

      .features {
        grid-template-columns: 1fr;
        gap: 8px;

        .feature-item {
          gap: 6px;

          i {
            font-size: 16px;
            margin-top: 1px;
          }

          h5 {
            font-size: 12px;
            margin-bottom: 2px;
          }

          p {
            font-size: 10px;
            line-height: 1.2;
          }
        }
      }
    }
  }
}
</style>
