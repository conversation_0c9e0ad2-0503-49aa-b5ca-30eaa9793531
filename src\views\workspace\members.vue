<template>
  <div class="workspace-members">
    <!-- 页面头部 -->
    <div class="members-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/workspace' }">工作空间</el-breadcrumb-item>
          <el-breadcrumb-item>{{ workspaceName }}</el-breadcrumb-item>
          <el-breadcrumb-item>成员管理</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <el-button type="primary" icon="el-icon-plus" @click="handleInvite">
          邀请成员
        </el-button>
      </div>
    </div>

    <!-- 成员列表 -->
    <div class="members-content">
      <el-table :data="memberList" v-loading="loading">
        <el-table-column label="头像" width="80">
          <template slot-scope="scope">
            <el-avatar :src="scope.row.avatar" :size="40">
              {{ scope.row.nickname ? scope.row.nickname.charAt(0) : 'U' }}
            </el-avatar>
          </template>
        </el-table-column>
        <el-table-column label="用户名" prop="username" />
        <el-table-column label="昵称" prop="nickname" />
        <el-table-column label="邮箱" prop="email" />
        <el-table-column label="角色" width="120">
          <template slot-scope="scope">
            <el-tag :type="getRoleTypeTag(scope.row.role)">
              {{ getRoleLabel(scope.row.role) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="加入时间" width="150">
          <template slot-scope="scope">
            {{ formatTime(scope.row.joinTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button v-if="scope.row.role !== 'owner' && canManageMembers" type="text" size="small"
              @click="handleChangeRole(scope.row)">
              修改角色
            </el-button>
            <el-button v-if="scope.row.role !== 'owner' && canManageMembers" type="text" size="small"
              @click="handleRemoveMember(scope.row)">
              移除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 空状态 -->
      <div v-if="!loading && memberList.length === 0" class="empty-state">
        <i class="el-icon-user"></i>
        <h3>暂无成员</h3>
        <p>邀请成员加入工作空间开始协作</p>
      </div>
    </div>

    <!-- 邀请成员对话框 -->
    <el-dialog title="邀请成员" :visible.sync="inviteDialogVisible" width="500px">
      <div class="invite-content">
        <div class="invite-link">
          <el-input v-model="inviteCode" readonly>
            <el-button slot="append" @click="copyInviteCode">复制邀请码</el-button>
          </el-input>
        </div>
        <div class="invite-tips">
          <p>分享此邀请码给其他用户，他们可以通过邀请码加入工作空间</p>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="inviteDialogVisible = false">关 闭</el-button>
        <el-button type="primary" @click="generateNewInviteCode">生成新邀请码</el-button>
      </div>
    </el-dialog>

    <!-- 修改角色对话框 -->
    <el-dialog title="修改角色" :visible.sync="roleDialogVisible" width="400px">
      <el-form ref="roleForm" :model="roleForm" label-width="80px">
        <el-form-item label="用户">
          <span>{{ roleForm.username }}</span>
        </el-form-item>
        <el-form-item label="角色" prop="role">
          <el-radio-group v-model="roleForm.role">
            <el-radio label="admin">管理员</el-radio>
            <el-radio label="member">成员</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="roleDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitChangeRole">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getWorkspace,
  getWorkspaceMembers,
  removeWorkspaceMember,
  updateMemberRole,
  generateInviteCode
} from '@/api/workspace/workspace'

export default {
  name: 'WorkspaceMembers',
  data () {
    return {
      loading: false,
      workspaceId: null,
      workspaceName: '',
      memberList: [],
      currentUserRole: '',
      inviteDialogVisible: false,
      roleDialogVisible: false,
      inviteCode: '',
      roleForm: {
        userId: null,
        username: '',
        role: ''
      }
    }
  },
  computed: {
    canManageMembers () {
      return ['owner', 'admin'].includes(this.currentUserRole)
    }
  },
  created () {
    this.workspaceId = this.$route.params.id
    this.loadWorkspaceInfo()
    this.loadMembers()
  },
  methods: {
    // 加载工作空间信息
    async loadWorkspaceInfo () {
      try {
        // 模拟API调用 - 实际项目中替换为真实API
        await new Promise(resolve => setTimeout(resolve, 300))

        const mockWorkspaces = {
          1: { name: '我的个人空间', role: 'owner' },
          2: { name: '研发团队协作空间', role: 'owner' },
          3: { name: '公共资源库', role: 'member' },
          4: { name: '项目Alpha', role: 'admin' }
        }

        const workspace = mockWorkspaces[this.workspaceId] || { name: '工作空间', role: 'member' }
        this.workspaceName = workspace.name
        this.currentUserRole = workspace.role
      } catch (error) {
        this.$message.error('加载工作空间信息失败')
      }
    },

    // 加载成员列表
    async loadMembers () {
      this.loading = true
      try {
        // 模拟API调用 - 实际项目中替换为真实API
        await new Promise(resolve => setTimeout(resolve, 600))

        const mockMembers = [
          {
            userId: 1,
            username: 'admin',
            nickname: '管理员',
            email: '<EMAIL>',
            avatar: '',
            role: 'owner',
            joinTime: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            userId: 2,
            username: 'zhangsan',
            nickname: '张三',
            email: '<EMAIL>',
            avatar: '',
            role: 'admin',
            joinTime: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            userId: 3,
            username: 'lisi',
            nickname: '李四',
            email: '<EMAIL>',
            avatar: '',
            role: 'member',
            joinTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
          },
          {
            userId: 4,
            username: 'wangwu',
            nickname: '王五',
            email: '<EMAIL>',
            avatar: '',
            role: 'member',
            joinTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
          }
        ]

        this.memberList = mockMembers
      } catch (error) {
        this.$message.error('加载成员列表失败')
      } finally {
        this.loading = false
      }
    },

    // 邀请成员
    async handleInvite () {
      try {
        const response = await generateInviteCode(this.workspaceId)
        this.inviteCode = response.data.inviteCode
        this.inviteDialogVisible = true
      } catch (error) {
        this.$message.error('生成邀请码失败')
      }
    },

    // 生成新邀请码
    async generateNewInviteCode () {
      try {
        const response = await generateInviteCode(this.workspaceId)
        this.inviteCode = response.data.inviteCode
        this.$message.success('新邀请码已生成')
      } catch (error) {
        this.$message.error('生成邀请码失败')
      }
    },

    // 复制邀请码
    copyInviteCode () {
      navigator.clipboard.writeText(this.inviteCode).then(() => {
        this.$message.success('邀请码已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    // 修改角色
    handleChangeRole (member) {
      this.roleForm = {
        userId: member.userId,
        username: member.username,
        role: member.role
      }
      this.roleDialogVisible = true
    },

    async submitChangeRole () {
      try {
        await updateMemberRole(this.workspaceId, this.roleForm.userId, this.roleForm.role)
        this.$message.success('角色修改成功')
        this.roleDialogVisible = false
        this.loadMembers()
      } catch (error) {
        this.$message.error('角色修改失败')
      }
    },

    // 移除成员
    async handleRemoveMember (member) {
      try {
        await this.$confirm(`确定要移除成员 ${member.username} 吗？`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await removeWorkspaceMember(this.workspaceId, member.userId)
        this.$message.success('移除成功')
        this.loadMembers()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('移除失败')
        }
      }
    },

    // 获取角色标签类型
    getRoleTypeTag (role) {
      const tagMap = {
        owner: 'danger',
        admin: 'warning',
        member: 'info'
      }
      return tagMap[role] || ''
    },

    // 获取角色标签文本
    getRoleLabel (role) {
      const labelMap = {
        owner: '所有者',
        admin: '管理员',
        member: '成员'
      }
      return labelMap[role] || role
    },

    // 格式化时间
    formatTime (time) {
      if (!time) return ''
      return new Date(time).toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.workspace-members {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.members-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-left {
    ::v-deep .el-breadcrumb__inner {
      color: #606266;
      font-weight: normal;
    }

    ::v-deep .el-breadcrumb__inner.is-link {
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

.members-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;

  i {
    font-size: 64px;
    color: #dcdfe6;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #606266;
  }

  p {
    margin: 0;
    font-size: 14px;
  }
}

.invite-content {
  .invite-link {
    margin-bottom: 20px;
  }

  .invite-tips {
    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
      line-height: 1.5;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .workspace-members {
    padding: 16px;
  }

  .members-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}
</style>
