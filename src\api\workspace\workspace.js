import request from '@/utils/request'

// 查询工作空间列表
export function listWorkspace(query) {
  return request({
    url: '/workspace/list',
    method: 'get',
    params: query
  })
}

// 查询工作空间详细
export function getWorkspace(id) {
  return request({
    url: '/workspace/' + id,
    method: 'get'
  })
}

// 新增工作空间
export function addWorkspace(data) {
  return request({
    url: '/workspace',
    method: 'post',
    data: data
  })
}

// 修改工作空间
export function updateWorkspace(data) {
  return request({
    url: '/workspace',
    method: 'put',
    data: data
  })
}

// 删除工作空间
export function delWorkspace(id) {
  return request({
    url: '/workspace/' + id,
    method: 'delete'
  })
}

// 加入工作空间
export function joinWorkspace(workspaceId, inviteCode) {
  return request({
    url: '/workspace/join',
    method: 'post',
    data: {
      workspaceId,
      inviteCode
    }
  })
}

// 退出工作空间
export function leaveWorkspace(workspaceId) {
  return request({
    url: '/workspace/leave/' + workspaceId,
    method: 'delete'
  })
}

// 获取工作空间成员列表
export function getWorkspaceMembers(workspaceId, query) {
  return request({
    url: '/workspace/' + workspaceId + '/members',
    method: 'get',
    params: query
  })
}

// 移除工作空间成员
export function removeWorkspaceMember(workspaceId, userId) {
  return request({
    url: '/workspace/' + workspaceId + '/members/' + userId,
    method: 'delete'
  })
}

// 更新成员角色
export function updateMemberRole(workspaceId, userId, role) {
  return request({
    url: '/workspace/' + workspaceId + '/members/' + userId + '/role',
    method: 'put',
    data: { role }
  })
}

// 生成邀请码
export function generateInviteCode(workspaceId) {
  return request({
    url: '/workspace/' + workspaceId + '/invite-code',
    method: 'post'
  })
}

// 获取工作空间统计信息
export function getWorkspaceStats(workspaceId) {
  return request({
    url: '/workspace/' + workspaceId + '/stats',
    method: 'get'
  })
}
