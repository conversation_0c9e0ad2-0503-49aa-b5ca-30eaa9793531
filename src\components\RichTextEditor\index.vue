<template>
  <div class="rich-text-editor">
    <!-- 编辑器工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <!-- 编辑器模式切换 -->
        <el-button-group size="small">
          <el-button :type="editorMode === 'rich' ? 'primary' : 'default'" @click="switchMode('rich')"
            icon="el-icon-edit">
            富文本
          </el-button>
          <el-button :type="editorMode === 'markdown' ? 'primary' : 'default'" @click="switchMode('markdown')"
            icon="el-icon-document">
            Markdown
          </el-button>
          <el-button :type="editorMode === 'block' ? 'primary' : 'default'" @click="switchMode('block')"
            icon="el-icon-menu">
            块编辑器
          </el-button>
        </el-button-group>

        <!-- 模板库 -->
        <el-dropdown @command="insertTemplate" style="margin-left: 12px;">
          <el-button size="small" icon="el-icon-folder">
            模板库<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item v-for="template in templates" :key="template.id" :command="template">
              {{ template.name }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <!-- 格式化工具 -->
        <div class="format-tools" v-if="editorMode === 'rich'">
          <el-button-group size="small" style="margin-left: 12px;">
            <el-button @click="formatText('bold')" icon="el-icon-bold" title="粗体"></el-button>
            <el-button @click="formatText('italic')" icon="el-icon-italic" title="斜体"></el-button>
            <el-button @click="formatText('underline')" icon="el-icon-underline" title="下划线"></el-button>
          </el-button-group>

          <el-button-group size="small" style="margin-left: 8px;">
            <el-button @click="insertElement('heading')" title="标题">H</el-button>
            <el-button @click="insertElement('list')" icon="el-icon-menu" title="列表"></el-button>
            <el-button @click="insertElement('link')" icon="el-icon-link" title="链接"></el-button>
            <el-button @click="insertElement('image')" icon="el-icon-picture" title="图片"></el-button>
            <el-button @click="insertElement('table')" icon="el-icon-s-grid" title="表格"></el-button>
            <el-button @click="insertElement('code')" icon="el-icon-cpu" title="代码块"></el-button>
          </el-button-group>
        </div>

        <!-- Markdown工具 -->
        <div class="markdown-tools" v-if="editorMode === 'markdown'">
          <el-button-group size="small" style="margin-left: 12px;">
            <el-button @click="insertMarkdown('**', '**')" title="粗体">B</el-button>
            <el-button @click="insertMarkdown('*', '*')" title="斜体">I</el-button>
            <el-button @click="insertMarkdown('# ', '')" title="标题">H</el-button>
            <el-button @click="insertMarkdown('- ', '')" title="列表">列表</el-button>
            <el-button @click="insertMarkdown('```\n', '\n```')" title="代码">代码</el-button>
            <el-button @click="insertMarkdown('> ', '')" title="引用">引用</el-button>
          </el-button-group>
        </div>
      </div>

      <div class="toolbar-right">
        <!-- 协作状态 -->
        <div class="collaboration-status" v-if="isCollaborating">
          <el-tag size="mini" type="success">
            <i class="el-icon-connection"></i> 协作中
          </el-tag>
          <span class="collaborator-count">{{ collaborators.length }} 人在线</span>
        </div>

        <!-- 评论系统 -->
        <CommentSystem v-if="isCollaborating" :document-id="documentId" :connection-id="connectionId"
          :collaborators="collaborators" :current-user="currentUser" />

        <!-- 版本历史 -->
        <VersionHistory :document-id="documentId" :current-content="content" :current-user="currentUser"
          @version-created="handleVersionCreated" @version-restored="handleVersionRestored" />

        <!-- 保存状态 -->
        <div class="save-status">
          <el-tag v-if="saving" size="mini" type="warning">
            <i class="el-icon-loading"></i> 保存中...
          </el-tag>
          <el-tag v-else-if="lastSaveTime" size="mini" type="success">
            <i class="el-icon-check"></i> 已保存
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 编辑器主体 -->
    <div class="editor-container" :class="{ 'split-view': showPreview && editorMode === 'markdown' }">
      <!-- 富文本编辑器 -->
      <div v-if="editorMode === 'rich'" class="rich-editor">
        <div ref="richEditor" class="rich-editor-content"></div>
        <!-- 协作光标 -->
        <CollaborativeCursor v-if="isCollaborating" :cursors="collaborativeCursors"
          :current-connection-id="connectionId" :editor-element="$refs.richEditor" />
      </div>

      <!-- Markdown编辑器 -->
      <div v-else-if="editorMode === 'markdown'" class="markdown-editor">
        <div class="markdown-input">
          <textarea ref="markdownEditor" v-model="content" class="markdown-textarea" placeholder="请输入Markdown内容..."
            @input="handleContentChange" @scroll="handleEditorScroll"
            @selectionchange="handleSelectionChange"></textarea>
          <!-- 协作光标 -->
          <CollaborativeCursor v-if="isCollaborating" :cursors="collaborativeCursors"
            :current-connection-id="connectionId" :editor-element="$refs.markdownEditor" />
        </div>
        <div v-if="showPreview" class="markdown-preview">
          <div class="preview-content" ref="preview" v-html="markdownPreview"></div>
        </div>
      </div>

      <!-- 块编辑器 -->
      <div v-else-if="editorMode === 'block'" class="block-editor">
        <div class="block-container">
          <div v-for="(block, index) in blocks" :key="block.id" class="block-item"
            :class="{ 'active': activeBlockIndex === index }" @click="setActiveBlock(index)">
            <div class="block-handle">
              <i class="el-icon-rank"></i>
            </div>
            <div class="block-content">
              <component :is="getBlockComponent(block.type)" v-model="block.content"
                :placeholder="getBlockPlaceholder(block.type)" @input="handleBlockChange(index)"
                @enter="addNewBlock(index)" @backspace="removeBlock(index)" />
            </div>
            <div class="block-actions">
              <el-dropdown @command="(command) => handleBlockAction(index, command)">
                <el-button type="text" size="mini" icon="el-icon-more"></el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="duplicate">复制块</el-dropdown-item>
                  <el-dropdown-item command="delete">删除块</el-dropdown-item>
                  <el-dropdown-item command="moveUp">上移</el-dropdown-item>
                  <el-dropdown-item command="moveDown">下移</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
            </div>
          </div>

          <!-- 添加新块按钮 -->
          <div class="add-block-button">
            <el-dropdown @command="addBlock">
              <el-button type="dashed" icon="el-icon-plus" style="width: 100%;">
                添加内容块
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="paragraph">段落</el-dropdown-item>
                <el-dropdown-item command="heading">标题</el-dropdown-item>
                <el-dropdown-item command="list">列表</el-dropdown-item>
                <el-dropdown-item command="quote">引用</el-dropdown-item>
                <el-dropdown-item command="code">代码</el-dropdown-item>
                <el-dropdown-item command="image">图片</el-dropdown-item>
                <el-dropdown-item command="table">表格</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 协作用户列表 -->
    <div class="collaboration-users" v-if="collaborators.length > 0">
      <div class="users-label">协作用户：</div>
      <div class="users-list">
        <el-avatar v-for="user in collaborators" :key="user.id" :src="user.avatar" :size="24" :title="user.nickname"
          style="margin-right: 4px">
          {{ user.nickname ? user.nickname.charAt(0) : 'U' }}
        </el-avatar>
      </div>
    </div>

    <!-- 模板管理对话框 -->
    <el-dialog title="模板管理" :visible.sync="showTemplateDialog" width="60%">
      <div class="template-manager">
        <div class="template-list">
          <div v-for="template in templates" :key="template.id" class="template-item" @click="selectTemplate(template)">
            <div class="template-info">
              <h4>{{ template.name }}</h4>
              <p>{{ template.description }}</p>
            </div>
            <div class="template-actions">
              <el-button type="text" size="mini" @click.stop="editTemplate(template)">编辑</el-button>
              <el-button type="text" size="mini" @click.stop="deleteTemplate(template)">删除</el-button>
            </div>
          </div>
        </div>
        <div class="template-preview" v-if="selectedTemplate">
          <h4>预览</h4>
          <div class="preview-content" v-html="selectedTemplate.preview"></div>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="showTemplateDialog = false">取消</el-button>
        <el-button type="primary" @click="createTemplate">新建模板</el-button>
        <el-button type="primary" @click="useTemplate" :disabled="!selectedTemplate">使用模板</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { marked } from 'marked'
import Quill from 'quill'
import enhancedCollaborationService from '@/utils/enhancedCollaboration'
import CollaborativeCursor from '@/components/CollaborativeCursor'
import CommentSystem from '@/components/CommentSystem'
import VersionHistory from '@/components/VersionHistory'

// 块编辑器组件
const BlockParagraph = {
  template: '<textarea v-model="value" :placeholder="placeholder" @input="$emit(\'input\', $event.target.value)" class="block-textarea"></textarea>',
  props: ['value', 'placeholder']
}

const BlockHeading = {
  template: '<input v-model="value" :placeholder="placeholder" @input="$emit(\'input\', $event.target.value)" class="block-heading">',
  props: ['value', 'placeholder']
}

const BlockList = {
  template: '<textarea v-model="value" :placeholder="placeholder" @input="$emit(\'input\', $event.target.value)" class="block-textarea"></textarea>',
  props: ['value', 'placeholder']
}

const BlockCode = {
  template: '<textarea v-model="value" :placeholder="placeholder" @input="$emit(\'input\', $event.target.value)" class="block-code"></textarea>',
  props: ['value', 'placeholder']
}

export default {
  name: 'RichTextEditor',
  components: {
    BlockParagraph,
    BlockHeading,
    BlockList,
    BlockCode,
    CollaborativeCursor,
    CommentSystem,
    VersionHistory
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    documentId: {
      type: String,
      required: true
    },
    enableCollaboration: {
      type: Boolean,
      default: true
    },
    showPreview: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      content: '',
      editorMode: 'rich', // rich, markdown, block
      richEditor: null,
      activeBlockIndex: -1,
      saving: false,
      lastSaveTime: null,

      // 协作相关
      isCollaborating: false,
      collaborators: [],
      connectionId: null,
      collaborativeCursors: new Map(),
      currentUser: {
        id: 1,
        nickname: '当前用户',
        avatar: ''
      },

      // 块编辑器
      blocks: [
        { id: 1, type: 'paragraph', content: '' }
      ],

      // 模板相关
      showTemplateDialog: false,
      selectedTemplate: null,
      templates: [
        {
          id: 1,
          name: '会议记录',
          description: '标准会议记录模板',
          content: '# 会议记录\n\n**会议时间：** \n**参会人员：** \n**会议主题：** \n\n## 会议内容\n\n## 决议事项\n\n## 后续行动',
          preview: '<h1>会议记录</h1><p><strong>会议时间：</strong></p><p><strong>参会人员：</strong></p>'
        },
        {
          id: 2,
          name: '项目计划',
          description: '项目计划文档模板',
          content: '# 项目计划\n\n## 项目概述\n\n## 项目目标\n\n## 时间安排\n\n## 资源分配\n\n## 风险评估',
          preview: '<h1>项目计划</h1><h2>项目概述</h2><h2>项目目标</h2>'
        }
      ]
    }
  },
  computed: {
    markdownPreview () {
      return this.content ? marked(this.content) : '<p class="empty-hint">开始编写内容...</p>'
    }
  },
  watch: {
    value: {
      immediate: true,
      handler (newVal) {
        if (newVal !== this.content) {
          this.content = newVal
          this.updateEditor()
        }
      }
    }
  },
  mounted () {
    this.initEditor()
    if (this.enableCollaboration) {
      this.initCollaboration()
    }
  },
  beforeDestroy () {
    this.disconnectCollaboration()
  },
  methods: {
    // 初始化编辑器
    initEditor () {
      if (this.editorMode === 'rich') {
        this.initRichEditor()
      }
    },

    // 初始化富文本编辑器
    initRichEditor () {
      this.$nextTick(() => {
        if (this.$refs.richEditor) {
          this.richEditor = new Quill(this.$refs.richEditor, {
            theme: 'snow',
            modules: {
              toolbar: false // 使用自定义工具栏
            }
          })

          this.richEditor.on('text-change', () => {
            this.content = this.richEditor.root.innerHTML
            this.handleContentChange()
          })
        }
      })
    },

    // 切换编辑器模式
    switchMode (mode) {
      if (this.editorMode === mode) return

      this.editorMode = mode
      this.$nextTick(() => {
        this.initEditor()
      })
    },

    // 更新编辑器内容
    updateEditor () {
      if (this.editorMode === 'rich' && this.richEditor) {
        this.richEditor.root.innerHTML = this.content
      }
    },

    // 处理内容变化
    handleContentChange () {
      this.$emit('input', this.content)

      // 实时协作
      if (this.isCollaborating && this.connectionId) {
        const operation = {
          type: 'content-change',
          content: this.content,
          timestamp: Date.now()
        }
        enhancedCollaborationService.sendOperation(this.connectionId, operation)
      }

      // 自动保存
      this.autoSave()
    },

    // 格式化文本（富文本模式）
    formatText (format) {
      if (!this.richEditor) return

      const selection = this.richEditor.getSelection()
      if (selection) {
        this.richEditor.format(format, true)
      }
    },

    // 插入元素（富文本模式）
    insertElement (type) {
      if (!this.richEditor) return

      const selection = this.richEditor.getSelection()
      if (!selection) return

      switch (type) {
        case 'heading':
          this.richEditor.format('header', 2)
          break
        case 'list':
          this.richEditor.format('list', 'ordered')
          break
        case 'link':
          const url = prompt('请输入链接地址:')
          if (url) {
            this.richEditor.format('link', url)
          }
          break
        case 'image':
          const imageUrl = prompt('请输入图片地址:')
          if (imageUrl) {
            this.richEditor.insertEmbed(selection.index, 'image', imageUrl)
          }
          break
        case 'table':
          // 插入简单表格
          const tableHtml = '<table><tr><td>单元格1</td><td>单元格2</td></tr><tr><td>单元格3</td><td>单元格4</td></tr></table>'
          this.richEditor.clipboard.dangerouslyPasteHTML(selection.index, tableHtml)
          break
        case 'code':
          this.richEditor.format('code-block', true)
          break
      }
    },

    // 插入Markdown语法
    insertMarkdown (before, after) {
      const textarea = this.$refs.markdownEditor
      if (!textarea) return

      const start = textarea.selectionStart
      const end = textarea.selectionEnd
      const selectedText = this.content.substring(start, end)

      const newText = before + selectedText + after
      this.content = this.content.substring(0, start) + newText + this.content.substring(end)

      this.$nextTick(() => {
        textarea.focus()
        textarea.setSelectionRange(start + before.length, start + before.length + selectedText.length)
      })
    },

    // 处理编辑器滚动同步
    handleEditorScroll () {
      if (this.showPreview && this.$refs.preview) {
        const editor = this.$refs.markdownEditor
        const preview = this.$refs.preview
        const scrollRatio = editor.scrollTop / (editor.scrollHeight - editor.clientHeight)
        preview.scrollTop = scrollRatio * (preview.scrollHeight - preview.clientHeight)
      }
    },

    // 块编辑器相关方法
    setActiveBlock (index) {
      this.activeBlockIndex = index
    },

    addBlock (type) {
      const newBlock = {
        id: Date.now(),
        type: type,
        content: ''
      }
      this.blocks.push(newBlock)
      this.activeBlockIndex = this.blocks.length - 1
    },

    addNewBlock (index) {
      const newBlock = {
        id: Date.now(),
        type: 'paragraph',
        content: ''
      }
      this.blocks.splice(index + 1, 0, newBlock)
      this.activeBlockIndex = index + 1
    },

    removeBlock (index) {
      if (this.blocks.length > 1) {
        this.blocks.splice(index, 1)
        this.activeBlockIndex = Math.max(0, index - 1)
      }
    },

    handleBlockChange (index) {
      this.updateBlockContent()
    },

    handleBlockAction (index, action) {
      switch (action) {
        case 'duplicate':
          const duplicatedBlock = { ...this.blocks[index], id: Date.now() }
          this.blocks.splice(index + 1, 0, duplicatedBlock)
          break
        case 'delete':
          this.removeBlock(index)
          break
        case 'moveUp':
          if (index > 0) {
            const block = this.blocks.splice(index, 1)[0]
            this.blocks.splice(index - 1, 0, block)
          }
          break
        case 'moveDown':
          if (index < this.blocks.length - 1) {
            const block = this.blocks.splice(index, 1)[0]
            this.blocks.splice(index + 1, 0, block)
          }
          break
      }
    },

    getBlockComponent (type) {
      const components = {
        paragraph: 'BlockParagraph',
        heading: 'BlockHeading',
        list: 'BlockList',
        code: 'BlockCode'
      }
      return components[type] || 'BlockParagraph'
    },

    getBlockPlaceholder (type) {
      const placeholders = {
        paragraph: '输入段落内容...',
        heading: '输入标题...',
        list: '输入列表项...',
        code: '输入代码...'
      }
      return placeholders[type] || '输入内容...'
    },

    updateBlockContent () {
      // 将块内容转换为统一格式
      let content = ''
      this.blocks.forEach(block => {
        switch (block.type) {
          case 'heading':
            content += `# ${block.content}\n\n`
            break
          case 'list':
            content += `- ${block.content}\n`
            break
          case 'code':
            content += `\`\`\`\n${block.content}\n\`\`\`\n\n`
            break
          default:
            content += `${block.content}\n\n`
        }
      })
      this.content = content
      this.handleContentChange()
    },

    // 协作相关方法
    initCollaboration () {
      if (!this.enableCollaboration) return

      this.connectionId = enhancedCollaborationService.connect(this.documentId, this.currentUser)

      enhancedCollaborationService.on(this.connectionId, 'operation', this.handleRemoteOperation)
      enhancedCollaborationService.on(this.connectionId, 'userJoined', this.handleUserJoined)
      enhancedCollaborationService.on(this.connectionId, 'userLeft', this.handleUserLeft)
      enhancedCollaborationService.on(this.connectionId, 'cursorUpdate', this.handleCursorUpdate)

      this.isCollaborating = true
      this.updateCollaborators()
      this.updateCursors()
    },

    disconnectCollaboration () {
      if (this.connectionId) {
        enhancedCollaborationService.disconnect(this.connectionId)
        this.connectionId = null
        this.isCollaborating = false
      }
    },

    updateCollaborators () {
      if (this.connectionId) {
        this.collaborators = enhancedCollaborationService.getCollaborators(this.documentId)
      }
    },

    updateCursors () {
      if (this.connectionId) {
        this.collaborativeCursors = enhancedCollaborationService.getCursors(this.documentId)
      }
    },

    handleRemoteOperation (operation) {
      if (operation.type === 'content-change') {
        this.content = operation.content
        this.updateEditor()
      }
    },

    handleUserJoined (userInfo) {
      this.updateCollaborators()
      this.updateCursors()
      this.$message.info(`${userInfo.nickname} 加入了协作`)
    },

    handleUserLeft (userInfo) {
      this.updateCollaborators()
      this.updateCursors()
      this.$message.info(`${userInfo.nickname} 离开了协作`)
    },

    handleCursorUpdate (cursorData) {
      this.updateCursors()
    },

    // 处理选择变化（光标位置同步）
    handleSelectionChange () {
      if (!this.isCollaborating || !this.connectionId) return

      const textarea = this.$refs.markdownEditor
      if (!textarea) return

      const cursor = {
        position: textarea.selectionStart,
        selection: textarea.selectionStart !== textarea.selectionEnd ? {
          start: textarea.selectionStart,
          end: textarea.selectionEnd
        } : null
      }

      enhancedCollaborationService.updateCursor(this.connectionId, cursor)
    },

    // 模板相关方法
    insertTemplate (template) {
      this.content = template.content
      this.updateEditor()
      this.handleContentChange()
      this.$message.success(`已插入模板：${template.name}`)
    },

    selectTemplate (template) {
      this.selectedTemplate = template
    },

    editTemplate (template) {
      // 编辑模板功能
      this.$prompt('请输入模板名称', '编辑模板', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: template.name
      }).then(({ value }) => {
        template.name = value
        this.$message.success('模板更新成功')
      })
    },

    deleteTemplate (template) {
      this.$confirm('确定要删除此模板吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.templates.findIndex(t => t.id === template.id)
        if (index > -1) {
          this.templates.splice(index, 1)
          this.$message.success('模板删除成功')
        }
      })
    },

    createTemplate () {
      this.$prompt('请输入模板名称', '新建模板', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        const newTemplate = {
          id: Date.now(),
          name: value,
          description: '自定义模板',
          content: this.content,
          preview: this.markdownPreview
        }
        this.templates.push(newTemplate)
        this.$message.success('模板创建成功')
      })
    },

    useTemplate () {
      if (this.selectedTemplate) {
        this.insertTemplate(this.selectedTemplate)
        this.showTemplateDialog = false
      }
    },

    // 自动保存
    autoSave () {
      if (this.saving) return

      this.saving = true
      setTimeout(() => {
        this.lastSaveTime = new Date()
        this.saving = false
        this.$emit('auto-save', this.content)
      }, 1000)
    },

    // 版本相关事件处理
    handleVersionCreated (version) {
      this.$emit('version-created', version)
      this.$message.success(`版本 ${version.number} 创建成功`)
    },

    handleVersionRestored (version) {
      this.content = version.content
      this.updateEditor()
      this.$emit('version-restored', version)
      this.$message.success(`已恢复到版本 ${version.number}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.rich-text-editor {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #ebeef5;
  background: #fafafa;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

.collaboration-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-container {
  min-height: 400px;

  &.split-view {
    display: flex;

    .markdown-input,
    .markdown-preview {
      flex: 1;
    }

    .markdown-input {
      border-right: 1px solid #ebeef5;
    }
  }
}

.rich-editor-content {
  min-height: 400px;
  padding: 12px;
}

.markdown-textarea {
  width: 100%;
  min-height: 400px;
  padding: 12px;
  border: none;
  outline: none;
  resize: vertical;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.6;
}

.markdown-preview {
  padding: 12px;
  background: #fafafa;
  overflow-y: auto;
}

.block-editor {
  padding: 12px;
}

.block-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.2s;

  &:hover,
  &.active {
    background: #f5f7fa;
  }
}

.block-handle {
  width: 20px;
  color: #c0c4cc;
  cursor: grab;
  margin-right: 8px;
  margin-top: 4px;
}

.block-content {
  flex: 1;
}

.block-actions {
  width: 30px;
  margin-left: 8px;
}

.block-textarea,
.block-heading,
.block-code {
  width: 100%;
  border: none;
  outline: none;
  background: transparent;
  resize: none;
  font-size: 14px;
  line-height: 1.6;
}

.block-heading {
  font-size: 18px;
  font-weight: bold;
}

.block-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  background: #f5f5f5;
  padding: 8px;
  border-radius: 4px;
}

.add-block-button {
  margin-top: 16px;
}

.collaboration-users {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-top: 1px solid #ebeef5;
  background: #fafafa;
}

.users-label {
  font-size: 12px;
  color: #909399;
  margin-right: 8px;
}

.template-manager {
  display: flex;
  height: 400px;
}

.template-list {
  flex: 1;
  border-right: 1px solid #ebeef5;
  padding-right: 16px;
  overflow-y: auto;
}

.template-item {
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    border-color: #409eff;
  }
}

.template-preview {
  flex: 1;
  padding-left: 16px;
  overflow-y: auto;
}
</style>
