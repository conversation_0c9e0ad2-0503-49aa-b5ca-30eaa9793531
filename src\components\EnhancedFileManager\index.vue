<template>
  <div class="enhanced-file-manager">
    <!-- 工具栏 -->
    <div class="file-toolbar">
      <div class="toolbar-left">
        <!-- 视图切换 -->
        <el-button-group size="small">
          <el-button 
            :type="viewMode === 'grid' ? 'primary' : 'default'" 
            icon="el-icon-view" 
            @click="viewMode = 'grid'"
          >
            网格
          </el-button>
          <el-button 
            :type="viewMode === 'list' ? 'primary' : 'default'" 
            icon="el-icon-menu" 
            @click="viewMode = 'list'"
          >
            列表
          </el-button>
          <el-button 
            :type="viewMode === 'timeline' ? 'primary' : 'default'" 
            icon="el-icon-time" 
            @click="viewMode = 'timeline'"
          >
            时间线
          </el-button>
        </el-button-group>

        <!-- 排序选项 -->
        <el-dropdown @command="handleSortChange" style="margin-left: 12px;">
          <el-button size="small">
            排序: {{ sortOptions[sortBy] }}<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="name">名称</el-dropdown-item>
            <el-dropdown-item command="modified">修改时间</el-dropdown-item>
            <el-dropdown-item command="size">大小</el-dropdown-item>
            <el-dropdown-item command="type">类型</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>

        <!-- 筛选器 -->
        <el-dropdown @command="handleFilterChange" style="margin-left: 8px;">
          <el-button size="small">
            筛选<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="all">全部文件</el-dropdown-item>
            <el-dropdown-item command="documents">文档</el-dropdown-item>
            <el-dropdown-item command="images">图片</el-dropdown-item>
            <el-dropdown-item command="videos">视频</el-dropdown-item>
            <el-dropdown-item command="folders">文件夹</el-dropdown-item>
            <el-dropdown-item command="recent">最近修改</el-dropdown-item>
            <el-dropdown-item command="favorites">收藏</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>

      <div class="toolbar-right">
        <!-- 搜索框 -->
        <el-input
          v-model="searchKeyword"
          placeholder="搜索文件..."
          size="small"
          style="width: 200px; margin-right: 12px;"
          prefix-icon="el-icon-search"
          clearable
          @input="handleSearch"
        />

        <!-- 操作按钮 -->
        <el-button-group size="small">
          <el-button icon="el-icon-plus" @click="showCreateMenu = !showCreateMenu">
            新建
          </el-button>
          <el-button icon="el-icon-upload" @click="handleUpload">
            上传
          </el-button>
          <el-button 
            icon="el-icon-delete" 
            :disabled="selectedFiles.length === 0"
            @click="handleBatchDelete"
          >
            删除
          </el-button>
        </el-button-group>

        <!-- 新建菜单 -->
        <div v-if="showCreateMenu" class="create-menu">
          <el-card shadow="hover">
            <div class="create-option" @click="createDocument">
              <i class="el-icon-document"></i>
              <span>新建文档</span>
            </div>
            <div class="create-option" @click="createFolder">
              <i class="el-icon-folder"></i>
              <span>新建文件夹</span>
            </div>
            <div class="create-option" @click="showTemplateDialog = true">
              <i class="el-icon-collection"></i>
              <span>从模板创建</span>
            </div>
          </el-card>
        </div>
      </div>
    </div>

    <!-- 面包屑导航 -->
    <div class="breadcrumb-nav">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/workspace' }">工作空间</el-breadcrumb-item>
        <el-breadcrumb-item :to="{ path: `/workspace/${workspaceId}/files` }">
          {{ workspaceName }}
        </el-breadcrumb-item>
        <el-breadcrumb-item 
          v-for="folder in breadcrumbs" 
          :key="folder.id"
          @click.native="navigateToFolder(folder)"
        >
          {{ folder.name }}
        </el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 文件列表 -->
    <div class="file-content" v-loading="loading">
      <!-- 网格视图 -->
      <div v-if="viewMode === 'grid'" class="files-grid">
        <div
          v-for="item in filteredFiles"
          :key="item.id"
          class="file-item"
          :class="{ 
            'is-selected': selectedFiles.includes(item.id),
            'is-collaborating': item.isCollaborating 
          }"
          @click="handleItemClick(item)"
          @contextmenu.prevent="handleContextMenu(item, $event)"
        >
          <div class="file-checkbox">
            <el-checkbox 
              :value="selectedFiles.includes(item.id)" 
              @change="handleSelectFile(item.id, $event)"
              @click.stop 
            />
          </div>
          
          <div class="file-icon">
            <i :class="getFileIcon(item)"></i>
            <!-- 协作指示器 -->
            <div v-if="item.isCollaborating" class="collaboration-badge">
              <i class="el-icon-connection"></i>
            </div>
          </div>
          
          <div class="file-name" :title="item.name">{{ item.name }}</div>
          
          <div class="file-info">
            <span class="file-size">{{ formatFileSize(item.size) }}</span>
            <span class="file-time">{{ formatTime(item.updateTime) }}</span>
          </div>

          <!-- 版本信息 -->
          <div v-if="item.versionInfo" class="version-info">
            <el-tag size="mini" type="info">v{{ item.versionInfo.current }}</el-tag>
          </div>

          <!-- 收藏标记 -->
          <div v-if="item.isFavorite" class="favorite-mark">
            <i class="el-icon-star-on"></i>
          </div>
        </div>
      </div>

      <!-- 列表视图 -->
      <div v-else-if="viewMode === 'list'" class="files-list">
        <el-table
          :data="filteredFiles"
          @selection-change="handleSelectionChange"
          @row-click="handleRowClick"
          stripe
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="名称" min-width="200">
            <template slot-scope="scope">
              <div class="file-name-cell">
                <i :class="getFileIcon(scope.row)"></i>
                <span>{{ scope.row.name }}</span>
                <el-tag v-if="scope.row.isCollaborating" size="mini" type="success">协作中</el-tag>
                <i v-if="scope.row.isFavorite" class="el-icon-star-on favorite-icon"></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="大小" width="100">
            <template slot-scope="scope">
              {{ formatFileSize(scope.row.size) }}
            </template>
          </el-table-column>
          <el-table-column label="修改时间" width="150">
            <template slot-scope="scope">
              {{ formatTime(scope.row.updateTime) }}
            </template>
          </el-table-column>
          <el-table-column label="版本" width="80">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.versionInfo" size="mini" type="info">
                v{{ scope.row.versionInfo.current }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button-group size="mini">
                <el-button icon="el-icon-view" @click.stop="previewFile(scope.row)"></el-button>
                <el-button icon="el-icon-more" @click.stop="showFileMenu(scope.row, $event)"></el-button>
              </el-button-group>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 时间线视图 -->
      <div v-else-if="viewMode === 'timeline'" class="files-timeline">
        <el-timeline>
          <el-timeline-item
            v-for="item in timelineFiles"
            :key="item.id"
            :timestamp="formatTime(item.updateTime)"
            placement="top"
          >
            <div class="timeline-item">
              <div class="timeline-content">
                <i :class="getFileIcon(item)"></i>
                <span class="file-name">{{ item.name }}</span>
                <span class="file-action">{{ item.action || '修改' }}</span>
              </div>
              <div class="timeline-meta">
                <span>{{ item.author || '未知用户' }}</span>
                <span>{{ formatFileSize(item.size) }}</span>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredFiles.length === 0 && !loading" class="empty-state">
        <i class="el-icon-folder-opened"></i>
        <p>{{ searchKeyword ? '没有找到匹配的文件' : '文件夹为空' }}</p>
        <el-button type="primary" @click="handleUpload">上传文件</el-button>
      </div>
    </div>

    <!-- 模板选择对话框 -->
    <el-dialog title="选择模板" :visible.sync="showTemplateDialog" width="60%">
      <div class="template-grid">
        <div
          v-for="template in templates"
          :key="template.id"
          class="template-item"
          @click="createFromTemplate(template)"
        >
          <div class="template-preview">
            <i :class="template.icon || 'el-icon-document'"></i>
          </div>
          <div class="template-info">
            <h4>{{ template.name }}</h4>
            <p>{{ template.description }}</p>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 文件上传 -->
    <el-upload
      ref="upload"
      :action="uploadUrl"
      :headers="uploadHeaders"
      :data="uploadData"
      :before-upload="handleBeforeUpload"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :show-file-list="false"
      multiple
      style="display: none;"
    >
    </el-upload>
  </div>
</template>

<script>
import { 
  getDocuments, 
  createDocument, 
  deleteDocument,
  favoriteDocument,
  unfavoriteDocument 
} from '@/api/workspace/file'
import { getTemplates } from '@/api/workspace/template'

export default {
  name: 'EnhancedFileManager',
  props: {
    workspaceId: {
      type: String,
      required: true
    },
    workspaceName: {
      type: String,
      default: '工作空间'
    }
  },
  data() {
    return {
      loading: false,
      viewMode: 'grid', // grid, list, timeline
      sortBy: 'name',
      sortOrder: 'asc',
      filterType: 'all',
      searchKeyword: '',
      showCreateMenu: false,
      showTemplateDialog: false,
      
      files: [],
      selectedFiles: [],
      breadcrumbs: [],
      currentFolderId: null,
      
      templates: [],
      
      sortOptions: {
        name: '名称',
        modified: '修改时间',
        size: '大小',
        type: '类型'
      },
      
      uploadUrl: process.env.VUE_APP_BASE_API + '/workspace/upload',
      uploadHeaders: {
        Authorization: 'Bearer ' + this.$store.getters.token
      }
    }
  },
  computed: {
    uploadData() {
      return {
        workspaceId: this.workspaceId,
        folderId: this.currentFolderId
      }
    },
    
    filteredFiles() {
      let files = [...this.files]
      
      // 搜索过滤
      if (this.searchKeyword) {
        files = files.filter(file =>
          file.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
        )
      }
      
      // 类型过滤
      if (this.filterType !== 'all') {
        files = files.filter(file => this.matchesFilter(file))
      }
      
      // 排序
      files.sort((a, b) => this.compareFiles(a, b))
      
      return files
    },
    
    timelineFiles() {
      return this.filteredFiles
        .sort((a, b) => new Date(b.updateTime) - new Date(a.updateTime))
        .slice(0, 50) // 限制显示数量
    }
  },
  mounted() {
    this.loadFiles()
    this.loadTemplates()
    this.bindGlobalEvents()
  },
  beforeDestroy() {
    this.unbindGlobalEvents()
  },
  methods: {
    // 加载文件列表
    async loadFiles() {
      this.loading = true
      try {
        const response = await getDocuments(this.workspaceId, {
          folderId: this.currentFolderId
        })
        this.files = response.data || []
      } catch (error) {
        this.$message.error('加载文件失败')
      } finally {
        this.loading = false
      }
    },
    
    // 加载模板
    async loadTemplates() {
      try {
        const response = await getTemplates(this.workspaceId)
        this.templates = response.data || []
      } catch (error) {
        console.error('加载模板失败', error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.enhanced-file-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.file-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #ebeef5;
  background: #fafafa;
}

.toolbar-left,
.toolbar-right {
  display: flex;
  align-items: center;
}

.create-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  margin-top: 8px;
  
  .create-option {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.3s;
    
    &:hover {
      background: #f5f7fa;
    }
    
    i {
      margin-right: 8px;
      font-size: 16px;
    }
  }
}

.breadcrumb-nav {
  padding: 12px 20px;
  border-bottom: 1px solid #ebeef5;
}

.file-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.file-item {
  position: relative;
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #c6e2ff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  &.is-selected {
    border-color: #409eff;
    background: #ecf5ff;
  }
  
  &.is-collaborating {
    border-color: #67c23a;
    
    &::after {
      content: '';
      position: absolute;
      top: 8px;
      right: 8px;
      width: 8px;
      height: 8px;
      background: #67c23a;
      border-radius: 50%;
    }
  }
}

.file-icon {
  position: relative;
  text-align: center;
  margin-bottom: 12px;
  
  i {
    font-size: 48px;
    color: #409eff;
  }
  
  .collaboration-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 16px;
    height: 16px;
    background: #67c23a;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    
    i {
      font-size: 10px;
      color: white;
    }
  }
}

.file-name {
  font-weight: 500;
  margin-bottom: 8px;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-info {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

.version-info {
  position: absolute;
  top: 8px;
  left: 8px;
}

.favorite-mark {
  position: absolute;
  top: 8px;
  right: 8px;
  color: #f56c6c;
}

.files-list {
  .file-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    
    i {
      font-size: 16px;
      color: #409eff;
    }
    
    .favorite-icon {
      color: #f56c6c;
    }
  }
}

.files-timeline {
  .timeline-item {
    .timeline-content {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 4px;
      
      i {
        font-size: 16px;
        color: #409eff;
      }
      
      .file-name {
        font-weight: 500;
      }
      
      .file-action {
        color: #909399;
        font-size: 12px;
      }
    }
    
    .timeline-meta {
      display: flex;
      gap: 12px;
      font-size: 12px;
      color: #909399;
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
  
  i {
    font-size: 64px;
    margin-bottom: 16px;
    display: block;
  }
  
  p {
    margin-bottom: 20px;
    font-size: 16px;
  }
}

.template-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.template-item {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  
  &:hover {
    border-color: #409eff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
  
  .template-preview {
    text-align: center;
    margin-bottom: 12px;
    
    i {
      font-size: 48px;
      color: #409eff;
    }
  }
  
  .template-info {
    h4 {
      margin: 0 0 8px 0;
      font-size: 14px;
    }
    
    p {
      margin: 0;
      font-size: 12px;
      color: #909399;
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-toolbar {
    flex-direction: column;
    gap: 12px;
  }
  
  .files-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }
  
  .template-grid {
    grid-template-columns: 1fr;
  }
}
</style>
