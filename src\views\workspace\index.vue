<template>
  <div class="workspace-container">
    <!-- 页面头部 -->
    <div class="workspace-header">
      <div class="header-left">
        <h2><i class="el-icon-folder-opened"></i> 工作空间</h2>
        <p>管理您的个人空间和团队协作空间</p>
      </div>
      <div class="header-right">
        <el-button icon="el-icon-cpu" @click="$router.push('/ai/chat')" style="margin-right: 8px">
          AI问答
        </el-button>
        <el-button type="primary" icon="el-icon-plus" @click="handleCreate">
          创建工作空间
        </el-button>
        <el-button type="success" icon="el-icon-connection" @click="handleJoin">
          加入工作空间
        </el-button>
      </div>
    </div>

    <!-- 工作空间类型切换 -->
    <div class="workspace-tabs">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="我的空间" name="my">
          <span slot="label">
            <i class="el-icon-user"></i> 我的空间
          </span>
        </el-tab-pane>
        <el-tab-pane label="公共空间" name="public">
          <span slot="label">
            <i class="el-icon-s-home"></i> 公共空间
          </span>
        </el-tab-pane>
        <el-tab-pane label="团队空间" name="team">
          <span slot="label">
            <i class="el-icon-s-custom"></i> 团队空间
          </span>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 工作空间列表 -->
    <div class="workspace-grid" v-loading="loading">
      <div v-for="workspace in workspaceList" :key="workspace.id" class="workspace-card"
        @click="enterWorkspace(workspace)">
        <div class="card-header">
          <div class="workspace-icon" :class="getWorkspaceIconClass(workspace.type)">
            <i :class="getWorkspaceIcon(workspace.type)"></i>
          </div>
          <div class="workspace-actions" @click.stop>
            <el-dropdown @command="handleCommand" trigger="click">
              <span class="el-dropdown-link">
                <i class="el-icon-more"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item :command="{ action: 'edit', workspace }">编辑</el-dropdown-item>
                <el-dropdown-item :command="{ action: 'members', workspace }"
                  v-if="workspace.type !== 'personal'">成员管理</el-dropdown-item>
                <el-dropdown-item :command="{ action: 'invite', workspace }"
                  v-if="workspace.type !== 'personal'">邀请成员</el-dropdown-item>
                <el-dropdown-item :command="{ action: 'leave', workspace }"
                  v-if="!workspace.isOwner && workspace.type !== 'personal'" divided>退出空间</el-dropdown-item>
                <el-dropdown-item :command="{ action: 'delete', workspace }" v-if="workspace.isOwner"
                  divided>删除空间</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <div class="card-body">
          <h3 class="workspace-name">{{ workspace.name }}</h3>
          <p class="workspace-description">{{ workspace.description || '暂无描述' }}</p>

          <div class="workspace-stats">
            <div class="stat-item">
              <i class="el-icon-document"></i>
              <span>{{ workspace.fileCount || 0 }} 文件</span>
            </div>
            <div class="stat-item" v-if="workspace.type !== 'personal'">
              <i class="el-icon-user"></i>
              <span>{{ workspace.memberCount || 0 }} 成员</span>
            </div>
            <div class="stat-item">
              <i class="el-icon-time"></i>
              <span>{{ formatTime(workspace.updateTime) }}</span>
            </div>
          </div>
        </div>

        <div class="card-footer">
          <div class="workspace-type">
            <el-tag :type="getWorkspaceTypeTag(workspace.type)" size="mini">
              {{ getWorkspaceTypeLabel(workspace.type) }}
            </el-tag>
          </div>
          <div class="workspace-role" v-if="workspace.type !== 'personal'">
            <el-tag :type="getRoleTypeTag(workspace.role)" size="mini">
              {{ getRoleLabel(workspace.role) }}
            </el-tag>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && workspaceList.length === 0" class="empty-state">
        <i class="el-icon-folder-opened"></i>
        <h3>暂无工作空间</h3>
        <p>创建您的第一个工作空间开始协作吧</p>
        <el-button type="primary" @click="handleCreate">创建工作空间</el-button>
      </div>
    </div>

    <!-- 创建/编辑工作空间对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px" @close="resetForm">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="空间名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入工作空间名称" maxlength="50" show-word-limit />
        </el-form-item>
        <el-form-item label="空间类型" prop="type" v-if="!form.id">
          <el-radio-group v-model="form.type">
            <el-radio label="personal">个人空间</el-radio>
            <el-radio label="team">团队空间</el-radio>
            <el-radio label="public">公共空间</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="空间描述" prop="description">
          <el-input v-model="form.description" type="textarea" :rows="3" placeholder="请输入工作空间描述" maxlength="200"
            show-word-limit />
        </el-form-item>
        <el-form-item label="访问权限" prop="accessLevel" v-if="form.type !== 'personal'">
          <el-radio-group v-model="form.accessLevel">
            <el-radio label="private">私有（仅成员可访问）</el-radio>
            <el-radio label="public">公开（所有人可访问）</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm" :loading="submitting">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 加入工作空间对话框 -->
    <el-dialog title="加入工作空间" :visible.sync="joinDialogVisible" width="500px">
      <el-form ref="joinForm" :model="joinForm" :rules="joinRules" label-width="100px">
        <el-form-item label="邀请码" prop="inviteCode">
          <el-input v-model="joinForm.inviteCode" placeholder="请输入邀请码" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="joinDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitJoin" :loading="joining">加 入</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWorkspace, addWorkspace, updateWorkspace, delWorkspace, joinWorkspace, leaveWorkspace } from '@/api/workspace/workspace'

export default {
  name: 'WorkspaceIndex',
  data () {
    return {
      loading: false,
      activeTab: 'my',
      workspaceList: [],
      dialogVisible: false,
      dialogTitle: '',
      submitting: false,
      joinDialogVisible: false,
      joining: false,
      form: {
        id: null,
        name: '',
        type: 'personal',
        description: '',
        accessLevel: 'private'
      },
      joinForm: {
        inviteCode: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入工作空间名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择空间类型', trigger: 'change' }
        ]
      },
      joinRules: {
        inviteCode: [
          { required: true, message: '请输入邀请码', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    filteredWorkspaces () {
      return this.workspaceList.filter(workspace => {
        switch (this.activeTab) {
          case 'my':
            return workspace.type === 'personal' || workspace.isOwner
          case 'public':
            return workspace.type === 'public'
          case 'team':
            return workspace.type === 'team'
          default:
            return true
        }
      })
    }
  },
  created () {
    this.loadWorkspaces()
  },
  methods: {
    // 加载工作空间列表
    async loadWorkspaces () {
      this.loading = true
      try {
        // 模拟API调用 - 实际项目中替换为真实API
        await new Promise(resolve => setTimeout(resolve, 800))

        const mockData = [
          {
            id: 1,
            name: '我的个人空间',
            type: 'personal',
            description: '个人文件存储空间',
            fileCount: 25,
            memberCount: 1,
            updateTime: new Date().toISOString(),
            isOwner: true,
            role: 'owner'
          },
          {
            id: 2,
            name: '研发团队协作空间',
            type: 'team',
            description: '用于研发团队日常协作和文件共享',
            fileCount: 156,
            memberCount: 8,
            updateTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            isOwner: true,
            role: 'owner'
          },
          {
            id: 3,
            name: '公共资源库',
            type: 'public',
            description: '公司公共资源和文档存储',
            fileCount: 89,
            memberCount: 25,
            updateTime: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            isOwner: false,
            role: 'member'
          },
          {
            id: 4,
            name: '项目Alpha',
            type: 'team',
            description: '项目Alpha相关文档和资料',
            fileCount: 42,
            memberCount: 5,
            updateTime: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            isOwner: false,
            role: 'admin'
          }
        ]

        // 根据当前标签页过滤数据
        this.workspaceList = mockData.filter(workspace => {
          switch (this.activeTab) {
            case 'my':
              return workspace.type === 'personal' || workspace.isOwner
            case 'public':
              return workspace.type === 'public'
            case 'team':
              return workspace.type === 'team'
            default:
              return true
          }
        })
      } catch (error) {
        this.$message.error('加载工作空间失败')
      } finally {
        this.loading = false
      }
    },

    // 标签页切换
    handleTabClick (tab) {
      this.activeTab = tab.name
      this.loadWorkspaces()
    },

    // 创建工作空间
    handleCreate () {
      this.resetForm()
      this.dialogTitle = '创建工作空间'
      this.dialogVisible = true
    },

    // 加入工作空间
    handleJoin () {
      this.joinForm.inviteCode = ''
      this.joinDialogVisible = true
    },

    // 进入工作空间
    enterWorkspace (workspace) {
      this.$router.push(`/workspace/${workspace.id}/files`)
    },

    // 下拉菜单操作
    handleCommand (command) {
      const { action, workspace } = command
      switch (action) {
        case 'edit':
          this.handleEdit(workspace)
          break
        case 'members':
          this.handleMembers(workspace)
          break
        case 'invite':
          this.handleInvite(workspace)
          break
        case 'leave':
          this.handleLeave(workspace)
          break
        case 'delete':
          this.handleDelete(workspace)
          break
      }
    },

    // 编辑工作空间
    handleEdit (workspace) {
      this.form = { ...workspace }
      this.dialogTitle = '编辑工作空间'
      this.dialogVisible = true
    },

    // 成员管理
    handleMembers (workspace) {
      this.$router.push(`/workspace/${workspace.id}/members`)
    },

    // 邀请成员
    handleInvite (workspace) {
      this.$router.push(`/workspace/${workspace.id}/invite`)
    },

    // 退出空间
    async handleLeave (workspace) {
      try {
        await this.$confirm('确定要退出该工作空间吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await leaveWorkspace(workspace.id)
        this.$message.success('退出成功')
        this.loadWorkspaces()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('退出失败')
        }
      }
    },

    // 删除空间
    async handleDelete (workspace) {
      try {
        await this.$confirm('确定要删除该工作空间吗？删除后无法恢复！', '警告', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await delWorkspace(workspace.id)
        this.$message.success('删除成功')
        this.loadWorkspaces()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    // 提交表单
    async submitForm () {
      try {
        await this.$refs.form.validate()
        this.submitting = true

        if (this.form.id) {
          await updateWorkspace(this.form)
          this.$message.success('更新成功')
        } else {
          await addWorkspace(this.form)
          this.$message.success('创建成功')
        }

        this.dialogVisible = false
        this.loadWorkspaces()
      } catch (error) {
        if (error !== false) {
          this.$message.error(this.form.id ? '更新失败' : '创建失败')
        }
      } finally {
        this.submitting = false
      }
    },

    // 提交加入
    async submitJoin () {
      try {
        await this.$refs.joinForm.validate()
        this.joining = true

        await joinWorkspace(null, this.joinForm.inviteCode)
        this.$message.success('加入成功')
        this.joinDialogVisible = false
        this.loadWorkspaces()
      } catch (error) {
        if (error !== false) {
          this.$message.error('加入失败，请检查邀请码是否正确')
        }
      } finally {
        this.joining = false
      }
    },

    // 重置表单
    resetForm () {
      this.form = {
        id: null,
        name: '',
        type: 'personal',
        description: '',
        accessLevel: 'private'
      }
      if (this.$refs.form) {
        this.$refs.form.resetFields()
      }
    },

    // 获取工作空间图标
    getWorkspaceIcon (type) {
      const iconMap = {
        personal: 'el-icon-user',
        team: 'el-icon-s-custom',
        public: 'el-icon-s-home'
      }
      return iconMap[type] || 'el-icon-folder'
    },

    // 获取工作空间图标样式类
    getWorkspaceIconClass (type) {
      return `workspace-icon-${type}`
    },

    // 获取工作空间类型标签
    getWorkspaceTypeTag (type) {
      const tagMap = {
        personal: 'info',
        team: 'success',
        public: 'warning'
      }
      return tagMap[type] || ''
    },

    // 获取工作空间类型标签文本
    getWorkspaceTypeLabel (type) {
      const labelMap = {
        personal: '个人',
        team: '团队',
        public: '公共'
      }
      return labelMap[type] || type
    },

    // 获取角色标签类型
    getRoleTypeTag (role) {
      const tagMap = {
        owner: 'danger',
        admin: 'warning',
        member: 'info'
      }
      return tagMap[role] || ''
    },

    // 获取角色标签文本
    getRoleLabel (role) {
      const labelMap = {
        owner: '所有者',
        admin: '管理员',
        member: '成员'
      }
      return labelMap[role] || role
    },

    // 格式化时间
    formatTime (time) {
      if (!time) return ''
      const date = new Date(time)
      const now = new Date()
      const diff = now - date

      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前'
      if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前'
      if (diff < 604800000) return Math.floor(diff / 86400000) + '天前'

      return date.toLocaleDateString()
    }
  }
}
</script>

<style lang="scss" scoped>
.workspace-container {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 84px);
}

.workspace-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .header-left {
    h2 {
      margin: 0 0 8px 0;
      color: #303133;
      font-size: 24px;
      font-weight: 600;

      i {
        color: #409eff;
        margin-right: 8px;
      }
    }

    p {
      margin: 0;
      color: #909399;
      font-size: 14px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 12px;

    .el-button {
      white-space: nowrap;
    }
  }
}

.workspace-tabs {
  margin-bottom: 24px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  ::v-deep .el-tabs__header {
    margin: 0;
    padding: 0 24px;
  }

  ::v-deep .el-tabs__nav-wrap::after {
    display: none;
  }
}

.workspace-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  min-height: 200px;
}

.workspace-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 20px 0 20px;

    .workspace-icon {
      width: 48px;
      height: 48px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      i {
        font-size: 24px;
        color: white;
      }

      &.workspace-icon-personal {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      }

      &.workspace-icon-team {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
      }

      &.workspace-icon-public {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
      }
    }

    .workspace-actions {
      .el-dropdown-link {
        color: #909399;
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        transition: all 0.3s ease;

        &:hover {
          background: #f5f7fa;
          color: #409eff;
        }
      }
    }
  }

  .card-body {
    padding: 16px 20px;

    .workspace-name {
      margin: 0 0 8px 0;
      font-size: 18px;
      font-weight: 600;
      color: #303133;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .workspace-description {
      margin: 0 0 16px 0;
      color: #606266;
      font-size: 14px;
      line-height: 1.5;
      height: 42px;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .workspace-stats {
      display: flex;
      gap: 16px;

      .stat-item {
        display: flex;
        align-items: center;
        color: #909399;
        font-size: 12px;

        i {
          margin-right: 4px;
          font-size: 14px;
        }
      }
    }
  }

  .card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px 20px 20px;

    .workspace-type,
    .workspace-role {
      .el-tag {
        border: none;
        font-size: 11px;
      }
    }
  }
}

.empty-state {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #909399;

  i {
    font-size: 64px;
    color: #dcdfe6;
    margin-bottom: 16px;
  }

  h3 {
    margin: 0 0 8px 0;
    font-size: 18px;
    color: #606266;
  }

  p {
    margin: 0 0 24px 0;
    font-size: 14px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .workspace-container {
    padding: 16px;
  }

  .workspace-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;

    .header-right {
      align-self: stretch;

      .el-button {
        margin-left: 0;
        margin-right: 12px;
      }
    }
  }

  .workspace-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style>
