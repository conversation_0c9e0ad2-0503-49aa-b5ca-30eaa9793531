<template>
  <div class="note-editor">
    <!-- 头部工具栏 -->
    <div class="note-header">
      <div class="header-left">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/workspace' }">工作空间</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: `/workspace/${workspaceId}/files` }">{{ workspaceName }}</el-breadcrumb-item>
          <el-breadcrumb-item>{{ isNew ? '新建笔记' : noteTitle }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>
      <div class="header-right">
        <!-- 协作状态指示器 -->
        <div v-if="isCollaborating" class="collaboration-indicator">
          <el-tag size="mini" type="success">
            <i class="el-icon-connection"></i> 协作中
          </el-tag>
          <span class="collaborator-count">{{ collaborators.length }} 人在线</span>
        </div>

        <!-- 保存状态 -->
        <div class="save-status">
          <el-tag v-if="saving" size="mini" type="warning">
            <i class="el-icon-loading"></i> 保存中...
          </el-tag>
          <el-tag v-else-if="lastSaveTime" size="mini" type="success">
            <i class="el-icon-check"></i> 已保存
          </el-tag>
        </div>

        <!-- 操作按钮 -->
        <el-button-group size="small">
          <el-button :type="viewMode === 'edit' ? 'primary' : 'info'" icon="el-icon-edit" @click="viewMode = 'edit'">
            编辑
          </el-button>
          <el-button :type="viewMode === 'preview' ? 'primary' : 'info'" icon="el-icon-view"
            @click="viewMode = 'preview'">
            预览
          </el-button>
          <el-button :type="viewMode === 'split' ? 'primary' : 'info'" icon="el-icon-s-unfold"
            @click="viewMode = 'split'">
            分屏
          </el-button>
        </el-button-group>

        <!-- 功能按钮组 -->
        <div class="function-buttons">
          <el-tooltip content="版本历史" placement="bottom">
            <el-button size="small" icon="el-icon-time" :type="rightPanel === 'version' ? 'primary' : 'default'"
              @click="toggleRightPanel('version')" />
          </el-tooltip>

          <el-tooltip content="评论" placement="bottom">
            <el-button size="small" icon="el-icon-chat-dot-round"
              :type="rightPanel === 'comment' ? 'primary' : 'default'" @click="toggleRightPanel('comment')">
              <el-badge :value="commentCount" :hidden="commentCount === 0" class="comment-badge">
              </el-badge>
            </el-button>
          </el-tooltip>

          <el-tooltip content="分享" placement="bottom">
            <el-button size="small" icon="el-icon-share" :type="rightPanel === 'share' ? 'primary' : 'default'"
              @click="toggleRightPanel('share')" />
          </el-tooltip>

          <el-tooltip content="模板" placement="bottom">
            <el-button size="small" icon="el-icon-collection" :type="rightPanel === 'template' ? 'primary' : 'default'"
              @click="toggleRightPanel('template')" />
          </el-tooltip>

          <el-tooltip content="更多" placement="bottom">
            <el-dropdown @command="handleMoreAction" trigger="click">
              <el-button size="small" icon="el-icon-more" />
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="export">导出文档</el-dropdown-item>
                <el-dropdown-item command="print">打印</el-dropdown-item>
                <el-dropdown-item command="fullscreen">全屏编辑</el-dropdown-item>
                <el-dropdown-item command="settings">编辑器设置</el-dropdown-item>
                <el-dropdown-item divided command="delete" style="color: #f56c6c;">删除文档</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </el-tooltip>
        </div>
        <el-button type="success" icon="el-icon-check" @click="handleSave" :loading="saving" size="small">
          保存
        </el-button>
        <el-button type="warning" icon="el-icon-share" @click="handleShare" size="small">
          分享
        </el-button>
      </div>
    </div>

    <!-- 笔记标题 -->
    <div class="note-title-section">
      <el-input v-model="noteTitle" placeholder="请输入笔记标题..." size="large" class="note-title-input"
        @blur="handleTitleChange" />
    </div>

    <!-- 编辑器区域 -->
    <div class="note-content" :class="{ 'split-view': viewMode === 'split' }">
      <!-- 编辑器 -->
      <div v-show="viewMode === 'edit' || viewMode === 'split'" class="editor-panel">
        <div class="editor-toolbar">
          <el-button-group size="small">
            <el-button icon="el-icon-bold" @click="insertMarkdown('**', '**')" title="粗体">B</el-button>
            <el-button icon="el-icon-italic" @click="insertMarkdown('*', '*')" title="斜体">I</el-button>
            <el-button icon="el-icon-underline" @click="insertMarkdown('<u>', '</u>')" title="下划线">U</el-button>
          </el-button-group>
          <el-button-group size="small" style="margin-left: 8px">
            <el-button @click="insertMarkdown('# ', '')" title="标题1">H1</el-button>
            <el-button @click="insertMarkdown('## ', '')" title="标题2">H2</el-button>
            <el-button @click="insertMarkdown('### ', '')" title="标题3">H3</el-button>
          </el-button-group>
          <el-button-group size="small" style="margin-left: 8px">
            <el-button @click="insertMarkdown('- ', '')" title="无序列表">列表</el-button>
            <el-button @click="insertMarkdown('1. ', '')" title="有序列表">序号</el-button>
            <el-button @click="insertMarkdown('> ', '')" title="引用">引用</el-button>
          </el-button-group>
          <el-button-group size="small" style="margin-left: 8px">
            <el-button @click="insertMarkdown('```\n', '\n```')" title="代码块">代码</el-button>
            <el-button @click="insertMarkdown('[', '](url)')" title="链接">链接</el-button>
            <el-button @click="insertMarkdown('![', '](url)')" title="图片">图片</el-button>
          </el-button-group>
        </div>
        <textarea ref="editor" v-model="noteContent" class="markdown-editor" placeholder="开始编写您的笔记..."
          @input="handleContentChange" @scroll="handleEditorScroll"></textarea>
      </div>

      <!-- 预览面板 -->
      <div v-show="viewMode === 'preview' || viewMode === 'split'" class="preview-panel">
        <div class="preview-content" ref="preview" v-html="renderedContent"></div>
      </div>
    </div>

    <!-- 协作用户列表 -->
    <div class="collaboration-users" v-if="collaborators.length > 0">
      <div class="users-label">协作用户：</div>
      <div class="users-list">
        <el-avatar v-for="user in collaborators" :key="user.id" :src="user.avatar" :size="24" :title="user.nickname"
          style="margin-right: 4px">
          {{ user.nickname ? user.nickname.charAt(0) : 'U' }}
        </el-avatar>
      </div>
    </div>

    <!-- 右侧功能面板 -->
    <div class="right-panel-overlay" v-if="rightPanel" @click="closeRightPanel">
      <div class="right-panel" :class="{ 'is-open': rightPanel }" @click.stop>
        <div class="panel-header">
          <h3 class="panel-title">{{ getPanelTitle() }}</h3>
          <el-button type="text" icon="el-icon-close" @click="rightPanel = null" class="close-btn" />
        </div>

        <div class="panel-content">
          <!-- 版本历史面板 -->
          <div v-if="rightPanel === 'version'" class="version-panel">
            <div class="version-actions">
              <el-button type="primary" size="small" icon="el-icon-plus" @click="createNewVersion">
                创建版本
              </el-button>
            </div>

            <div class="version-list">
              <div v-for="version in versionList" :key="version.id" class="version-item"
                :class="{ 'is-current': version.isCurrent }">
                <div class="version-header">
                  <div class="version-info">
                    <div class="version-number">{{ version.number }}</div>
                    <div class="version-meta">
                      <span class="version-author">{{ version.author }}</span>
                      <span class="version-time">{{ formatTime(version.createdAt) }}</span>
                    </div>
                  </div>
                  <div class="version-status">
                    <span class="status-badge" :class="{ current: version.isCurrent, draft: !version.isCurrent }">
                      {{ version.isCurrent ? '当前版本' : '历史版本' }}
                    </span>
                  </div>
                </div>
                <div class="version-title">{{ version.title }}</div>
                <div class="version-description" v-if="version.description">{{ version.description }}</div>
                <div class="version-changes" v-if="version.changes">
                  <div class="changes-summary">
                    <span class="change-stat additions">{{ version.changes.additions || 0 }} 行新增</span>
                    <span class="change-stat deletions">{{ version.changes.deletions || 0 }} 行删除</span>
                  </div>
                </div>
                <div class="version-actions">
                  <el-button-group size="mini">
                    <el-button icon="el-icon-view" @click="previewVersion(version)">预览</el-button>
                    <el-button icon="el-icon-refresh-left" @click="restoreVersion(version)">恢复</el-button>
                    <el-button icon="el-icon-s-data" @click="compareVersion(version)">对比</el-button>
                  </el-button-group>
                </div>
              </div>
            </div>
          </div>

          <!-- 评论面板 -->
          <div v-if="rightPanel === 'comment'" class="comment-panel">
            <div class="comment-input">
              <el-input type="textarea" v-model="newComment" placeholder="添加评论..." :rows="3" maxlength="500"
                show-word-limit />
              <div class="comment-actions">
                <el-button type="primary" size="small" @click="addComment" :disabled="!newComment.trim()">
                  发表评论
                </el-button>
              </div>
            </div>

            <div class="comment-list">
              <div v-for="comment in commentList" :key="comment.id" class="comment-item">
                <div class="comment-header">
                  <img :src="comment.avatar" class="comment-avatar" />
                  <div class="comment-info">
                    <span class="comment-author">{{ comment.author }}</span>
                    <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
                  </div>
                  <el-dropdown @command="handleCommentAction" trigger="click">
                    <el-button type="text" icon="el-icon-more" size="mini" />
                    <el-dropdown-menu slot="dropdown">
                      <el-dropdown-item :command="{ action: 'reply', comment }">回复</el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'edit', comment }">编辑</el-dropdown-item>
                      <el-dropdown-item :command="{ action: 'delete', comment }">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </el-dropdown>
                </div>
                <div class="comment-content">{{ comment.content }}</div>
                <div class="comment-actions">
                  <el-button type="text" size="mini" @click="likeComment(comment)"
                    :class="{ 'is-liked': comment.isLiked }">
                    <i class="el-icon-thumb"></i> {{ comment.likeCount }}
                  </el-button>
                  <el-button type="text" size="mini" @click="replyComment(comment)">
                    回复
                  </el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 分享面板 -->
          <div v-if="rightPanel === 'share'" class="share-panel">
            <div class="share-options">
              <div class="share-option">
                <h4>公开分享</h4>
                <p>任何人都可以通过链接访问</p>
                <el-switch v-model="shareSettings.isPublic" @change="updateShareSettings" />
              </div>

              <div class="share-option">
                <h4>密码保护</h4>
                <el-input v-model="shareSettings.password" placeholder="设置访问密码" show-password
                  @change="updateShareSettings" />
              </div>

              <div class="share-option">
                <h4>过期时间</h4>
                <el-date-picker v-model="shareSettings.expiresAt" type="datetime" placeholder="选择过期时间"
                  @change="updateShareSettings" />
              </div>
            </div>

            <div class="share-link" v-if="shareLink">
              <h4>分享链接</h4>
              <div class="link-container">
                <el-input :value="shareLink" readonly class="share-input" />
                <el-button type="primary" @click="copyShareLink" icon="el-icon-document-copy">
                  复制
                </el-button>
              </div>
            </div>

            <div class="share-stats" v-if="shareStats">
              <h4>分享统计</h4>
              <div class="stats-grid">
                <div class="stat-item">
                  <span class="stat-label">查看次数</span>
                  <span class="stat-value">{{ shareStats.viewCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">下载次数</span>
                  <span class="stat-value">{{ shareStats.downloadCount }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 模板面板 -->
          <div v-if="rightPanel === 'template'" class="template-panel">
            <div class="template-actions">
              <el-button type="primary" size="small" icon="el-icon-plus" @click="saveAsTemplate">
                保存为模板
              </el-button>
            </div>

            <div class="template-categories">
              <el-tabs v-model="activeTemplateTab">
                <el-tab-pane label="我的模板" name="my">
                  <div class="template-list">
                    <div v-for="template in myTemplates" :key="template.id" class="template-item"
                      @click="applyTemplate(template)">
                      <div class="template-icon">
                        <i :class="template.icon"></i>
                      </div>
                      <div class="template-info">
                        <div class="template-name">{{ template.name }}</div>
                        <div class="template-desc">{{ template.description }}</div>
                      </div>
                      <div class="template-actions">
                        <el-button type="text" size="mini" @click.stop="editTemplate(template)">
                          <i class="el-icon-edit"></i>
                        </el-button>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>

                <el-tab-pane label="公共模板" name="public">
                  <div class="template-list">
                    <div v-for="template in publicTemplates" :key="template.id" class="template-item"
                      @click="applyTemplate(template)">
                      <div class="template-icon">
                        <i :class="template.icon"></i>
                      </div>
                      <div class="template-info">
                        <div class="template-name">{{ template.name }}</div>
                        <div class="template-desc">{{ template.description }}</div>
                        <div class="template-meta">
                          <span class="template-author">{{ template.author }}</span>
                          <span class="template-usage">{{ template.usageCount }} 次使用</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-tab-pane>
              </el-tabs>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分享对话框 -->
    <el-dialog title="分享笔记" :visible.sync="shareDialogVisible" width="500px">
      <div class="share-content">
        <div class="share-link">
          <el-input v-model="shareLink" readonly>
            <el-button slot="append" @click="copyShareLink">复制链接</el-button>
          </el-input>
        </div>
        <div class="share-options">
          <el-form label-width="80px">
            <el-form-item label="权限">
              <el-radio-group v-model="sharePermission">
                <el-radio label="read">只读</el-radio>
                <el-radio label="edit">可编辑</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="有效期">
              <el-select v-model="shareExpire" @change="updateShareLink">
                <el-option label="1天" value="1" />
                <el-option label="7天" value="7" />
                <el-option label="30天" value="30" />
                <el-option label="永久" value="0" />
              </el-select>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { marked } from 'marked'
import collaborationService from '@/utils/collaboration'

export default {
  name: 'WorkspaceNote',
  data () {
    return {
      workspaceId: null,
      workspaceName: '',
      noteId: null,
      isNew: false,
      noteTitle: '',
      noteContent: '',
      viewMode: 'split', // edit, preview, split
      saving: false,
      lastSaveTime: null,

      // 协作相关
      collaborators: [],
      connectionId: null,
      isCollaborating: false,

      // 分享相关
      shareDialogVisible: false,
      shareLink: '',
      sharePermission: 'read',
      shareExpire: '7',

      // 右侧面板
      rightPanel: null, // version, comment, share, template

      // 版本控制
      versionList: [],

      // 评论系统
      commentList: [],
      newComment: '',
      commentCount: 0,

      // 分享设置
      shareSettings: {
        isPublic: false,
        password: '',
        expiresAt: null
      },
      shareStats: null,

      // 模板系统
      activeTemplateTab: 'my',
      myTemplates: [],
      publicTemplates: []
    }
  },
  computed: {
    renderedContent () {
      if (!this.noteContent) return '<p class="empty-hint">开始编写您的笔记...</p>'
      return marked(this.noteContent)
    }
  },
  created () {
    this.workspaceId = this.$route.params.id
    this.noteId = this.$route.params.noteId
    this.isNew = this.noteId === 'new'

    this.loadWorkspaceInfo()
    if (!this.isNew) {
      this.loadNote()
    }

    this.initCollaboration()
  },
  mounted () {
    // 自动保存
    this.autoSaveInterval = setInterval(() => {
      if (this.noteContent && this.noteTitle) {
        this.autoSave()
      }
    }, 30000) // 30秒自动保存一次

    // 添加键盘监听器
    this.addKeyboardListeners()
  },

  beforeDestroy () {
    // 清理定时器
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
    }

    // 移除键盘监听器
    this.removeKeyboardListeners()
  },
  beforeDestroy () {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
    }
    this.disconnectCollaboration()
  },
  methods: {
    // 加载工作空间信息
    async loadWorkspaceInfo () {
      try {
        // 模拟API调用 - 实际项目中替换为真实API
        await new Promise(resolve => setTimeout(resolve, 300))

        const mockWorkspaces = {
          1: '我的个人空间',
          2: '研发团队协作空间',
          3: '公共资源库',
          4: '项目Alpha'
        }

        this.workspaceName = mockWorkspaces[this.workspaceId] || '工作空间'
      } catch (error) {
        this.$message.error('加载工作空间信息失败')
      }
    },

    // 加载笔记内容
    async loadNote () {
      try {
        // 模拟加载笔记内容
        await new Promise(resolve => setTimeout(resolve, 500))
        this.noteTitle = '示例笔记'
        this.noteContent = `# 欢迎使用Markdown笔记

这是一个支持**Markdown**语法的笔记编辑器。

## 功能特性

- 实时预览
- 协同编辑
- 自动保存
- 分享功能

### 代码示例

\`\`\`javascript
function hello() {
  console.log('Hello, World!')
}
\`\`\`

> 这是一个引用块

**粗体文本** 和 *斜体文本*

[链接示例](https://example.com)`
      } catch (error) {
        this.$message.error('加载笔记失败')
      }
    },

    // 处理标题变化
    handleTitleChange () {
      if (this.noteTitle.trim()) {
        this.autoSave()

        // 实时协作功能
        if (this.isCollaborating && this.connectionId) {
          const operation = {
            type: 'title',
            content: this.noteTitle,
            timestamp: Date.now()
          }

          collaborationService.sendOperation(this.connectionId, operation)
        }
      }
    },

    // 初始化协作
    initCollaboration () {
      // 模拟当前用户信息
      const currentUser = {
        id: 1,
        nickname: '当前用户',
        avatar: ''
      }

      // 连接到协作服务
      this.connectionId = collaborationService.connect(
        `note_${this.workspaceId}_${this.noteId}`,
        currentUser
      )

      // 监听协作事件
      collaborationService.on(this.connectionId, 'operation', this.handleRemoteOperation)
      collaborationService.on(this.connectionId, 'userJoined', this.handleUserJoined)
      collaborationService.on(this.connectionId, 'userLeft', this.handleUserLeft)

      this.isCollaborating = true
      this.updateCollaborators()
    },

    // 断开协作连接
    disconnectCollaboration () {
      if (this.connectionId) {
        collaborationService.disconnect(this.connectionId)
        this.connectionId = null
        this.isCollaborating = false
      }
    },

    // 更新协作用户列表
    updateCollaborators () {
      if (this.connectionId) {
        this.collaborators = collaborationService.getCollaborators(`note_${this.workspaceId}_${this.noteId}`)
      } else {
        // 模拟协作用户（非协作模式）
        this.collaborators = [
          { id: 1, nickname: '张三', avatar: '', isActive: true, isEditing: false },
          { id: 2, nickname: '李四', avatar: '', isActive: true, isEditing: true }
        ]
      }
    },

    // 处理远程操作
    handleRemoteOperation (operation) {
      // 应用远程操作到本地内容
      switch (operation.type) {
        case 'replace':
          this.noteContent = operation.content
          break
        case 'title':
          this.noteTitle = operation.content
          break
      }
    },

    // 处理用户加入
    handleUserJoined (userInfo) {
      this.updateCollaborators()
      this.$message.info(`${userInfo.nickname} 加入了协作`)
    },

    // 处理用户离开
    handleUserLeft (userInfo) {
      this.updateCollaborators()
      this.$message.info(`${userInfo.nickname} 离开了协作`)
    },

    // 处理内容变化
    handleContentChange () {
      // 实时协作功能
      if (this.isCollaborating && this.connectionId) {
        // 更新用户活动时间
        collaborationService.updateActivity(this.connectionId)

        // 发送内容变更操作
        const operation = {
          type: 'replace',
          content: this.noteContent,
          timestamp: Date.now()
        }

        collaborationService.sendOperation(this.connectionId, operation)
        this.updateCollaborators()
      }
    },

    // 处理编辑器滚动同步
    handleEditorScroll () {
      if (this.viewMode === 'split' && this.$refs.preview) {
        // 同步滚动位置
        const editor = this.$refs.editor
        const preview = this.$refs.preview
        const scrollRatio = editor.scrollTop / (editor.scrollHeight - editor.clientHeight)
        preview.scrollTop = scrollRatio * (preview.scrollHeight - preview.clientHeight)
      }
    },

    // 插入Markdown语法
    insertMarkdown (before, after) {
      const editor = this.$refs.editor
      const start = editor.selectionStart
      const end = editor.selectionEnd
      const selectedText = this.noteContent.substring(start, end)

      const newText = before + selectedText + after
      this.noteContent = this.noteContent.substring(0, start) + newText + this.noteContent.substring(end)

      this.$nextTick(() => {
        editor.focus()
        editor.setSelectionRange(start + before.length, start + before.length + selectedText.length)
      })
    },

    // 保存笔记
    async handleSave () {
      if (!this.noteTitle.trim()) {
        this.$message.warning('请输入笔记标题')
        return
      }

      this.saving = true
      try {
        // 模拟保存API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        this.$message.success('保存成功')
        this.lastSaveTime = new Date()

        if (this.isNew) {
          // 保存成功后跳转到编辑页面
          this.$router.replace(`/workspace/${this.workspaceId}/note/1`)
          this.isNew = false
          this.noteId = '1'
        }
      } catch (error) {
        this.$message.error('保存失败')
      } finally {
        this.saving = false
      }
    },

    // 自动保存
    async autoSave () {
      if (this.saving) return

      try {
        // 模拟自动保存
        await new Promise(resolve => setTimeout(resolve, 200))
        this.lastSaveTime = new Date()
      } catch (error) {
        console.error('自动保存失败', error)
      }
    },

    // 分享笔记
    async handleShare () {
      try {
        // 模拟生成分享链接
        this.shareLink = `${window.location.origin}/share/note/${this.noteId}?token=abc123`
        this.shareDialogVisible = true
      } catch (error) {
        this.$message.error('生成分享链接失败')
      }
    },

    // 更新分享链接
    async updateShareLink () {
      // 根据权限和有效期更新分享链接
      this.shareLink = `${window.location.origin}/share/note/${this.noteId}?token=abc123&perm=${this.sharePermission}&expire=${this.shareExpire}`
    },

    // 复制分享链接
    copyShareLink () {
      navigator.clipboard.writeText(this.shareLink).then(() => {
        this.$message.success('链接已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    // 右侧面板相关方法
    toggleRightPanel (panelType) {
      if (this.rightPanel === panelType) {
        this.rightPanel = null
      } else {
        this.rightPanel = panelType
        this.loadPanelData(panelType)
      }
    },

    closeRightPanel () {
      this.rightPanel = null
    },

    getPanelTitle () {
      const titles = {
        version: '版本历史',
        comment: '评论',
        share: '分享设置',
        template: '模板库'
      }
      return titles[this.rightPanel] || ''
    },

    async loadPanelData (panelType) {
      switch (panelType) {
        case 'version':
          await this.loadVersionList()
          break
        case 'comment':
          await this.loadCommentList()
          break
        case 'share':
          await this.loadShareSettings()
          break
        case 'template':
          await this.loadTemplateList()
          break
      }
    },

    // 版本控制相关方法
    async loadVersionList () {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))

        this.versionList = [
          {
            id: 1,
            number: '1.0.0',
            title: '正式发布版本',
            description: '完成了所有核心功能的开发和测试，准备正式发布',
            author: '张三',
            createdAt: new Date(Date.now() - 86400000),
            isCurrent: true,
            changes: {
              additions: 156,
              deletions: 23
            }
          },
          {
            id: 2,
            number: '0.9.0',
            title: '预发布版本',
            description: '添加了用户反馈功能，修复了已知问题',
            author: '李四',
            createdAt: new Date(Date.now() - 172800000),
            isCurrent: false,
            changes: {
              additions: 89,
              deletions: 12
            }
          },
          {
            id: 3,
            number: '0.8.0',
            title: '功能完善版本',
            description: '优化了界面交互，提升了用户体验',
            author: '王五',
            createdAt: new Date(Date.now() - 259200000),
            isCurrent: false,
            changes: {
              additions: 67,
              deletions: 8
            }
          }
        ]
      } catch (error) {
        this.$message.error('加载版本列表失败')
      }
    },

    async createNewVersion () {
      try {
        const { value: title } = await this.$prompt('请输入版本标题', '创建新版本', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /.+/,
          inputErrorMessage: '版本标题不能为空'
        })

        // 模拟创建版本
        await new Promise(resolve => setTimeout(resolve, 500))

        const newVersion = {
          id: Date.now(),
          number: '1.1.0',
          title,
          author: '当前用户',
          createdAt: new Date(),
          isCurrent: true
        }

        // 更新版本列表
        this.versionList.forEach(v => v.isCurrent = false)
        this.versionList.unshift(newVersion)

        this.$message.success('版本创建成功')
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('创建版本失败')
        }
      }
    },

    previewVersion (version) {
      this.$message.info(`预览版本 ${version.number}`)
      // 实现版本预览逻辑
    },

    async restoreVersion (version) {
      try {
        await this.$confirm(`确定要恢复到版本 ${version.number} 吗？`, '确认恢复', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 模拟恢复版本
        await new Promise(resolve => setTimeout(resolve, 500))

        this.$message.success(`已恢复到版本 ${version.number}`)
        // 实现版本恢复逻辑
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('恢复版本失败')
        }
      }
    },

    compareVersion (version) {
      this.$message.info(`对比版本 ${version.number}`)
      // 实现版本对比逻辑
    },

    // 评论系统相关方法
    async loadCommentList () {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))

        this.commentList = [
          {
            id: 1,
            author: '李四',
            avatar: '',
            content: '这个文档写得很好，建议在第二段添加更多细节。',
            createdAt: new Date(Date.now() - 3600000),
            likeCount: 2,
            isLiked: false
          },
          {
            id: 2,
            author: '王五',
            avatar: '',
            content: '同意楼上的观点，另外格式可以再优化一下。',
            createdAt: new Date(Date.now() - 1800000),
            likeCount: 1,
            isLiked: true
          }
        ]

        this.commentCount = this.commentList.length
      } catch (error) {
        this.$message.error('加载评论失败')
      }
    },

    async addComment () {
      if (!this.newComment.trim()) return

      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))

        const comment = {
          id: Date.now(),
          author: '当前用户',
          avatar: '',
          content: this.newComment,
          createdAt: new Date(),
          likeCount: 0,
          isLiked: false
        }

        this.commentList.unshift(comment)
        this.commentCount = this.commentList.length
        this.newComment = ''

        this.$message.success('评论发表成功')
      } catch (error) {
        this.$message.error('发表评论失败')
      }
    },

    likeComment (comment) {
      comment.isLiked = !comment.isLiked
      comment.likeCount += comment.isLiked ? 1 : -1
    },

    replyComment (comment) {
      this.newComment = `@${comment.author} `
      this.$nextTick(() => {
        const textarea = this.$el.querySelector('.comment-input textarea')
        if (textarea) {
          textarea.focus()
          textarea.setSelectionRange(textarea.value.length, textarea.value.length)
        }
      })
    },

    handleCommentAction ({ action, comment }) {
      switch (action) {
        case 'reply':
          this.replyComment(comment)
          break
        case 'edit':
          this.$message.info('编辑评论功能开发中')
          break
        case 'delete':
          this.$confirm('确定要删除这条评论吗？', '确认删除', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }).then(() => {
            const index = this.commentList.findIndex(c => c.id === comment.id)
            if (index > -1) {
              this.commentList.splice(index, 1)
              this.commentCount = this.commentList.length
              this.$message.success('评论已删除')
            }
          }).catch(() => { })
          break
      }
    },

    // 分享设置相关方法
    async loadShareSettings () {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))

        this.shareSettings = {
          isPublic: false,
          password: '',
          expiresAt: null
        }

        this.shareStats = {
          viewCount: 15,
          downloadCount: 3
        }

        this.shareLink = `${window.location.origin}/share/note/${this.noteId}?token=abc123`
      } catch (error) {
        this.$message.error('加载分享设置失败')
      }
    },

    async updateShareSettings () {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 200))

        // 根据设置更新分享链接
        let link = `${window.location.origin}/share/note/${this.noteId}?token=abc123`
        if (this.shareSettings.password) {
          link += `&pwd=${this.shareSettings.password}`
        }
        if (this.shareSettings.expiresAt) {
          link += `&expires=${this.shareSettings.expiresAt.getTime()}`
        }

        this.shareLink = link
        this.$message.success('分享设置已更新')
      } catch (error) {
        this.$message.error('更新分享设置失败')
      }
    },

    copyShareLink () {
      navigator.clipboard.writeText(this.shareLink).then(() => {
        this.$message.success('分享链接已复制到剪贴板')
      }).catch(() => {
        this.$message.error('复制失败')
      })
    },

    // 模板相关方法
    async loadTemplateList () {
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 300))

        this.myTemplates = [
          {
            id: 1,
            name: '会议记录模板',
            description: '标准的会议记录格式',
            icon: 'el-icon-chat-dot-round',
            content: '# 会议记录\n\n**会议时间：** \n**参会人员：** \n**会议议题：** \n\n## 讨论内容\n\n## 决议事项\n\n## 后续行动'
          },
          {
            id: 2,
            name: '项目计划模板',
            description: '项目规划和进度跟踪',
            icon: 'el-icon-s-management',
            content: '# 项目计划\n\n## 项目概述\n\n## 目标和里程碑\n\n## 资源分配\n\n## 风险评估\n\n## 时间安排'
          }
        ]

        this.publicTemplates = [
          {
            id: 101,
            name: '技术文档模板',
            description: '标准的技术文档结构',
            icon: 'el-icon-document',
            author: '系统管理员',
            usageCount: 156,
            content: '# 技术文档\n\n## 概述\n\n## 技术架构\n\n## API文档\n\n## 部署指南\n\n## 常见问题'
          },
          {
            id: 102,
            name: '产品需求文档',
            description: 'PRD标准模板',
            icon: 'el-icon-s-data',
            author: '产品团队',
            usageCount: 89,
            content: '# 产品需求文档\n\n## 需求背景\n\n## 用户故事\n\n## 功能需求\n\n## 非功能需求\n\n## 验收标准'
          }
        ]
      } catch (error) {
        this.$message.error('加载模板列表失败')
      }
    },

    async applyTemplate (template) {
      try {
        await this.$confirm(`确定要应用模板"${template.name}"吗？当前内容将被替换。`, '确认应用模板', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        this.noteContent = template.content
        this.noteTitle = template.name.replace('模板', '')

        this.$message.success('模板应用成功')
        this.rightPanel = null
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('应用模板失败')
        }
      }
    },

    async saveAsTemplate () {
      if (!this.noteContent.trim()) {
        this.$message.warning('请先编写内容再保存为模板')
        return
      }

      try {
        const { value: name } = await this.$prompt('请输入模板名称', '保存为模板', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /.+/,
          inputErrorMessage: '模板名称不能为空'
        })

        // 模拟保存模板
        await new Promise(resolve => setTimeout(resolve, 500))

        const newTemplate = {
          id: Date.now(),
          name,
          description: '自定义模板',
          icon: 'el-icon-document',
          content: this.noteContent
        }

        this.myTemplates.unshift(newTemplate)
        this.$message.success('模板保存成功')
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('保存模板失败')
        }
      }
    },

    editTemplate (template) {
      this.$message.info(`编辑模板"${template.name}"功能开发中`)
    },

    // 更多操作处理
    handleMoreAction (command) {
      switch (command) {
        case 'export':
          this.exportDocument()
          break
        case 'print':
          this.printDocument()
          break
        case 'fullscreen':
          this.toggleFullscreen()
          break
        case 'settings':
          this.openEditorSettings()
          break
        case 'delete':
          this.deleteDocument()
          break
      }
    },

    exportDocument () {
      this.$message.info('导出功能开发中')
    },

    printDocument () {
      window.print()
    },

    toggleFullscreen () {
      if (document.fullscreenElement) {
        document.exitFullscreen()
      } else {
        document.documentElement.requestFullscreen()
      }
    },

    openEditorSettings () {
      this.$message.info('编辑器设置功能开发中')
    },

    async deleteDocument () {
      try {
        await this.$confirm('确定要删除这个文档吗？此操作不可恢复。', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 模拟删除操作
        await new Promise(resolve => setTimeout(resolve, 500))

        this.$message.success('文档已删除')
        this.$router.push(`/workspace/${this.workspaceId}`)
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },

    // 时间格式化工具方法
    formatTime (date) {
      if (!date) return ''

      const now = new Date()
      const diff = now - date

      if (diff < 60000) { // 1分钟内
        return '刚刚'
      } else if (diff < 3600000) { // 1小时内
        return `${Math.floor(diff / 60000)}分钟前`
      } else if (diff < 86400000) { // 1天内
        return `${Math.floor(diff / 3600000)}小时前`
      } else if (diff < 604800000) { // 1周内
        return `${Math.floor(diff / 86400000)}天前`
      } else {
        return date.toLocaleDateString()
      }
    },

    // 键盘监听器相关方法
    addKeyboardListeners () {
      document.addEventListener('keydown', this.handleKeydown)
    },

    removeKeyboardListeners () {
      document.removeEventListener('keydown', this.handleKeydown)
    },

    handleKeydown (event) {
      // ESC键关闭右侧面板
      if (event.key === 'Escape' && this.rightPanel) {
        this.rightPanel = null
        event.preventDefault()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.note-editor {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
  position: relative;
}

.note-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .header-left {
    ::v-deep .el-breadcrumb__inner {
      color: #606266;
      font-weight: normal;
    }

    ::v-deep .el-breadcrumb__inner.is-link {
      color: #409eff;

      &:hover {
        color: #66b1ff;
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 8px;

    .collaboration-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-right: 12px;

      .collaborator-count {
        font-size: 12px;
        color: #67c23a;
      }
    }

    .save-status {
      margin-right: 12px;
    }

    .function-buttons {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-right: 12px;

      .el-button {
        padding: 7px;

        &.el-button--primary {
          background: #409eff;
          border-color: #409eff;
        }
      }

      .comment-badge {
        ::v-deep .el-badge__content {
          top: 8px;
          right: 8px;
        }
      }
    }

    .el-button {
      white-space: nowrap;
    }
  }
}

.note-title-section {
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;

  .note-title-input {
    ::v-deep .el-input__inner {
      border: none;
      font-size: 24px;
      font-weight: 600;
      padding: 0;

      &:focus {
        border: none;
        box-shadow: none;
      }
    }
  }
}

.note-content {
  flex: 1;
  display: flex;
  overflow: hidden;

  &.split-view {

    .editor-panel,
    .preview-panel {
      width: 50%;
    }
  }

  .editor-panel {
    width: 100%;
    display: flex;
    flex-direction: column;
    background: white;
    border-right: 1px solid #e4e7ed;

    .editor-toolbar {
      padding: 12px 16px;
      border-bottom: 1px solid #e4e7ed;
      background: #fafbfc;
    }

    .markdown-editor {
      flex: 1;
      border: none;
      outline: none;
      padding: 20px;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      font-size: 14px;
      line-height: 1.6;
      resize: none;
      background: white;
    }
  }

  .preview-panel {
    width: 100%;
    background: white;
    overflow-y: auto;

    .preview-content {
      padding: 20px;

      ::v-deep {

        h1,
        h2,
        h3,
        h4,
        h5,
        h6 {
          margin-top: 24px;
          margin-bottom: 16px;
          font-weight: 600;
          line-height: 1.25;
        }

        h1 {
          font-size: 2em;
          border-bottom: 1px solid #eaecef;
          padding-bottom: 8px;
        }

        h2 {
          font-size: 1.5em;
          border-bottom: 1px solid #eaecef;
          padding-bottom: 8px;
        }

        h3 {
          font-size: 1.25em;
        }

        p {
          margin-bottom: 16px;
          line-height: 1.6;
        }

        code {
          background: #f6f8fa;
          padding: 2px 4px;
          border-radius: 3px;
          font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          font-size: 85%;
        }

        pre {
          background: #f6f8fa;
          padding: 16px;
          border-radius: 6px;
          overflow-x: auto;
          margin-bottom: 16px;

          code {
            background: none;
            padding: 0;
          }
        }

        blockquote {
          border-left: 4px solid #dfe2e5;
          padding-left: 16px;
          margin-left: 0;
          margin-bottom: 16px;
          color: #6a737d;
        }

        ul,
        ol {
          margin-bottom: 16px;
          padding-left: 24px;
        }

        li {
          margin-bottom: 4px;
        }

        .empty-hint {
          color: #909399;
          font-style: italic;
          text-align: center;
          margin-top: 60px;
        }
      }
    }
  }
}

.collaboration-users {
  display: flex;
  align-items: center;
  padding: 8px 20px;
  background: white;
  border-top: 1px solid #e4e7ed;

  .users-label {
    font-size: 12px;
    color: #909399;
    margin-right: 8px;
  }

  .users-list {
    display: flex;
    align-items: center;
  }
}

// 右侧面板遮罩层
.right-panel-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 999;
  transition: opacity 0.3s ease;
  backdrop-filter: blur(2px);
}

// 右侧面板样式 - 现代化设计
.right-panel {
  position: fixed;
  top: 0;
  right: -420px;
  width: 420px;
  height: 100vh;
  background: #ffffff;
  border-left: 1px solid #e1e4e8;
  box-shadow: -4px 0 24px rgba(0, 0, 0, 0.08);
  z-index: 1000;
  transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;

  &.is-open {
    right: 0;
  }

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e1e4e8;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: linear-gradient(90deg, #409eff 0%, #67c23a 50%, #e6a23c 100%);
    }

    .panel-title {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #24292e;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '';
        width: 4px;
        height: 18px;
        background: #409eff;
        border-radius: 2px;
      }
    }

    .close-btn {
      padding: 8px;
      color: #6a737d;
      border-radius: 6px;
      transition: all 0.2s;

      &:hover {
        color: #24292e;
        background: #f1f3f4;
      }
    }
  }

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding: 0;
    background: #fafbfc;

    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #d1d5da;
      border-radius: 3px;

      &:hover {
        background: #959da5;
      }
    }
  }
}

// 版本历史面板 - GitHub风格
.version-panel {
  .version-actions {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e1e4e8;
    background: white;

    .el-button {
      background: #2ea043;
      border-color: #2ea043;
      color: white;
      font-weight: 500;
      border-radius: 6px;
      padding: 8px 16px;
      transition: all 0.2s;

      &:hover {
        background: #2c974b;
        border-color: #2c974b;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(46, 160, 67, 0.2);
      }

      i {
        margin-right: 4px;
      }
    }
  }

  .version-list {
    padding: 0;

    .version-item {
      position: relative;
      padding: 16px 24px;
      border-bottom: 1px solid #e1e4e8;
      background: white;
      transition: all 0.2s;

      &:hover {
        background: #f6f8fa;
      }

      &.is-current {
        background: #f0fff4;
        border-left: 4px solid #2ea043;

        &::before {
          content: '';
          position: absolute;
          left: -1px;
          top: 0;
          bottom: 0;
          width: 3px;
          background: #2ea043;
        }

        .version-number {
          color: #2ea043;
        }
      }

      .version-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 8px;

        .version-info {
          flex: 1;

          .version-number {
            font-size: 16px;
            font-weight: 600;
            color: #0969da;
            margin-bottom: 4px;
            display: flex;
            align-items: center;
            gap: 8px;

            &::before {
              content: '';
              width: 8px;
              height: 8px;
              background: #0969da;
              border-radius: 50%;
              flex-shrink: 0;
            }
          }

          .version-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 12px;
            color: #656d76;

            .version-author {
              display: flex;
              align-items: center;
              gap: 4px;

              &::before {
                content: '👤';
                font-size: 10px;
              }
            }

            .version-time {
              display: flex;
              align-items: center;
              gap: 4px;

              &::before {
                content: '🕒';
                font-size: 10px;
              }
            }
          }
        }

        .version-status {
          display: flex;
          align-items: center;
          gap: 8px;

          .status-badge {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;

            &.current {
              background: #dafbe1;
              color: #1a7f37;
            }

            &.draft {
              background: #fff8c5;
              color: #9a6700;
            }
          }
        }
      }

      .version-title {
        font-size: 14px;
        color: #24292e;
        margin-bottom: 8px;
        font-weight: 500;
        line-height: 1.4;
      }

      .version-description {
        font-size: 12px;
        color: #656d76;
        margin-bottom: 12px;
        line-height: 1.4;
      }

      .version-actions {
        display: flex;
        gap: 8px;

        .el-button-group {
          .el-button {
            padding: 4px 8px;
            font-size: 12px;
            border-radius: 4px;
            border: 1px solid #d0d7de;
            background: white;
            color: #24292e;
            transition: all 0.2s;

            &:hover {
              background: #f3f4f6;
              border-color: #d0d7de;
            }

            &:first-child {
              border-top-right-radius: 0;
              border-bottom-right-radius: 0;
            }

            &:last-child {
              border-top-left-radius: 0;
              border-bottom-left-radius: 0;
              border-left: none;
            }

            &:not(:first-child):not(:last-child) {
              border-radius: 0;
              border-left: none;
            }
          }
        }
      }

      .version-changes {
        margin-top: 12px;
        padding: 8px 12px;
        background: #f6f8fa;
        border-radius: 6px;
        border-left: 3px solid #d0d7de;

        .changes-summary {
          font-size: 11px;
          color: #656d76;
          display: flex;
          gap: 12px;

          .change-stat {
            display: flex;
            align-items: center;
            gap: 4px;

            &.additions {
              color: #1a7f37;

              &::before {
                content: '+';
                font-weight: bold;
              }
            }

            &.deletions {
              color: #cf222e;

              &::before {
                content: '-';
                font-weight: bold;
              }
            }
          }
        }
      }
    }
  }
}

// 评论面板 - 现代化设计
.comment-panel {
  .comment-input {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e1e4e8;
    background: white;

    .el-textarea {
      ::v-deep .el-textarea__inner {
        border: 1px solid #d0d7de;
        border-radius: 6px;
        padding: 12px;
        font-size: 14px;
        line-height: 1.5;
        resize: vertical;
        min-height: 80px;
        transition: all 0.2s;

        &:focus {
          border-color: #0969da;
          box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
        }

        &::placeholder {
          color: #656d76;
        }
      }
    }

    .comment-actions {
      margin-top: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .el-button {
        background: #2ea043;
        border-color: #2ea043;
        color: white;
        font-weight: 500;
        border-radius: 6px;
        padding: 6px 16px;
        transition: all 0.2s;

        &:hover:not(:disabled) {
          background: #2c974b;
          border-color: #2c974b;
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(46, 160, 67, 0.2);
        }

        &:disabled {
          background: #94d3a2;
          border-color: #94d3a2;
          cursor: not-allowed;
        }
      }
    }
  }

  .comment-list {
    padding: 0;

    .comment-item {
      padding: 16px 24px;
      border-bottom: 1px solid #e1e4e8;
      background: white;
      transition: all 0.2s;

      &:hover {
        background: #f6f8fa;
      }

      &:last-child {
        border-bottom: none;
      }

      .comment-header {
        display: flex;
        align-items: flex-start;
        margin-bottom: 12px;

        .comment-avatar {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          margin-right: 12px;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: 600;
          font-size: 14px;
          flex-shrink: 0;
        }

        .comment-info {
          flex: 1;
          min-width: 0;

          .comment-author {
            font-weight: 600;
            color: #24292e;
            margin-right: 8px;
            font-size: 14px;
          }

          .comment-time {
            font-size: 12px;
            color: #656d76;
            display: flex;
            align-items: center;
            gap: 4px;

            &::before {
              content: '•';
            }
          }
        }

        .comment-menu {
          .el-button {
            padding: 4px;
            color: #656d76;
            border: none;
            background: transparent;

            &:hover {
              color: #24292e;
              background: #f3f4f6;
            }
          }
        }
      }

      .comment-content {
        margin-left: 48px;
        margin-bottom: 12px;
        line-height: 1.6;
        color: #24292e;
        font-size: 14px;
        word-wrap: break-word;
        background: #f6f8fa;
        padding: 12px 16px;
        border-radius: 8px;
        border: 1px solid #d0d7de;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 12px;
          left: -8px;
          width: 0;
          height: 0;
          border-top: 8px solid transparent;
          border-bottom: 8px solid transparent;
          border-right: 8px solid #f6f8fa;
        }

        &::after {
          content: '';
          position: absolute;
          top: 12px;
          left: -9px;
          width: 0;
          height: 0;
          border-top: 8px solid transparent;
          border-bottom: 8px solid transparent;
          border-right: 8px solid #d0d7de;
        }
      }

      .comment-actions {
        margin-left: 48px;
        display: flex;
        gap: 16px;

        .el-button {
          padding: 0;
          font-size: 12px;
          color: #656d76;
          background: transparent;
          border: none;
          display: flex;
          align-items: center;
          gap: 4px;
          transition: all 0.2s;

          &:hover {
            color: #0969da;
          }

          &.is-liked {
            color: #cf222e;

            i {
              transform: scale(1.1);
            }
          }

          i {
            font-size: 14px;
            transition: transform 0.2s;
          }
        }
      }
    }
  }
}

// 分享面板 - 现代化设计
.share-panel {
  .share-options {
    padding: 0;

    .share-option {
      padding: 20px 24px;
      border-bottom: 1px solid #e1e4e8;
      background: white;
      transition: all 0.2s;

      &:hover {
        background: #f6f8fa;
      }

      h4 {
        margin: 0 0 8px 0;
        font-size: 16px;
        color: #24292e;
        font-weight: 600;
        display: flex;
        align-items: center;
        gap: 8px;

        &::before {
          content: '🔗';
          font-size: 14px;
        }
      }

      p {
        margin: 0 0 16px 0;
        font-size: 13px;
        color: #656d76;
        line-height: 1.4;
      }

      .el-switch {
        ::v-deep .el-switch__core {
          border-color: #d0d7de;
          background: #d0d7de;

          &::after {
            background: white;
          }
        }

        ::v-deep .el-switch.is-checked .el-switch__core {
          border-color: #2ea043;
          background: #2ea043;
        }
      }

      .el-input {
        ::v-deep .el-input__inner {
          border: 1px solid #d0d7de;
          border-radius: 6px;
          padding: 8px 12px;
          transition: all 0.2s;

          &:focus {
            border-color: #0969da;
            box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
          }
        }
      }

      .el-date-editor {
        width: 100%;

        ::v-deep .el-input__inner {
          border: 1px solid #d0d7de;
          border-radius: 6px;
          padding: 8px 12px;

          &:focus {
            border-color: #0969da;
            box-shadow: 0 0 0 3px rgba(9, 105, 218, 0.1);
          }
        }
      }
    }
  }

  .share-link {
    padding: 20px 24px;
    border-bottom: 1px solid #e1e4e8;
    background: white;

    h4 {
      margin: 0 0 12px 0;
      font-size: 16px;
      color: #24292e;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '📋';
        font-size: 14px;
      }
    }

    .link-container {
      display: flex;
      gap: 8px;
      align-items: stretch;

      .share-input {
        flex: 1;

        ::v-deep .el-input__inner {
          border: 1px solid #d0d7de;
          border-radius: 6px 0 0 6px;
          background: #f6f8fa;
          font-family: 'SFMono-Regular', 'Consolas', 'Liberation Mono', 'Menlo', monospace;
          font-size: 12px;
          color: #24292e;
        }
      }

      .el-button {
        border-radius: 0 6px 6px 0;
        background: #0969da;
        border-color: #0969da;
        color: white;
        font-weight: 500;
        padding: 8px 16px;

        &:hover {
          background: #0860ca;
          border-color: #0860ca;
        }
      }
    }
  }

  .share-stats {
    padding: 20px 24px;
    background: white;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      color: #24292e;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 8px;

      &::before {
        content: '📊';
        font-size: 14px;
      }
    }

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 12px;

      .stat-item {
        padding: 16px;
        background: linear-gradient(135deg, #f6f8fa 0%, #ffffff 100%);
        border: 1px solid #d0d7de;
        border-radius: 8px;
        text-align: center;
        transition: all 0.2s;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .stat-label {
          display: block;
          font-size: 12px;
          color: #656d76;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .stat-value {
          display: block;
          font-size: 24px;
          font-weight: 700;
          color: #0969da;
          line-height: 1;
        }
      }
    }
  }
}

// 模板面板 - 现代化设计
.template-panel {
  .template-actions {
    padding: 20px 24px 16px;
    border-bottom: 1px solid #e1e4e8;
    background: white;

    .el-button {
      background: #0969da;
      border-color: #0969da;
      color: white;
      font-weight: 500;
      border-radius: 6px;
      padding: 8px 16px;
      transition: all 0.2s;

      &:hover {
        background: #0860ca;
        border-color: #0860ca;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(9, 105, 218, 0.2);
      }

      i {
        margin-right: 4px;
      }
    }
  }

  .template-categories {

    // 重置Element UI标签页的默认样式
    ::v-deep .el-tabs {

      // 移除所有默认的边框和间距
      .el-tabs__header {
        .el-tabs__nav-wrap {
          &::after {
            display: none !important;
            height: 0 !important;
            border: none !important;
          }
        }

        .el-tabs__nav {
          border: none !important;

          &::before {
            display: none !important;
          }

          &::after {
            display: none !important;
          }
        }
      }
    }

    .el-tabs {
      ::v-deep .el-tabs__header {
        margin: 0;
        background: white;
        border-bottom: 1px solid #e1e4e8;
        position: relative;

        .el-tabs__nav-wrap {
          padding: 0 24px;
          margin-bottom: 0;

          &::after {
            display: none;
          }

          .el-tabs__nav-scroll {
            overflow: visible;
          }

          .el-tabs__nav {
            border: none;

            .el-tabs__item {
              padding: 14px 20px 16px;
              font-weight: 500;
              color: #656d76;
              border: none !important;
              border-bottom: 2px solid transparent !important;
              transition: all 0.2s ease;
              position: relative;
              margin: 0 !important;
              background: transparent !important;
              height: auto;
              line-height: 1.4;

              &::before,
              &::after {
                display: none !important;
              }

              &:hover {
                color: #24292e;
                background: rgba(9, 105, 218, 0.05) !important;
                border-radius: 6px 6px 0 0;
              }

              &.is-active {
                color: #0969da !important;
                background: rgba(9, 105, 218, 0.08) !important;
                border-bottom: 2px solid #0969da !important;
                border-radius: 6px 6px 0 0;
                font-weight: 600;
              }
            }
          }
        }

        .el-tabs__active-bar {
          display: none !important;
        }
      }

      ::v-deep .el-tabs__content {
        padding: 0;
        margin: 0;
        border: none;

        .el-tab-pane {
          padding: 0;
          margin: 0;
          border: none;
        }
      }
    }

    // 额外的重置样式，确保没有任何重叠
    ::v-deep .el-tabs {
      .el-tabs__header {
        margin-bottom: 0 !important;

        .el-tabs__nav-wrap {
          margin-bottom: 0 !important;

          &::after {
            content: none !important;
            display: none !important;
            height: 0 !important;
            border: none !important;
          }
        }

        .el-tabs__nav {
          border: none !important;
          margin-bottom: 0 !important;

          &::before,
          &::after {
            content: none !important;
            display: none !important;
          }
        }

        .el-tabs__active-bar {
          display: none !important;
          height: 0 !important;
        }
      }

      .el-tabs__content {
        border: none !important;
        margin-top: 0 !important;
        padding-top: 0 !important;
      }
    }
  }

  .template-list {
    padding: 0;

    .template-item {
      display: flex;
      align-items: flex-start;
      padding: 16px 24px;
      border-bottom: 1px solid #e1e4e8;
      background: white;
      cursor: pointer;
      transition: all 0.2s;

      &:hover {
        background: #f6f8fa;
        transform: translateX(4px);
      }

      .template-icon {
        width: 48px;
        height: 48px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        margin-right: 16px;
        flex-shrink: 0;
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);
        }

        i {
          font-size: 20px;
          color: white;
          z-index: 1;
        }
      }

      .template-info {
        flex: 1;
        min-width: 0;

        .template-name {
          font-weight: 600;
          color: #24292e;
          margin-bottom: 6px;
          font-size: 15px;
          line-height: 1.3;
        }

        .template-desc {
          font-size: 13px;
          color: #656d76;
          margin-bottom: 8px;
          line-height: 1.4;
        }

        .template-meta {
          font-size: 12px;
          color: #8b949e;
          display: flex;
          align-items: center;
          gap: 12px;

          .template-author {
            display: flex;
            align-items: center;
            gap: 4px;

            &::before {
              content: '👤';
              font-size: 10px;
            }
          }

          .template-usage {
            display: flex;
            align-items: center;
            gap: 4px;

            &::before {
              content: '📊';
              font-size: 10px;
            }
          }
        }
      }

      .template-actions {
        opacity: 0;
        transition: opacity 0.2s;

        .el-button {
          padding: 6px;
          color: #656d76;
          background: transparent;
          border: 1px solid #d0d7de;
          border-radius: 6px;
          transition: all 0.2s;

          &:hover {
            color: #0969da;
            background: #f6f8fa;
            border-color: #0969da;
          }
        }
      }

      &:hover .template-actions {
        opacity: 1;
      }
    }
  }
}


.share-content {
  .share-link {
    margin-bottom: 20px;
  }

  .share-options {
    .el-form-item {
      margin-bottom: 16px;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .note-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;

    .header-right {
      .function-buttons {
        .el-button {
          padding: 6px;
        }
      }
    }
  }

  .note-content.split-view {
    flex-direction: column;

    .editor-panel,
    .preview-panel {
      width: 100%;
      height: 50%;
    }
  }

  .right-panel-overlay {
    backdrop-filter: none; // 移动端禁用模糊效果以提升性能
  }

  .right-panel {
    width: 100vw;
    right: -100vw;

    &.is-open {
      right: 0;
    }
  }
}
</style>
