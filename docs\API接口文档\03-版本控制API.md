# 版本控制 API 接口文档

## 📋 概述

版本控制模块提供文档的版本管理功能，包括版本创建、查看、对比、回滚、分支管理等。

## 🔗 基础信息

- **基础URL**: `/api/v1/workspace/{workspaceId}/documents/{documentId}`
- **认证方式**: Bear<PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 📚 版本管理 API

### 1. 获取版本列表

```http
GET /workspace/{workspaceId}/documents/{documentId}/versions
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | Number | 否 | 页码，默认1 |
| pageSize | Number | 否 | 每页数量，默认20 |
| branch | String | 否 | 分支名称，默认main |
| author | String | 否 | 作者筛选 |
| dateFrom | String | 否 | 开始日期 |
| dateTo | String | 否 | 结束日期 |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "v_123456",
        "number": 15,
        "title": "添加用户反馈章节",
        "message": "根据用户反馈添加了新的功能说明",
        "content": "文档内容...",
        "authorId": "user_123",
        "authorName": "张三",
        "authorAvatar": "avatar.jpg",
        "branch": "main",
        "parentId": "v_123455",
        "hash": "abc123def456",
        "size": 2048,
        "wordCount": 1500,
        "isCurrent": true,
        "isTag": false,
        "tags": ["v1.2.0"],
        "changes": [
          {
            "type": "add",
            "lineNumber": 25,
            "content": "新增内容行",
            "oldContent": null
          },
          {
            "type": "modify",
            "lineNumber": 30,
            "content": "修改后的内容",
            "oldContent": "原始内容"
          }
        ],
        "statistics": {
          "addedLines": 5,
          "deletedLines": 2,
          "modifiedLines": 3
        },
        "createdAt": "2024-01-20T15:30:00Z"
      }
    ],
    "total": 15,
    "page": 1,
    "pageSize": 20,
    "currentVersion": "v_123456"
  }
}
```

### 2. 获取版本详情

```http
GET /workspace/{workspaceId}/documents/{documentId}/versions/{versionId}
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "v_123456",
    "number": 15,
    "title": "添加用户反馈章节",
    "message": "根据用户反馈添加了新的功能说明",
    "content": "# 文档标题\n\n## 概述\n...",
    "authorId": "user_123",
    "authorName": "张三",
    "authorAvatar": "avatar.jpg",
    "branch": "main",
    "parentId": "v_123455",
    "hash": "abc123def456",
    "size": 2048,
    "wordCount": 1500,
    "metadata": {
      "editor": "markdown",
      "platform": "web",
      "userAgent": "Mozilla/5.0..."
    },
    "createdAt": "2024-01-20T15:30:00Z"
  }
}
```

### 3. 创建新版本

```http
POST /workspace/{workspaceId}/documents/{documentId}/versions
```

**请求体**

```json
{
  "title": "版本标题",
  "message": "版本说明",
  "content": "更新后的文档内容",
  "branch": "main",
  "parentId": "v_123455",
  "metadata": {
    "tags": ["重要更新"],
    "priority": "high"
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "版本创建成功",
  "data": {
    "id": "v_123457",
    "number": 16,
    "title": "版本标题",
    "hash": "def456ghi789",
    "authorId": "user_123",
    "createdAt": "2024-01-20T16:00:00Z",
    "changes": [
      {
        "type": "add",
        "lineNumber": 35,
        "content": "新增的内容"
      }
    ]
  }
}
```

### 4. 版本对比

```http
GET /workspace/{workspaceId}/documents/{documentId}/versions/compare
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| version1 | String | 是 | 版本1 ID |
| version2 | String | 是 | 版本2 ID |
| format | String | 否 | 对比格式：unified/side-by-side |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "version1": {
      "id": "v_123455",
      "number": 14,
      "title": "版本14",
      "authorName": "张三",
      "createdAt": "2024-01-19T10:00:00Z"
    },
    "version2": {
      "id": "v_123456",
      "number": 15,
      "title": "版本15",
      "authorName": "李四",
      "createdAt": "2024-01-20T15:30:00Z"
    },
    "differences": [
      {
        "type": "add",
        "lineNumber": 25,
        "content": "+ 新增的内容行",
        "context": {
          "before": ["上下文行1", "上下文行2"],
          "after": ["上下文行3", "上下文行4"]
        }
      },
      {
        "type": "delete",
        "lineNumber": 30,
        "content": "- 删除的内容行"
      },
      {
        "type": "modify",
        "lineNumber": 35,
        "oldContent": "原始内容",
        "newContent": "修改后的内容"
      }
    ],
    "statistics": {
      "addedLines": 5,
      "deletedLines": 2,
      "modifiedLines": 3,
      "totalChanges": 10
    }
  }
}
```

### 5. 回滚到指定版本

```http
POST /workspace/{workspaceId}/documents/{documentId}/versions/{versionId}/rollback
```

**请求体**

```json
{
  "message": "回滚到版本15",
  "createNewVersion": true
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "回滚成功",
  "data": {
    "newVersionId": "v_123458",
    "rollbackToVersion": "v_123456",
    "message": "回滚到版本15"
  }
}
```

## 🏷️ 标签管理 API

### 1. 获取版本标签

```http
GET /workspace/{workspaceId}/documents/{documentId}/versions/tags
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tags": [
      {
        "id": "tag_123",
        "name": "v1.0.0",
        "description": "第一个正式版本",
        "versionId": "v_123450",
        "versionNumber": 10,
        "authorId": "user_123",
        "authorName": "张三",
        "createdAt": "2024-01-15T10:00:00Z"
      }
    ]
  }
}
```

### 2. 创建版本标签

```http
POST /workspace/{workspaceId}/documents/{documentId}/versions/{versionId}/tags
```

**请求体**

```json
{
  "name": "v1.1.0",
  "description": "新功能发布版本",
  "type": "release"
}
```

### 3. 删除版本标签

```http
DELETE /workspace/{workspaceId}/documents/{documentId}/versions/tags/{tagId}
```

## 🌿 分支管理 API

### 1. 获取分支列表

```http
GET /workspace/{workspaceId}/documents/{documentId}/branches
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "branches": [
      {
        "id": "branch_123",
        "name": "main",
        "description": "主分支",
        "isDefault": true,
        "headVersionId": "v_123456",
        "headVersionNumber": 15,
        "authorId": "user_123",
        "authorName": "张三",
        "versionCount": 15,
        "lastCommitAt": "2024-01-20T15:30:00Z",
        "createdAt": "2024-01-01T00:00:00Z"
      },
      {
        "id": "branch_456",
        "name": "feature/new-ui",
        "description": "新UI设计分支",
        "isDefault": false,
        "headVersionId": "v_123460",
        "headVersionNumber": 3,
        "parentBranch": "main",
        "authorId": "user_456",
        "authorName": "李四",
        "versionCount": 3,
        "lastCommitAt": "2024-01-19T14:20:00Z",
        "createdAt": "2024-01-18T09:00:00Z"
      }
    ]
  }
}
```

### 2. 创建分支

```http
POST /workspace/{workspaceId}/documents/{documentId}/branches
```

**请求体**

```json
{
  "name": "feature/user-feedback",
  "description": "用户反馈功能分支",
  "fromVersionId": "v_123456",
  "fromBranch": "main"
}
```

### 3. 合并分支

```http
POST /workspace/{workspaceId}/documents/{documentId}/branches/{branchId}/merge
```

**请求体**

```json
{
  "targetBranch": "main",
  "message": "合并用户反馈功能",
  "strategy": "merge"
}
```

### 4. 删除分支

```http
DELETE /workspace/{workspaceId}/documents/{documentId}/branches/{branchId}
```

## 📊 版本统计 API

### 1. 获取版本统计

```http
GET /workspace/{workspaceId}/documents/{documentId}/versions/statistics
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "overview": {
      "totalVersions": 15,
      "totalBranches": 3,
      "totalTags": 5,
      "totalAuthors": 4
    },
    "activity": {
      "thisWeek": 8,
      "thisMonth": 15,
      "averagePerWeek": 3.5
    },
    "authors": [
      {
        "authorId": "user_123",
        "authorName": "张三",
        "versionCount": 8,
        "lastCommitAt": "2024-01-20T15:30:00Z"
      }
    ],
    "timeline": [
      {
        "date": "2024-01-20",
        "versionCount": 2,
        "authors": ["张三", "李四"]
      }
    ]
  }
}
```

## 📥 导出功能 API

### 1. 导出版本历史

```http
GET /workspace/{workspaceId}/documents/{documentId}/versions/export
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| format | String | 否 | 导出格式：pdf/excel/json |
| branch | String | 否 | 分支名称 |
| dateFrom | String | 否 | 开始日期 |
| dateTo | String | 否 | 结束日期 |

### 2. 导出版本对比

```http
GET /workspace/{workspaceId}/documents/{documentId}/versions/compare/export
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| version1 | String | 是 | 版本1 ID |
| version2 | String | 是 | 版本2 ID |
| format | String | 否 | 导出格式：pdf/html |

## 🚨 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 版本不存在 |
| 409 | 版本冲突 |
| 422 | 版本数据无效 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 📝 使用示例

### JavaScript/Axios

```javascript
// 创建新版本
const createVersion = async (workspaceId, documentId, versionData) => {
  try {
    const response = await axios.post(
      `/api/v1/workspace/${workspaceId}/documents/${documentId}/versions`,
      versionData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    )
    return response.data
  } catch (error) {
    console.error('创建版本失败:', error)
    throw error
  }
}

// 版本对比
const compareVersions = async (workspaceId, documentId, version1, version2) => {
  try {
    const response = await axios.get(
      `/api/v1/workspace/${workspaceId}/documents/${documentId}/versions/compare`,
      {
        params: { version1, version2 },
        headers: { 'Authorization': `Bearer ${token}` }
      }
    )
    return response.data
  } catch (error) {
    console.error('版本对比失败:', error)
    throw error
  }
}
```
