<template>
  <div class="mobile-material-detail">
    <!-- 头部 -->
    <div class="mobile-header">
      <div class="header-content">
        <h1 class="material-name">{{ materialInfo.name || '加载中...' }}</h1>
        <div class="material-type" v-if="materialInfo.materialType">
          <el-tag :type="materialInfo.materialType === 'raw' ? 'primary' : 'success'" size="small">
            {{ materialInfo.materialType === 'raw' ? '原料' : '辅料' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="mobile-content" v-loading="loading">
      <div v-if="!loading && materialInfo.id">
        <!-- 基本信息卡片 -->
        <div class="info-card">
          <div class="card-title">
            <i class="el-icon-info"></i>
            基本信息
          </div>
          <div class="info-grid">
            <div class="info-item" v-if="materialInfo.description">
              <label>简介描述</label>
              <div class="info-value">{{ materialInfo.description }}</div>
            </div>
            <div class="info-item" v-if="materialInfo.type">
              <label>类型</label>
              <div class="info-value">
                <el-tag size="mini" :type="getTypeTagType(materialInfo.type)">
                  {{ materialInfo.type }}
                </el-tag>
              </div>
            </div>
            <div class="info-item" v-if="materialInfo.molecularFormula">
              <label>分子式</label>
              <div class="info-value">{{ materialInfo.molecularFormula }}</div>
            </div>
            <div class="info-item" v-if="materialInfo.molecularWeight">
              <label>分子质量</label>
              <div class="info-value">{{ materialInfo.molecularWeight }}</div>
            </div>
          </div>
        </div>

        <!-- 详细信息卡片 -->
        <div class="info-card" v-if="hasDetailInfo">
          <div class="card-title">
            <i class="el-icon-document"></i>
            详细信息
          </div>
          <div class="detail-content">
            <div class="detail-item" v-if="materialInfo.trait">
              <h4>性状</h4>
              <p>{{ materialInfo.trait }}</p>
            </div>
            <div class="detail-item" v-if="materialInfo.identification">
              <h4>鉴别</h4>
              <p>{{ materialInfo.identification }}</p>
            </div>
            <div class="detail-item" v-if="materialInfo.inspection">
              <h4>检查</h4>
              <p>{{ materialInfo.inspection }}</p>
            </div>
            <div class="detail-item" v-if="materialInfo.assay">
              <h4>含量测定</h4>
              <p>{{ materialInfo.assay }}</p>
            </div>
            <div class="detail-item" v-if="materialInfo.storage">
              <h4>贮藏</h4>
              <p>{{ materialInfo.storage }}</p>
            </div>
            <div class="detail-item" v-if="materialInfo.preparation">
              <h4>制剂</h4>
              <p>{{ materialInfo.preparation }}</p>
            </div>
            <div class="detail-item" v-if="materialInfo.actionAndUse">
              <h4>作用与用途</h4>
              <p>{{ materialInfo.actionAndUse }}</p>
            </div>
            <div class="detail-item" v-if="materialInfo.usageAndDosage">
              <h4>用法与用量</h4>
              <p>{{ materialInfo.usageAndDosage }}</p>
            </div>
            <div class="detail-item" v-if="materialInfo.adverseReaction">
              <h4>不良反应</h4>
              <p>{{ materialInfo.adverseReaction }}</p>
            </div>
            <div class="detail-item" v-if="materialInfo.precautions">
              <h4>注意事项</h4>
              <p>{{ materialInfo.precautions }}</p>
            </div>
            <div class="detail-item" v-if="materialInfo.withdrawalPeriod">
              <h4>休药期</h4>
              <p>{{ materialInfo.withdrawalPeriod }}</p>
            </div>
            <div class="detail-item" v-if="materialInfo.specification">
              <h4>规格</h4>
              <p>{{ materialInfo.specification }}</p>
            </div>
          </div>
        </div>

        <!-- 结构图片 -->
        <div class="info-card" v-if="materialInfo.structureImage">
          <div class="card-title">
            <i class="el-icon-picture"></i>
            结构图
          </div>
          <div class="structure-image">
            <img :src="materialInfo.structureImage" alt="结构图" @error="handleImageError" />
          </div>
        </div>

        <!-- 文献信息 -->
        <div class="info-card" v-if="literatureInfo">
          <div class="card-title">
            <i class="el-icon-document-copy"></i>
            来源文献
          </div>
          <div class="literature-info">
            <h4>{{ literatureInfo.name }}</h4>
            <div class="literature-meta">
              <span v-if="literatureInfo.author">作者：{{ literatureInfo.author }}</span>
              <span v-if="literatureInfo.version">版本：{{ literatureInfo.version }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="!loading && error" class="error-state">
        <i class="el-icon-warning-outline"></i>
        <p>{{ error }}</p>
        <el-button type="primary" size="small" @click="loadMaterialDetail">重新加载</el-button>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!loading && !materialInfo.id" class="empty-state">
        <i class="el-icon-info"></i>
        <p>未找到相关信息</p>
      </div>
    </div>

    <!-- 底部操作 -->
    <div class="mobile-footer">
      <div class="footer-content">
        <el-button type="primary" size="small" @click="goToDesktop" icon="el-icon-monitor">
          桌面版查看
        </el-button>
        <el-button size="small" @click="shareInfo" icon="el-icon-share">
          分享
        </el-button>
      </div>
    </div>
  </div>
</template>

<script>
import { getMaterial } from "@/api/knowledge/material"
import { getLiterature } from "@/api/knowledge/literature"

export default {
  name: "MobileMaterialDetail",
  data() {
    return {
      loading: true,
      error: null,
      materialInfo: {},
      literatureInfo: null
    }
  },
  computed: {
    materialId() {
      return this.$route.params.id
    },
    hasDetailInfo() {
      const detailFields = [
        'trait', 'identification', 'inspection', 'assay', 'storage',
        'preparation', 'actionAndUse', 'usageAndDosage', 'adverseReaction',
        'precautions', 'withdrawalPeriod', 'specification'
      ]
      return detailFields.some(field => this.materialInfo[field])
    }
  },
  created() {
    this.loadMaterialDetail()
  },
  methods: {
    async loadMaterialDetail() {
      if (!this.materialId) {
        this.error = '无效的原辅料ID'
        this.loading = false
        return
      }

      try {
        this.loading = true
        this.error = null
        
        const response = await getMaterial(this.materialId)
        this.materialInfo = response.data || {}
        
        // 加载文献信息
        if (this.materialInfo.literatureId) {
          try {
            const literatureResponse = await getLiterature(this.materialInfo.literatureId)
            this.literatureInfo = literatureResponse.data
          } catch (error) {
            console.warn('加载文献信息失败:', error)
          }
        }
        
      } catch (error) {
        console.error('加载原辅料详情失败:', error)
        this.error = '加载失败，请稍后重试'
      } finally {
        this.loading = false
      }
    },

    getTypeTagType(type) {
      if (!type) return ''
      const hash = type.split('').reduce((a, b) => {
        a = ((a << 5) - a) + b.charCodeAt(0)
        return a & a
      }, 0)
      const colors = ['primary', 'success', 'info', 'warning', '']
      return colors[Math.abs(hash) % colors.length]
    },

    handleImageError(event) {
      event.target.style.display = 'none'
    },

    goToDesktop() {
      const desktopUrl = `${window.location.origin}/knowledge/material/detail/${this.materialId}`
      window.open(desktopUrl, '_blank')
    },

    shareInfo() {
      if (navigator.share) {
        navigator.share({
          title: this.materialInfo.name,
          text: `${this.materialInfo.name} - 原辅料信息`,
          url: window.location.href
        }).catch(console.error)
      } else {
        // 降级方案：复制链接
        navigator.clipboard.writeText(window.location.href).then(() => {
          this.$message.success('链接已复制到剪贴板')
        }).catch(() => {
          this.$message.error('分享失败')
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mobile-material-detail {
  min-height: 100vh;
  background: #f5f5f5;
  padding-bottom: 60px;
}

.mobile-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 16px;
  
  .header-content {
    .material-name {
      font-size: 20px;
      font-weight: 600;
      margin: 0 0 8px 0;
      line-height: 1.3;
    }
    
    .material-type {
      .el-tag {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
      }
    }
  }
}

.mobile-content {
  padding: 16px;
}

.info-card {
  background: white;
  border-radius: 8px;
  margin-bottom: 16px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
  .card-title {
    background: #f8f9fa;
    padding: 12px 16px;
    font-weight: 600;
    color: #303133;
    border-bottom: 1px solid #ebeef5;
    
    i {
      margin-right: 8px;
      color: #409eff;
    }
  }
}

.info-grid {
  padding: 16px;
  
  .info-item {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    label {
      display: block;
      font-size: 12px;
      color: #909399;
      margin-bottom: 4px;
      font-weight: 500;
    }
    
    .info-value {
      font-size: 14px;
      color: #303133;
      line-height: 1.4;
    }
  }
}

.detail-content {
  padding: 16px;
  
  .detail-item {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    h4 {
      font-size: 14px;
      color: #409eff;
      margin: 0 0 8px 0;
      font-weight: 600;
    }
    
    p {
      font-size: 14px;
      color: #606266;
      line-height: 1.6;
      margin: 0;
    }
  }
}

.structure-image {
  padding: 16px;
  text-align: center;
  
  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
  }
}

.literature-info {
  padding: 16px;
  
  h4 {
    font-size: 16px;
    color: #303133;
    margin: 0 0 8px 0;
  }
  
  .literature-meta {
    font-size: 12px;
    color: #909399;
    
    span {
      margin-right: 16px;
    }
  }
}

.error-state,
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
  
  i {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
  }
  
  p {
    margin: 0 0 16px 0;
    font-size: 14px;
  }
}

.mobile-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-top: 1px solid #ebeef5;
  padding: 12px 16px;
  
  .footer-content {
    display: flex;
    gap: 12px;
    
    .el-button {
      flex: 1;
    }
  }
}

// 响应式适配
@media (max-width: 480px) {
  .mobile-header {
    padding: 16px 12px;
    
    .material-name {
      font-size: 18px;
    }
  }
  
  .mobile-content {
    padding: 12px;
  }
  
  .info-card {
    margin-bottom: 12px;
  }
}
</style>
