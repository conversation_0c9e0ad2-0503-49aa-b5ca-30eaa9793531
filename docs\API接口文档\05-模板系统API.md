# 模板系统 API 接口文档

## 📋 概述

模板系统模块提供文档模板的创建、管理、使用等功能，支持模板分类、版本管理、评论评分等。

## 🔗 基础信息

- **基础URL**: `/api/v1/workspace/{workspaceId}/templates`
- **认证方式**: Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

## 📄 模板管理 API

### 1. 获取模板列表

```http
GET /workspace/{workspaceId}/templates
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | Number | 否 | 页码，默认1 |
| pageSize | Number | 否 | 每页数量，默认20 |
| category | String | 否 | 分类筛选 |
| tags | String | 否 | 标签筛选，逗号分隔 |
| keyword | String | 否 | 搜索关键词 |
| type | String | 否 | 模板类型：personal/public/system |
| sortBy | String | 否 | 排序字段：name/created/usage/rating |
| order | String | 否 | 排序方向：asc/desc |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "templates": [
      {
        "id": "template_123456",
        "name": "会议记录模板",
        "description": "标准的会议记录格式模板",
        "content": "# 会议记录\n\n## 会议信息\n...",
        "preview": "会议记录模板预览内容...",
        "type": "public",
        "category": "会议文档",
        "categoryId": "cat_123",
        "tags": ["会议", "记录", "标准"],
        "authorId": "user_123",
        "authorName": "张三",
        "authorAvatar": "avatar.jpg",
        "version": "1.2.0",
        "isOfficial": true,
        "isRecommended": true,
        "statistics": {
          "usageCount": 156,
          "favoriteCount": 23,
          "downloadCount": 89,
          "rating": 4.8,
          "ratingCount": 45
        },
        "metadata": {
          "language": "zh-CN",
          "format": "markdown",
          "difficulty": "beginner",
          "estimatedTime": "5分钟"
        },
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20,
    "categories": [
      {
        "id": "cat_123",
        "name": "会议文档",
        "count": 15
      }
    ]
  }
}
```

### 2. 获取模板详情

```http
GET /workspace/{workspaceId}/templates/{templateId}
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "template_123456",
    "name": "会议记录模板",
    "description": "标准的会议记录格式模板，包含会议基本信息、议程、决议等标准结构",
    "content": "# 会议记录\n\n## 会议信息\n- **会议主题**: \n- **会议时间**: \n- **会议地点**: \n...",
    "type": "public",
    "category": "会议文档",
    "tags": ["会议", "记录", "标准"],
    "authorId": "user_123",
    "authorName": "张三",
    "authorAvatar": "avatar.jpg",
    "version": "1.2.0",
    "changelog": "优化了议程结构，增加了行动项跟踪",
    "permissions": {
      "canEdit": false,
      "canDelete": false,
      "canUse": true,
      "canComment": true
    },
    "statistics": {
      "usageCount": 156,
      "favoriteCount": 23,
      "downloadCount": 89,
      "rating": 4.8,
      "ratingCount": 45,
      "viewCount": 1250
    },
    "relatedTemplates": [
      {
        "id": "template_789",
        "name": "项目会议模板",
        "rating": 4.6
      }
    ],
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### 3. 创建模板

```http
POST /workspace/{workspaceId}/templates
```

**请求体**

```json
{
  "name": "新模板名称",
  "description": "模板描述",
  "content": "模板内容...",
  "type": "personal",
  "categoryId": "cat_123",
  "tags": ["标签1", "标签2"],
  "isPublic": false,
  "metadata": {
    "language": "zh-CN",
    "format": "markdown",
    "difficulty": "intermediate"
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "模板创建成功",
  "data": {
    "id": "template_789012",
    "name": "新模板名称",
    "authorId": "user_123",
    "version": "1.0.0",
    "createdAt": "2024-01-20T16:00:00Z"
  }
}
```

### 4. 更新模板

```http
PUT /workspace/{workspaceId}/templates/{templateId}
```

**请求体**

```json
{
  "name": "更新后的模板名称",
  "description": "更新后的描述",
  "content": "更新后的内容...",
  "tags": ["新标签"],
  "changelog": "更新说明"
}
```

### 5. 删除模板

```http
DELETE /workspace/{workspaceId}/templates/{templateId}
```

### 6. 使用模板创建文档

```http
POST /workspace/{workspaceId}/templates/{templateId}/create-document
```

**请求体**

```json
{
  "title": "基于模板创建的文档",
  "folderId": "folder_123",
  "variables": {
    "projectName": "新项目",
    "date": "2024-01-20",
    "author": "张三"
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "文档创建成功",
  "data": {
    "documentId": "doc_789012",
    "title": "基于模板创建的文档",
    "templateId": "template_123456",
    "createdAt": "2024-01-20T16:00:00Z",
    "url": "/workspace/ws_123/documents/doc_789012"
  }
}
```

## 📂 模板分类 API

### 1. 获取分类列表

```http
GET /workspace/{workspaceId}/templates/categories
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "categories": [
      {
        "id": "cat_123",
        "name": "会议文档",
        "description": "各类会议相关的文档模板",
        "icon": "meeting",
        "color": "#409eff",
        "parentId": null,
        "level": 1,
        "templateCount": 15,
        "isSystem": true,
        "sortOrder": 1,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

### 2. 创建分类

```http
POST /workspace/{workspaceId}/templates/categories
```

**请求体**

```json
{
  "name": "新分类",
  "description": "分类描述",
  "icon": "folder",
  "color": "#67c23a",
  "parentId": "cat_123"
}
```

### 3. 更新分类

```http
PUT /workspace/{workspaceId}/templates/categories/{categoryId}
```

### 4. 删除分类

```http
DELETE /workspace/{workspaceId}/templates/categories/{categoryId}
```

## 🏷️ 模板标签 API

### 1. 获取标签列表

```http
GET /workspace/{workspaceId}/templates/tags
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "tags": [
      {
        "id": "tag_123",
        "name": "会议",
        "color": "#409eff",
        "usageCount": 25,
        "isSystem": true,
        "createdAt": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

### 2. 创建标签

```http
POST /workspace/{workspaceId}/templates/tags
```

**请求体**

```json
{
  "name": "新标签",
  "color": "#f56c6c",
  "description": "标签描述"
}
```

## 📊 模板版本 API

### 1. 获取模板版本列表

```http
GET /workspace/{workspaceId}/templates/{templateId}/versions
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "versions": [
      {
        "id": "version_123",
        "version": "1.2.0",
        "title": "优化版本",
        "changelog": "优化了模板结构，增加了新的字段",
        "content": "模板内容...",
        "authorId": "user_123",
        "authorName": "张三",
        "isCurrent": true,
        "downloadCount": 45,
        "createdAt": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

### 2. 创建模板版本

```http
POST /workspace/{workspaceId}/templates/{templateId}/versions
```

**请求体**

```json
{
  "version": "1.3.0",
  "title": "新版本",
  "changelog": "版本更新说明",
  "content": "新版本的模板内容..."
}
```

## 💬 模板评论 API

### 1. 获取模板评论

```http
GET /workspace/{workspaceId}/templates/{templateId}/comments
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | Number | 否 | 页码，默认1 |
| pageSize | Number | 否 | 每页数量，默认10 |
| sortBy | String | 否 | 排序：latest/oldest/rating |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "comments": [
      {
        "id": "comment_123",
        "content": "这个模板很实用，结构清晰",
        "rating": 5,
        "authorId": "user_456",
        "authorName": "李四",
        "authorAvatar": "avatar2.jpg",
        "replies": [
          {
            "id": "reply_123",
            "content": "同意，我也在用",
            "authorId": "user_789",
            "authorName": "王五",
            "createdAt": "2024-01-20T16:00:00Z"
          }
        ],
        "likes": 8,
        "isLiked": false,
        "createdAt": "2024-01-20T15:30:00Z"
      }
    ],
    "total": 25,
    "averageRating": 4.6
  }
}
```

### 2. 添加评论

```http
POST /workspace/{workspaceId}/templates/{templateId}/comments
```

**请求体**

```json
{
  "content": "评论内容",
  "rating": 5
}
```

### 3. 回复评论

```http
POST /workspace/{workspaceId}/templates/{templateId}/comments/{commentId}/replies
```

**请求体**

```json
{
  "content": "回复内容"
}
```

## ⭐ 模板收藏 API

### 1. 收藏模板

```http
POST /workspace/{workspaceId}/templates/{templateId}/favorite
```

### 2. 取消收藏

```http
DELETE /workspace/{workspaceId}/templates/{templateId}/favorite
```

### 3. 获取收藏列表

```http
GET /workspace/{workspaceId}/templates/favorites
```

## 📈 模板统计 API

### 1. 获取模板统计

```http
GET /workspace/{workspaceId}/templates/{templateId}/statistics
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "overview": {
      "usageCount": 156,
      "favoriteCount": 23,
      "downloadCount": 89,
      "viewCount": 1250,
      "commentCount": 25,
      "rating": 4.8,
      "ratingCount": 45
    },
    "usage": {
      "thisWeek": 15,
      "thisMonth": 45,
      "growth": 12.5
    },
    "timeline": [
      {
        "date": "2024-01-20",
        "usage": 8,
        "views": 25
      }
    ]
  }
}
```

## 🔍 模板搜索 API

### 1. 搜索模板

```http
GET /workspace/{workspaceId}/templates/search
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| q | String | 是 | 搜索关键词 |
| category | String | 否 | 分类筛选 |
| tags | String | 否 | 标签筛选 |
| type | String | 否 | 类型筛选 |
| rating | Number | 否 | 最低评分 |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "results": [
      {
        "id": "template_123456",
        "name": "会议记录模板",
        "description": "标准的会议记录格式模板",
        "snippet": "...包含搜索关键词的片段...",
        "rating": 4.8,
        "usageCount": 156,
        "relevanceScore": 0.95
      }
    ],
    "total": 15,
    "searchTime": 0.05
  }
}
```

## 🚨 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 模板不存在 |
| 409 | 模板名称冲突 |
| 413 | 模板内容过大 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 📝 使用示例

### JavaScript/Axios

```javascript
// 获取模板列表
const getTemplates = async (workspaceId, query = {}) => {
  try {
    const response = await axios.get(`/api/v1/workspace/${workspaceId}/templates`, {
      params: query,
      headers: { 'Authorization': `Bearer ${token}` }
    })
    return response.data
  } catch (error) {
    console.error('获取模板列表失败:', error)
    throw error
  }
}

// 使用模板创建文档
const createDocumentFromTemplate = async (workspaceId, templateId, documentData) => {
  try {
    const response = await axios.post(
      `/api/v1/workspace/${workspaceId}/templates/${templateId}/create-document`,
      documentData,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      }
    )
    return response.data
  } catch (error) {
    console.error('创建文档失败:', error)
    throw error
  }
}
```
