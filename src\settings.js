module.exports = {
  /**
   * 网页标题
   */
  title: process.env.VUE_APP_TITLE,

  /**
   * 侧边栏主题 深色主题theme-dark，浅色主题theme-light
   */
  sideTheme: 'theme-light',

  /**
   * 系统布局配置
   */
  showSettings: false,

  /**
   * 是否显示顶部导航
   */
  topNav: false,

  /**
   * 是否显示 tagsView
   */
  tagsView: true,

  /**
   * 显示页签图标
   */
  tagsIcon: false,

  /**
   * 是否固定头部
   */
  fixedHeader: true,

  /**
   * 是否显示logo
   */
  sidebarLogo: true,

  /**
   * 是否显示动态标题
   */
  dynamicTitle: false,

  /**
   * 是否显示底部版权
   */
  footerVisible: false,

  /**
   * 底部版权文本内容
   */
  footerContent: 'Copyright © 2018-2025 CPBIO. All Rights Reserved.',

  /**
   * 二维码配置
   */
  qrcode: {
    /**
     * 二维码访问域名，用于生成二维码链接
     * 可以是域名或IP地址，如：'https://example.com' 或 'http://*************:8090'
     * 如果为空，则使用当前页面的域名
     */
    baseUrl: process.env.VUE_APP_QRCODE_BASE_URL || '',

    /**
     * 二维码尺寸配置
     */
    size: {
      small: 40,    // 表格中显示的小尺寸
      large: 250,   // 预览对话框中的大尺寸
      print: 250    // 打印时的尺寸
    },

    /**
     * 二维码样式配置
     */
    style: {
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    }
  }
}
