# 文档管理 API 接口文档

## 📋 概述

文档管理模块提供文档的创建、编辑、删除、查询等核心功能，支持文件夹管理、文档分享、权限控制等。

## 🔗 基础信息

- **基础URL**: `/api/v1/workspace/{workspaceId}`
- **认证方式**: Bearer <PERSON>ken
- **数据格式**: JSON
- **字符编码**: UTF-8

## 📄 文档基础 API

### 1. 获取文档列表

```http
GET /workspace/{workspaceId}/documents
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| folderId | String | 否 | 文件夹ID，不传则获取根目录 |
| page | Number | 否 | 页码，默认1 |
| pageSize | Number | 否 | 每页数量，默认20 |
| keyword | String | 否 | 搜索关键词 |
| type | String | 否 | 文档类型：markdown/text/rich |
| sortBy | String | 否 | 排序字段：name/modified/size/created |
| order | String | 否 | 排序方向：asc/desc |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "doc_123456",
        "title": "产品需求文档",
        "content": "文档内容...",
        "type": "markdown",
        "folderId": "folder_123",
        "folderPath": "/产品文档/需求分析",
        "authorId": "user_123",
        "authorName": "张三",
        "authorAvatar": "avatar.jpg",
        "size": 2048,
        "wordCount": 1500,
        "status": "published",
        "isPublic": false,
        "tags": ["需求", "产品"],
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-20T15:30:00Z",
        "lastEditedBy": "user_456",
        "lastEditedByName": "李四",
        "viewCount": 25,
        "commentCount": 8,
        "favoriteCount": 3
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20
  }
}
```

### 2. 获取文档详情

```http
GET /workspace/{workspaceId}/documents/{documentId}
```

**路径参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| workspaceId | String | 是 | 工作空间ID |
| documentId | String | 是 | 文档ID |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "doc_123456",
    "title": "产品需求文档",
    "content": "# 产品需求文档\n\n## 概述\n...",
    "type": "markdown",
    "folderId": "folder_123",
    "folderPath": "/产品文档/需求分析",
    "authorId": "user_123",
    "authorName": "张三",
    "authorAvatar": "avatar.jpg",
    "size": 2048,
    "wordCount": 1500,
    "status": "published",
    "isPublic": false,
    "tags": ["需求", "产品"],
    "metadata": {
      "template": "requirement_template",
      "priority": "high",
      "deadline": "2024-02-01"
    },
    "permissions": {
      "canRead": true,
      "canWrite": true,
      "canDelete": false,
      "canShare": true
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-20T15:30:00Z",
    "lastEditedBy": "user_456",
    "lastEditedByName": "李四"
  }
}
```

### 3. 创建文档

```http
POST /workspace/{workspaceId}/documents
```

**请求体**

```json
{
  "title": "新文档标题",
  "content": "文档内容",
  "type": "markdown",
  "folderId": "folder_123",
  "templateId": "template_456",
  "tags": ["标签1", "标签2"],
  "isPublic": false,
  "metadata": {
    "priority": "medium",
    "category": "需求文档"
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "文档创建成功",
  "data": {
    "id": "doc_789012",
    "title": "新文档标题",
    "authorId": "user_123",
    "createdAt": "2024-01-20T16:00:00Z",
    "url": "/workspace/ws_123/documents/doc_789012"
  }
}
```

### 4. 更新文档

```http
PUT /workspace/{workspaceId}/documents/{documentId}
```

**请求体**

```json
{
  "title": "更新后的标题",
  "content": "更新后的内容",
  "tags": ["新标签"],
  "metadata": {
    "priority": "high"
  }
}
```

### 5. 删除文档

```http
DELETE /workspace/{workspaceId}/documents/{documentId}
```

**响应示例**

```json
{
  "code": 200,
  "message": "文档删除成功"
}
```

### 6. 批量操作文档

```http
POST /workspace/{workspaceId}/documents/batch
```

**请求体**

```json
{
  "operation": "delete",
  "documentIds": ["doc_123", "doc_456", "doc_789"],
  "targetFolderId": "folder_456"
}
```

## 📁 文件夹管理 API

### 1. 获取文件夹列表

```http
GET /workspace/{workspaceId}/folders
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| parentId | String | 否 | 父文件夹ID |
| includeDocuments | Boolean | 否 | 是否包含文档，默认false |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "folders": [
      {
        "id": "folder_123",
        "name": "产品文档",
        "parentId": null,
        "path": "/产品文档",
        "description": "产品相关文档",
        "documentCount": 15,
        "subfolderCount": 3,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:00:00Z"
      }
    ],
    "documents": []
  }
}
```

### 2. 创建文件夹

```http
POST /workspace/{workspaceId}/folders
```

**请求体**

```json
{
  "name": "新文件夹",
  "parentId": "folder_123",
  "description": "文件夹描述"
}
```

### 3. 更新文件夹

```http
PUT /workspace/{workspaceId}/folders/{folderId}
```

### 4. 删除文件夹

```http
DELETE /workspace/{workspaceId}/folders/{folderId}
```

## 🔗 文档分享 API

### 1. 创建分享链接

```http
POST /workspace/{workspaceId}/documents/{documentId}/share
```

**请求体**

```json
{
  "expireTime": "2024-02-01T00:00:00Z",
  "password": "123456",
  "allowDownload": true,
  "allowComment": false
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "分享链接创建成功",
  "data": {
    "shareId": "share_123456",
    "shareUrl": "https://example.com/share/share_123456",
    "password": "123456",
    "expireTime": "2024-02-01T00:00:00Z",
    "createdAt": "2024-01-20T16:00:00Z"
  }
}
```

### 2. 获取分享信息

```http
GET /workspace/{workspaceId}/documents/{documentId}/share
```

### 3. 取消分享

```http
DELETE /workspace/{workspaceId}/documents/{documentId}/share
```

## 🔒 权限管理 API

### 1. 获取文档权限

```http
GET /workspace/{workspaceId}/documents/{documentId}/permissions
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "documentId": "doc_123456",
    "isPublic": false,
    "defaultPermission": "read",
    "permissions": [
      {
        "userId": "user_123",
        "userName": "张三",
        "permission": "owner",
        "grantedAt": "2024-01-01T00:00:00Z",
        "grantedBy": "user_123"
      },
      {
        "userId": "user_456",
        "userName": "李四",
        "permission": "write",
        "grantedAt": "2024-01-10T10:00:00Z",
        "grantedBy": "user_123"
      }
    ]
  }
}
```

### 2. 设置文档权限

```http
POST /workspace/{workspaceId}/documents/{documentId}/permissions
```

**请求体**

```json
{
  "userId": "user_789",
  "permission": "read"
}
```

### 3. 移除文档权限

```http
DELETE /workspace/{workspaceId}/documents/{documentId}/permissions/{userId}
```

## 📊 文档统计 API

### 1. 获取文档统计

```http
GET /workspace/{workspaceId}/documents/{documentId}/statistics
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "documentId": "doc_123456",
    "viewCount": 125,
    "editCount": 23,
    "commentCount": 8,
    "favoriteCount": 5,
    "shareCount": 3,
    "downloadCount": 12,
    "lastViewedAt": "2024-01-20T15:30:00Z",
    "lastEditedAt": "2024-01-20T14:20:00Z",
    "dailyViews": [
      {"date": "2024-01-20", "views": 15},
      {"date": "2024-01-19", "views": 12}
    ]
  }
}
```

## 🔍 搜索 API

### 1. 搜索文档

```http
GET /workspace/{workspaceId}/documents/search
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| q | String | 是 | 搜索关键词 |
| type | String | 否 | 文档类型筛选 |
| author | String | 否 | 作者筛选 |
| tags | String | 否 | 标签筛选，逗号分隔 |
| dateFrom | String | 否 | 开始日期 |
| dateTo | String | 否 | 结束日期 |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "results": [
      {
        "id": "doc_123456",
        "title": "产品需求文档",
        "snippet": "...包含搜索关键词的片段...",
        "authorName": "张三",
        "updatedAt": "2024-01-20T15:30:00Z",
        "relevanceScore": 0.95
      }
    ],
    "total": 15,
    "searchTime": 0.05
  }
}
```

## 🚨 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 文档不存在 |
| 409 | 文档标题冲突 |
| 413 | 文档内容过大 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |
