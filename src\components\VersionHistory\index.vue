<template>
  <div class="version-history">
    <!-- 版本历史按钮 -->
    <el-button v-if="!showHistory" type="text" icon="el-icon-time" @click="toggleHistory" class="history-toggle">
      版本历史
    </el-button>

    <!-- 版本历史面板 -->
    <div v-if="showHistory" class="history-panel">
      <div class="history-header">
        <h4>版本历史</h4>
        <div class="header-actions">
          <el-button type="text" size="mini" @click="createVersion">
            <i class="el-icon-plus"></i> 新建版本
          </el-button>
          <el-button type="text" icon="el-icon-close" @click="toggleHistory"></el-button>
        </div>
      </div>

      <!-- 版本统计 -->
      <div class="version-stats" v-if="documentStats">
        <div class="stat-item">
          <span class="stat-label">总版本数:</span>
          <span class="stat-value">{{ documentStats.totalVersions }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">当前版本:</span>
          <span class="stat-value">v{{ documentStats.currentVersion }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">协作者:</span>
          <span class="stat-value">{{ documentStats.authors }}</span>
        </div>
      </div>

      <!-- 版本列表 -->
      <div class="version-list">
        <div v-for="version in versions" :key="version.id" class="version-item" :class="{
          'current': version.id === currentVersionId,
          'selected': selectedVersions.includes(version.id)
        }" @click="selectVersion(version)">
          <!-- 版本信息 -->
          <div class="version-info">
            <div class="version-header">
              <span class="version-number">v{{ version.number }}</span>
              <span class="version-title">{{ version.title }}</span>
              <el-tag v-if="version.id === currentVersionId" size="mini" type="success">
                当前
              </el-tag>
              <el-tag v-if="version.metadata.isRollback" size="mini" type="warning">
                回滚
              </el-tag>
            </div>

            <div class="version-meta">
              <div class="author-info">
                <el-avatar :src="version.author.avatar" :size="20">
                  {{ version.author.name ? version.author.name.charAt(0) : 'U' }}
                </el-avatar>
                <span class="author-name">{{ version.author.name }}</span>
              </div>
              <span class="version-time">{{ formatTime(version.createdAt) }}</span>
            </div>

            <div v-if="version.message" class="version-message">
              {{ version.message }}
            </div>

            <!-- 变更统计 -->
            <div v-if="version.changes.length > 0" class="change-stats">
              <span class="change-item add">+{{ getChangeCount(version.changes, 'add') }}</span>
              <span class="change-item delete">-{{ getChangeCount(version.changes, 'delete') }}</span>
              <span class="change-item modify">~{{ getChangeCount(version.changes, 'modify') }}</span>
            </div>
          </div>

          <!-- 版本操作 -->
          <div class="version-actions">
            <el-dropdown @command="(command) => handleVersionAction(version, command)">
              <el-button type="text" size="mini" icon="el-icon-more"></el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="preview">预览</el-dropdown-item>
                <el-dropdown-item command="restore" :disabled="version.id === currentVersionId">
                  恢复到此版本
                </el-dropdown-item>
                <el-dropdown-item command="compare" :disabled="selectedVersions.length === 0">
                  与选中版本对比
                </el-dropdown-item>
                <el-dropdown-item command="tag">添加标签</el-dropdown-item>
                <el-dropdown-item command="branch">创建分支</el-dropdown-item>
                <el-dropdown-item command="export">导出</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="versions.length === 0" class="empty-versions">
          <i class="el-icon-time"></i>
          <p>还没有版本历史</p>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalVersions > pageSize" class="version-pagination">
        <el-pagination @current-change="handlePageChange" :current-page="currentPage" :page-size="pageSize"
          :total="totalVersions" layout="prev, pager, next" small />
      </div>
    </div>

    <!-- 版本预览对话框 -->
    <el-dialog title="版本预览" :visible.sync="showPreview" width="80%" :before-close="closePreview">
      <div class="version-preview">
        <div class="preview-header">
          <h4>{{ previewVersion ? previewVersion.title : '' }}</h4>
          <div class="preview-meta">
            <span>版本 {{ previewVersion ? previewVersion.number : '' }}</span>
            <span>{{ previewVersion ? formatTime(previewVersion.createdAt) : '' }}</span>
            <span>{{ previewVersion ? previewVersion.author.name : '' }}</span>
          </div>
        </div>
        <div class="preview-content" v-html="previewContent"></div>
      </div>
    </el-dialog>

    <!-- 版本对比对话框 -->
    <el-dialog title="版本对比" :visible.sync="showComparison" width="90%" :before-close="closeComparison">
      <div class="version-comparison">
        <div class="comparison-header">
          <div class="version-info">
            <h5>版本 {{ comparisonData.version1 ? comparisonData.version1.number : '' }}</h5>
            <span>{{ comparisonData.version1 ? formatTime(comparisonData.version1.createdAt) : '' }}</span>
          </div>
          <div class="comparison-arrow">
            <i class="el-icon-right"></i>
          </div>
          <div class="version-info">
            <h5>版本 {{ comparisonData.version2 ? comparisonData.version2.number : '' }}</h5>
            <span>{{ comparisonData.version2 ? formatTime(comparisonData.version2.createdAt) : '' }}</span>
          </div>
        </div>

        <!-- 变更摘要 -->
        <div class="change-summary">
          <el-alert :title="`共 ${comparisonData.summary ? comparisonData.summary.totalChanges : 0} 处变更`" type="info"
            :closable="false">
            <div slot="default">
              <span class="summary-item">新增: {{ comparisonData.summary ? comparisonData.summary.additions : 0 }}
                行</span>
              <span class="summary-item">删除: {{ comparisonData.summary ? comparisonData.summary.deletions : 0 }}
                行</span>
              <span class="summary-item">修改: {{ comparisonData.summary ? comparisonData.summary.modifications : 0 }}
                行</span>
            </div>
          </el-alert>
        </div>

        <!-- 差异显示 -->
        <div class="diff-view">
          <div v-for="(change, index) in comparisonData.changes" :key="index" class="diff-line" :class="change.type">
            <span class="line-number">
              {{ change.oldLineNumber || '' }}
            </span>
            <span class="line-number">
              {{ change.lineNumber || '' }}
            </span>
            <span class="line-content">
              <span v-if="change.type === 'modify'" class="old-content">{{ change.oldContent }}</span>
              {{ change.content }}
            </span>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 创建版本对话框 -->
    <el-dialog title="创建新版本" :visible.sync="showCreateVersion" width="500px">
      <el-form :model="newVersionForm" label-width="80px">
        <el-form-item label="版本标题">
          <el-input v-model="newVersionForm.title" placeholder="请输入版本标题"></el-input>
        </el-form-item>
        <el-form-item label="版本说明">
          <el-input v-model="newVersionForm.message" type="textarea" :rows="3" placeholder="请输入版本说明"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showCreateVersion = false">取消</el-button>
        <el-button type="primary" @click="submitCreateVersion">创建</el-button>
      </div>
    </el-dialog>

    <!-- 添加标签对话框 -->
    <el-dialog title="添加标签" :visible.sync="showCreateTag" width="400px">
      <el-form :model="newTagForm" label-width="80px">
        <el-form-item label="标签名称">
          <el-input v-model="newTagForm.name" placeholder="请输入标签名称"></el-input>
        </el-form-item>
        <el-form-item label="标签描述">
          <el-input v-model="newTagForm.description" type="textarea" :rows="2" placeholder="请输入标签描述"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer">
        <el-button @click="showCreateTag = false">取消</el-button>
        <el-button type="primary" @click="submitCreateTag">创建</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { marked } from 'marked'
import versionControlService from '@/utils/versionControl'

export default {
  name: 'VersionHistory',
  props: {
    documentId: {
      type: String,
      required: true
    },
    currentContent: {
      type: String,
      default: ''
    },
    currentUser: {
      type: Object,
      required: true
    }
  },
  data () {
    return {
      showHistory: false,
      versions: [],
      currentVersionId: null,
      selectedVersions: [],
      documentStats: null,

      // 分页
      currentPage: 1,
      pageSize: 10,
      totalVersions: 0,

      // 预览
      showPreview: false,
      previewVersion: null,
      previewContent: '',

      // 对比
      showComparison: false,
      comparisonData: {
        version1: null,
        version2: null,
        changes: [],
        summary: null
      },

      // 创建版本
      showCreateVersion: false,
      newVersionForm: {
        title: '',
        message: ''
      },

      // 创建标签
      showCreateTag: false,
      newTagForm: {
        name: '',
        description: ''
      },
      selectedVersionForTag: null
    }
  },
  mounted () {
    this.initVersionControl()
  },
  methods: {
    // 初始化版本控制
    initVersionControl () {
      versionControlService.initDocument(this.documentId, this.currentContent, this.currentUser)
      this.loadVersions()
      this.loadDocumentStats()
    },

    // 切换历史面板显示
    toggleHistory () {
      this.showHistory = !this.showHistory
      if (this.showHistory) {
        this.loadVersions()
      }
    },

    // 加载版本列表
    loadVersions () {
      const options = {
        page: this.currentPage,
        pageSize: this.pageSize,
        sortBy: 'date'
      }

      this.versions = versionControlService.getVersions(this.documentId, options)
      this.currentVersionId = versionControlService.getCurrentVersion(this.documentId)?.id
      this.totalVersions = versionControlService.getVersions(this.documentId).length
    },

    // 加载文档统计
    loadDocumentStats () {
      this.documentStats = versionControlService.getDocumentStats(this.documentId)
    },

    // 选择版本
    selectVersion (version) {
      const index = this.selectedVersions.indexOf(version.id)
      if (index > -1) {
        this.selectedVersions.splice(index, 1)
      } else {
        this.selectedVersions.push(version.id)
      }
    },

    // 处理版本操作
    handleVersionAction (version, action) {
      switch (action) {
        case 'preview':
          this.previewVersion(version)
          break
        case 'restore':
          this.restoreVersion(version)
          break
        case 'compare':
          this.compareVersions(version)
          break
        case 'tag':
          this.showTagDialog(version)
          break
        case 'branch':
          this.createBranch(version)
          break
        case 'export':
          this.exportVersion(version)
          break
      }
    },

    // 预览版本
    previewVersion (version) {
      this.previewVersion = version
      this.previewContent = marked(version.content)
      this.showPreview = true
    },

    // 关闭预览
    closePreview () {
      this.showPreview = false
      this.previewVersion = null
      this.previewContent = ''
    },

    // 恢复版本
    restoreVersion (version) {
      this.$confirm(`确定要恢复到版本 ${version.number} 吗？`, '确认恢复', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        try {
          const newVersion = versionControlService.rollbackToVersion(
            this.documentId,
            version.id,
            this.currentUser
          )

          this.$emit('version-restored', newVersion)
          this.loadVersions()
          this.loadDocumentStats()
          this.$message.success('版本恢复成功')
        } catch (error) {
          this.$message.error('版本恢复失败: ' + error.message)
        }
      })
    },

    // 对比版本
    compareVersions (version) {
      if (this.selectedVersions.length === 0) {
        this.$message.warning('请先选择要对比的版本')
        return
      }

      const compareVersionId = this.selectedVersions[0]
      try {
        this.comparisonData = versionControlService.compareVersions(
          this.documentId,
          compareVersionId,
          version.id
        )
        this.showComparison = true
      } catch (error) {
        this.$message.error('版本对比失败: ' + error.message)
      }
    },

    // 关闭对比
    closeComparison () {
      this.showComparison = false
      this.comparisonData = {
        version1: null,
        version2: null,
        changes: [],
        summary: null
      }
    },

    // 显示标签对话框
    showTagDialog (version) {
      this.selectedVersionForTag = version
      this.newTagForm = { name: '', description: '' }
      this.showCreateTag = true
    },

    // 创建标签
    submitCreateTag () {
      if (!this.newTagForm.name.trim()) {
        this.$message.warning('请输入标签名称')
        return
      }

      try {
        versionControlService.createTag(
          this.documentId,
          this.newTagForm.name,
          this.selectedVersionForTag.id,
          this.newTagForm.description
        )

        this.showCreateTag = false
        this.$message.success('标签创建成功')
      } catch (error) {
        this.$message.error('标签创建失败: ' + error.message)
      }
    },

    // 创建分支
    createBranch (version) {
      this.$prompt('请输入分支名称', '创建分支', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        try {
          versionControlService.createBranch(this.documentId, value, version.id)
          this.$message.success('分支创建成功')
        } catch (error) {
          this.$message.error('分支创建失败: ' + error.message)
        }
      })
    },

    // 导出版本
    exportVersion (version) {
      const data = {
        version,
        content: version.content,
        exportedAt: new Date().toISOString()
      }

      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `version_${version.number}_${this.documentId}.json`
      a.click()
      URL.revokeObjectURL(url)

      this.$message.success('版本导出成功')
    },

    // 创建版本
    createVersion () {
      this.newVersionForm = { title: '', message: '' }
      this.showCreateVersion = true
    },

    // 提交创建版本
    submitCreateVersion () {
      if (!this.newVersionForm.title.trim()) {
        this.$message.warning('请输入版本标题')
        return
      }

      try {
        const newVersion = versionControlService.createVersion(this.documentId, {
          content: this.currentContent,
          title: this.newVersionForm.title,
          message: this.newVersionForm.message,
          author: this.currentUser
        })

        this.showCreateVersion = false
        this.loadVersions()
        this.loadDocumentStats()
        this.$emit('version-created', newVersion)
        this.$message.success('版本创建成功')
      } catch (error) {
        this.$message.error('版本创建失败: ' + error.message)
      }
    },

    // 处理分页变化
    handlePageChange (page) {
      this.currentPage = page
      this.loadVersions()
    },

    // 获取变更数量
    getChangeCount (changes, type) {
      return changes.filter(change => change.type === type).length
    },

    // 格式化时间
    formatTime (timeString) {
      const time = new Date(timeString)
      const now = new Date()
      const diff = now - time

      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`
      return time.toLocaleDateString()
    }
  }
}
</script>

<style lang="scss" scoped>
.version-history {
  position: relative;
}

.history-toggle {
  margin-left: 8px;
}

.history-panel {
  position: fixed;
  right: 20px;
  top: 20px;
  width: 400px;
  max-height: 80vh;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 2000;
  display: flex;
  flex-direction: column;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;

  h4 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.version-stats {
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;

  .stat-label {
    font-size: 12px;
    color: #909399;
  }

  .stat-value {
    font-size: 14px;
    font-weight: 500;
    color: #303133;
  }
}

.version-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
  max-height: 400px;
}

.version-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px;
  margin-bottom: 8px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;

  &:hover {
    border-color: #c6e2ff;
    background: #f0f9ff;
  }

  &.current {
    border-color: #409eff;
    background: #ecf5ff;
  }

  &.selected {
    border-color: #67c23a;
    background: #f0f9ff;
  }
}

.version-info {
  flex: 1;
}

.version-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;

  .version-number {
    font-weight: 500;
    color: #409eff;
  }

  .version-title {
    font-size: 14px;
    color: #303133;
  }
}

.version-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 4px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 6px;

  .author-name {
    font-size: 12px;
    color: #606266;
  }
}

.version-time {
  font-size: 12px;
  color: #909399;
}

.version-message {
  font-size: 12px;
  color: #606266;
  margin-bottom: 4px;
}

.change-stats {
  display: flex;
  gap: 8px;
}

.change-item {
  font-size: 11px;
  padding: 2px 4px;
  border-radius: 2px;

  &.add {
    background: #e1f3d8;
    color: #67c23a;
  }

  &.delete {
    background: #fde2e2;
    color: #f56c6c;
  }

  &.modify {
    background: #fdf6ec;
    color: #e6a23c;
  }
}

.empty-versions {
  text-align: center;
  color: #909399;
  padding: 40px 20px;

  i {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
  }
}

.version-pagination {
  padding: 12px 16px;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

/* 对话框样式 */
.version-preview {
  .preview-header {
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid #ebeef5;

    h4 {
      margin: 0 0 8px 0;
    }

    .preview-meta {
      display: flex;
      gap: 16px;
      font-size: 12px;
      color: #909399;
    }
  }

  .preview-content {
    max-height: 400px;
    overflow-y: auto;
    padding: 16px;
    background: #fafafa;
    border-radius: 4px;
  }
}

.version-comparison {
  .comparison-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .version-info {
      text-align: center;

      h5 {
        margin: 0 0 4px 0;
      }

      span {
        font-size: 12px;
        color: #909399;
      }
    }

    .comparison-arrow {
      font-size: 20px;
      color: #409eff;
    }
  }

  .change-summary {
    margin-bottom: 16px;

    .summary-item {
      margin-right: 16px;
      font-size: 12px;
    }
  }

  .diff-view {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .diff-line {
    display: flex;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    line-height: 1.5;

    &.add {
      background: #e1f3d8;
    }

    &.delete {
      background: #fde2e2;
    }

    &.modify {
      background: #fdf6ec;
    }

    .line-number {
      width: 40px;
      padding: 2px 8px;
      background: #f5f7fa;
      border-right: 1px solid #ebeef5;
      text-align: right;
      color: #909399;
    }

    .line-content {
      flex: 1;
      padding: 2px 8px;

      .old-content {
        text-decoration: line-through;
        color: #f56c6c;
        margin-right: 8px;
      }
    }
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .history-panel {
    right: 10px;
    left: 10px;
    width: auto;
  }
}
</style>
