<template>
  <div class="ai-chat-widget">
    <!-- 浮动按钮 -->
    <div class="ai-float-button" @click="toggleWidget" v-if="!isExpanded">
      <i class="el-icon-cpu"></i>
      <div class="pulse-ring"></div>
    </div>

    <!-- 展开的聊天窗口 -->
    <div class="ai-chat-panel" v-if="isExpanded">
      <!-- 头部 -->
      <div class="chat-panel-header">
        <div class="header-left">
          <i class="el-icon-cpu"></i>
          <span>AI助手</span>
        </div>
        <div class="header-right">
          <el-button type="text" icon="el-icon-full-screen" @click="openFullScreen" title="全屏模式" />
          <el-button type="text" icon="el-icon-minus" @click="toggleWidget" title="最小化" />
        </div>
      </div>

      <!-- 聊天内容 -->
      <div class="chat-panel-content" ref="chatContent">
        <!-- 欢迎消息 -->
        <div v-if="messages.length === 0" class="welcome-message">
          <div class="ai-avatar">
            <i class="el-icon-cpu"></i>
          </div>
          <div class="welcome-text">
            <h4>您好！我是AI助手</h4>
            <p>我可以帮您分析文档内容和原辅料数据</p>
          </div>
          <div class="quick-actions">
            <div class="action-item" @click="askQuestion('帮我分析当前文档的要点')">
              <i class="el-icon-document"></i>
              <span>分析文档</span>
            </div>
            <div class="action-item" @click="askQuestion('查询原辅料信息')">
              <i class="el-icon-s-grid"></i>
              <span>查询数据</span>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div v-else class="message-list">
          <div 
            v-for="message in messages" 
            :key="message.id"
            class="message-item"
            :class="{ 'user-message': message.role === 'user', 'ai-message': message.role === 'assistant' }"
          >
            <div class="message-avatar">
              <el-avatar v-if="message.role === 'user'" :size="24" icon="el-icon-user" />
              <div v-else class="ai-avatar-small">
                <i class="el-icon-cpu"></i>
              </div>
            </div>
            <div class="message-content">
              <div class="message-text" v-html="formatMessage(message.content)"></div>
              <div class="message-time">{{ formatTime(message.timestamp) }}</div>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoading" class="message-item ai-message">
            <div class="message-avatar">
              <div class="ai-avatar-small">
                <i class="el-icon-cpu"></i>
              </div>
            </div>
            <div class="message-content">
              <div class="typing-indicator">
                <span></span>
                <span></span>
                <span></span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="chat-panel-input">
        <el-input
          v-model="inputMessage"
          placeholder="输入您的问题..."
          @keyup.enter="sendMessage"
          :disabled="isLoading"
        >
          <el-button 
            slot="append" 
            icon="el-icon-s-promotion" 
            :loading="isLoading"
            @click="sendMessage"
            :disabled="!inputMessage.trim()"
          />
        </el-input>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AiChatWidget',
  props: {
    // 当前页面上下文，用于智能问答
    context: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isExpanded: false,
      messages: [],
      inputMessage: '',
      isLoading: false
    }
  },
  methods: {
    // 切换窗口显示状态
    toggleWidget() {
      this.isExpanded = !this.isExpanded
      if (this.isExpanded) {
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 打开全屏模式
    openFullScreen() {
      this.$router.push('/ai/chat')
    },

    // 发送消息
    async sendMessage() {
      if (!this.inputMessage.trim()) return

      const userMessage = {
        id: Date.now(),
        role: 'user',
        content: this.inputMessage.trim(),
        timestamp: new Date().toISOString()
      }

      this.messages.push(userMessage)
      const question = this.inputMessage.trim()
      this.inputMessage = ''
      this.isLoading = true

      this.$nextTick(() => {
        this.scrollToBottom()
      })

      try {
        // 模拟AI回复
        await new Promise(resolve => setTimeout(resolve, 1500))
        
        const aiMessage = {
          id: Date.now() + 1,
          role: 'assistant',
          content: this.generateAiResponse(question),
          timestamp: new Date().toISOString()
        }

        this.messages.push(aiMessage)
      } catch (error) {
        const errorMessage = {
          id: Date.now() + 1,
          role: 'assistant',
          content: '抱歉，我遇到了一些问题，请稍后再试。',
          timestamp: new Date().toISOString()
        }
        this.messages.push(errorMessage)
      } finally {
        this.isLoading = false
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 快速提问
    askQuestion(question) {
      this.inputMessage = question
      this.sendMessage()
    },

    // 生成AI回复
    generateAiResponse(question) {
      const responses = {
        '帮我分析当前文档的要点': `基于当前页面内容，我为您总结以下要点：

**主要功能：**
- 文件管理和预览
- 协同编辑功能
- 多格式文档支持

**技术特点：**
- 响应式设计
- 实时协作
- 安全可靠

需要我详细解释某个具体功能吗？`,
        
        '查询原辅料信息': `我可以帮您查询原辅料的以下信息：

**基本信息：**
- 化学成分和分子式
- 物理性质和外观
- 质量标准和规格

**应用信息：**
- 功能作用和用途
- 推荐用量和配比
- 配伍禁忌和注意事项

请告诉我您想了解哪种原辅料？`
      }

      return responses[question] || `关于"${question}"，我正在为您分析...

基于我的知识库，我建议：

1. **明确需求**：请提供更多具体信息
2. **查看文档**：参考相关技术文档
3. **专业建议**：如需详细分析，建议使用完整版AI问答

您可以点击右上角的全屏按钮进入完整的AI问答界面。`
    },

    // 格式化消息
    formatMessage(content) {
      return content
        .replace(/\n/g, '<br>')
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
    },

    // 格式化时间
    formatTime(time) {
      const date = new Date(time)
      return date.toLocaleTimeString('zh-CN', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    },

    // 滚动到底部
    scrollToBottom() {
      this.$nextTick(() => {
        const chatContent = this.$refs.chatContent
        if (chatContent) {
          chatContent.scrollTop = chatContent.scrollHeight
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.ai-chat-widget {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

// 浮动按钮
.ai-float-button {
  position: relative;
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
  }

  i {
    font-size: 24px;
    color: white;
  }

  .pulse-ring {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    animation: pulse 2s infinite;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

// 聊天面板
.ai-chat-panel {
  width: 360px;
  height: 480px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-bottom: 12px;
}

.chat-panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .header-left {
    display: flex;
    align-items: center;
    gap: 8px;

    i {
      font-size: 18px;
    }

    span {
      font-weight: 500;
    }
  }

  .header-right {
    display: flex;
    gap: 4px;

    .el-button {
      color: white;
      border: none;
      background: transparent;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
      }
    }
  }
}

.chat-panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

// 欢迎消息
.welcome-message {
  text-align: center;

  .ai-avatar {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px auto;

    i {
      font-size: 24px;
      color: white;
    }
  }

  .welcome-text {
    margin-bottom: 16px;

    h4 {
      margin: 0 0 4px 0;
      color: #303133;
      font-size: 16px;
    }

    p {
      margin: 0;
      color: #606266;
      font-size: 14px;
    }
  }

  .quick-actions {
    display: flex;
    gap: 8px;
    justify-content: center;

    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;
      padding: 12px 8px;
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s ease;
      min-width: 80px;

      &:hover {
        border-color: #409eff;
        background: #f0f9ff;
      }

      i {
        font-size: 20px;
        color: #409eff;
      }

      span {
        font-size: 12px;
        color: #303133;
      }
    }
  }
}

// 消息列表
.message-list {
  .message-item {
    display: flex;
    margin-bottom: 16px;
    align-items: flex-start;
    gap: 8px;

    &.user-message {
      flex-direction: row-reverse;

      .message-content {
        background: #409eff;
        color: white;
        border-radius: 12px 12px 4px 12px;
      }
    }

    &.ai-message {
      .message-content {
        background: #f5f7fa;
        border-radius: 12px 12px 12px 4px;
      }
    }

    .message-avatar {
      flex-shrink: 0;

      .ai-avatar-small {
        width: 24px;
        height: 24px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 12px;
          color: white;
        }
      }
    }

    .message-content {
      max-width: 70%;
      padding: 8px 12px;

      .message-text {
        font-size: 14px;
        line-height: 1.4;
        word-wrap: break-word;

        ::v-deep {
          strong {
            font-weight: 600;
          }

          em {
            font-style: italic;
          }
        }
      }

      .message-time {
        font-size: 11px;
        opacity: 0.6;
        margin-top: 4px;
      }
    }
  }
}

// 输入中指示器
.typing-indicator {
  display: flex;
  gap: 3px;
  align-items: center;

  span {
    width: 6px;
    height: 6px;
    background: #409eff;
    border-radius: 50%;
    animation: typing 1.4s infinite ease-in-out;

    &:nth-child(1) { animation-delay: -0.32s; }
    &:nth-child(2) { animation-delay: -0.16s; }
  }
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.chat-panel-input {
  padding: 12px 16px;
  border-top: 1px solid #e4e7ed;

  ::v-deep .el-input-group__append {
    background: #409eff;
    border-color: #409eff;

    .el-button {
      color: white;
      border: none;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .ai-chat-widget {
    bottom: 10px;
    right: 10px;
  }

  .ai-chat-panel {
    width: calc(100vw - 20px);
    height: 60vh;
    max-width: 360px;
  }
}
</style>
