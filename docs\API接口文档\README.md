# 工作空间 API 接口文档

## 📋 概述

本文档集合包含工作空间文档管理系统的完整API接口文档，按功能模块分类组织，便于开发者查阅和使用。

## 📚 文档目录

### 核心模块

1. **[工作空间管理API](./01-工作空间管理API.md)**
   - 工作空间创建、管理、删除
   - 成员管理、权限控制
   - 统计信息、存储管理

2. **[文档管理API](./02-文档管理API.md)**
   - 文档CRUD操作
   - 文件夹管理
   - 文档分享、权限管理
   - 搜索功能

3. **[版本控制API](./03-版本控制API.md)**
   - 版本创建、查看、对比
   - 版本回滚、标签管理
   - 分支管理、合并操作
   - 版本统计

4. **[协作功能API](./04-协作功能API.md)**
   - 实时协作编辑
   - 光标同步、冲突解决
   - 评论系统、提醒通知
   - WebSocket通信

5. **[模板系统API](./05-模板系统API.md)**
   - 模板创建、管理、使用
   - 模板分类、标签管理
   - 模板版本、评论系统
   - 模板统计

6. **[文件管理API](./06-文件管理API.md)**
   - 文件上传、下载、预览
   - 分块上传、批量操作
   - 文件分享、存储统计
   - 文件搜索

7. **[AI智能问答API](./07-AI智能问答API.md)**
   - AI模型管理
   - 聊天对话、流式响应
   - 文档问答、原辅料分析
   - 智能搜索、推荐问题

## 🔗 基础信息

### API基础URL
```
生产环境: https://api.example.com/api/v1
测试环境: https://test-api.example.com/api/v1
开发环境: http://localhost:8080/api/v1
```

### 认证方式
所有API接口均使用Bearer Token认证：
```http
Authorization: Bearer your_access_token
```

### 数据格式
- **请求格式**: JSON (application/json)
- **响应格式**: JSON
- **字符编码**: UTF-8
- **时间格式**: ISO 8601 (YYYY-MM-DDTHH:mm:ssZ)

### 通用响应格式
```json
{
  "code": 200,
  "message": "success",
  "data": {},
  "timestamp": "2024-01-20T15:30:00Z",
  "requestId": "req_123456"
}
```

## 🚨 通用错误码

| 错误码 | HTTP状态码 | 说明 |
|--------|------------|------|
| 200 | 200 | 成功 |
| 400 | 400 | 请求参数错误 |
| 401 | 401 | 未授权，Token无效或过期 |
| 403 | 403 | 权限不足 |
| 404 | 404 | 资源不存在 |
| 409 | 409 | 资源冲突 |
| 413 | 413 | 请求体过大 |
| 422 | 422 | 请求参数验证失败 |
| 429 | 429 | 请求过于频繁 |
| 500 | 500 | 服务器内部错误 |
| 502 | 502 | 网关错误 |
| 503 | 503 | 服务不可用 |

## 📊 API使用统计

### 请求限制
- **普通用户**: 1000次/小时
- **高级用户**: 5000次/小时
- **企业用户**: 无限制

### 响应时间
- **文档操作**: < 200ms
- **文件上传**: 取决于文件大小
- **AI问答**: 1-5秒
- **协作功能**: < 100ms

## 🔧 SDK和工具

### JavaScript SDK
```bash
npm install @workspace/api-sdk
```

```javascript
import { WorkspaceAPI } from '@workspace/api-sdk'

const api = new WorkspaceAPI({
  baseURL: 'https://api.example.com/api/v1',
  token: 'your_access_token'
})

// 使用示例
const workspaces = await api.workspace.list()
const document = await api.document.create(workspaceId, documentData)
```

### Python SDK
```bash
pip install workspace-api-sdk
```

```python
from workspace_api import WorkspaceAPI

api = WorkspaceAPI(
    base_url='https://api.example.com/api/v1',
    token='your_access_token'
)

# 使用示例
workspaces = api.workspace.list()
document = api.document.create(workspace_id, document_data)
```

### Postman集合
下载Postman集合文件：[workspace-api.postman_collection.json](./postman/workspace-api.postman_collection.json)

## 📝 快速开始

### 1. 获取访问令牌
```http
POST /auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "password"
}
```

### 2. 创建工作空间
```http
POST /workspace
Authorization: Bearer your_token
Content-Type: application/json

{
  "name": "我的工作空间",
  "description": "工作空间描述"
}
```

### 3. 创建文档
```http
POST /workspace/{workspaceId}/documents
Authorization: Bearer your_token
Content-Type: application/json

{
  "title": "新文档",
  "content": "文档内容",
  "type": "markdown"
}
```

### 4. 上传文件
```http
POST /workspace/{workspaceId}/files/upload
Authorization: Bearer your_token
Content-Type: multipart/form-data

file: [文件对象]
folderId: folder_123
```

## 🧪 测试环境

### 测试账号
```
邮箱: <EMAIL>
密码: test123456
工作空间ID: ws_test_123456
```

### 测试数据
测试环境提供了完整的示例数据，包括：
- 5个示例工作空间
- 50个示例文档
- 20个文档模板
- 100个测试文件

## 📖 更新日志

### v1.2.0 (2024-01-20)
- 新增AI智能问答模块
- 优化文件上传性能
- 增强协作功能稳定性

### v1.1.0 (2024-01-15)
- 新增模板系统
- 支持文档版本控制
- 增加批量操作接口

### v1.0.0 (2024-01-01)
- 初始版本发布
- 基础文档管理功能
- 工作空间管理功能

## 🤝 技术支持

### 联系方式
- **技术支持邮箱**: <EMAIL>
- **开发者社区**: https://community.example.com
- **GitHub仓库**: https://github.com/example/workspace-api

### 问题反馈
如果您在使用API过程中遇到问题，请通过以下方式反馈：

1. **GitHub Issues**: 提交bug报告或功能请求
2. **技术支持邮箱**: 发送详细的问题描述
3. **开发者社区**: 与其他开发者交流讨论

### 文档贡献
欢迎为API文档贡献内容：
1. Fork文档仓库
2. 创建功能分支
3. 提交Pull Request
4. 等待审核合并

## 📄 许可证

本API文档遵循 [MIT License](./LICENSE) 开源协议。

---

**最后更新**: 2024-01-20  
**文档版本**: v1.2.0  
**API版本**: v1
