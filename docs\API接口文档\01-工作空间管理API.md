# 工作空间管理 API 接口文档

## 📋 概述

工作空间管理模块提供工作空间的创建、管理、成员管理等核心功能。

## 🔗 基础信息

- **基础URL**: `/api/v1/workspace`
- **认证方式**: <PERSON><PERSON>
- **数据格式**: JSON
- **字符编码**: UTF-8

## 📚 工作空间基础 API

### 1. 获取工作空间列表

```http
GET /workspace/list
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | Number | 否 | 页码，默认1 |
| pageSize | Number | 否 | 每页数量，默认10 |
| keyword | String | 否 | 搜索关键词 |
| status | String | 否 | 状态筛选：active/inactive |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "ws_123456",
        "name": "产品研发工作空间",
        "description": "产品研发团队协作空间",
        "avatar": "workspace_avatar.jpg",
        "ownerId": "user_123",
        "ownerName": "张三",
        "memberCount": 15,
        "documentCount": 128,
        "storageUsed": 2048576,
        "storageLimit": 10737418240,
        "status": "active",
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-15T10:30:00Z"
      }
    ],
    "total": 5,
    "page": 1,
    "pageSize": 10
  }
}
```

### 2. 获取工作空间详情

```http
GET /workspace/{id}
```

**路径参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | String | 是 | 工作空间ID |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "ws_123456",
    "name": "产品研发工作空间",
    "description": "产品研发团队协作空间",
    "avatar": "workspace_avatar.jpg",
    "ownerId": "user_123",
    "ownerName": "张三",
    "memberLimit": 50,
    "storageLimit": 10737418240,
    "settings": {
      "allowInvite": true,
      "defaultPermission": "read",
      "enableVersionControl": true,
      "enableCollaboration": true
    },
    "statistics": {
      "memberCount": 15,
      "documentCount": 128,
      "storageUsed": 2048576,
      "activeUsers": 8
    },
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-15T10:30:00Z"
  }
}
```

### 3. 创建工作空间

```http
POST /workspace
```

**请求体**

```json
{
  "name": "新工作空间",
  "description": "工作空间描述",
  "avatar": "avatar_url",
  "memberLimit": 20,
  "storageLimit": 5368709120,
  "settings": {
    "allowInvite": true,
    "defaultPermission": "read",
    "enableVersionControl": true,
    "enableCollaboration": true
  }
}
```

**响应示例**

```json
{
  "code": 200,
  "message": "工作空间创建成功",
  "data": {
    "id": "ws_789012",
    "name": "新工作空间",
    "description": "工作空间描述",
    "ownerId": "user_123",
    "inviteCode": "ABC123",
    "createdAt": "2024-01-20T14:30:00Z"
  }
}
```

### 4. 更新工作空间

```http
PUT /workspace
```

**请求体**

```json
{
  "id": "ws_123456",
  "name": "更新后的工作空间名称",
  "description": "更新后的描述",
  "avatar": "new_avatar_url",
  "settings": {
    "allowInvite": false,
    "defaultPermission": "write"
  }
}
```

### 5. 删除工作空间

```http
DELETE /workspace/{id}
```

**路径参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | String | 是 | 工作空间ID |

**响应示例**

```json
{
  "code": 200,
  "message": "工作空间删除成功"
}
```

## 👥 成员管理 API

### 1. 获取成员列表

```http
GET /workspace/{workspaceId}/members
```

**请求参数**

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | Number | 否 | 页码，默认1 |
| pageSize | Number | 否 | 每页数量，默认20 |
| role | String | 否 | 角色筛选：owner/admin/member/viewer |
| status | String | 否 | 状态筛选：active/pending/inactive |

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "member_123",
        "userId": "user_123",
        "userName": "张三",
        "userEmail": "<EMAIL>",
        "userAvatar": "avatar.jpg",
        "role": "owner",
        "status": "active",
        "permissions": ["read", "write", "admin"],
        "joinedAt": "2024-01-01T00:00:00Z",
        "lastActiveAt": "2024-01-20T15:30:00Z"
      }
    ],
    "total": 15,
    "page": 1,
    "pageSize": 20
  }
}
```

### 2. 邀请成员

```http
POST /workspace/{workspaceId}/members/invite
```

**请求体**

```json
{
  "emails": ["<EMAIL>", "<EMAIL>"],
  "role": "member",
  "message": "邀请您加入我们的工作空间"
}
```

### 3. 加入工作空间

```http
POST /workspace/join
```

**请求体**

```json
{
  "workspaceId": "ws_123456",
  "inviteCode": "ABC123"
}
```

### 4. 退出工作空间

```http
DELETE /workspace/leave/{workspaceId}
```

### 5. 移除成员

```http
DELETE /workspace/{workspaceId}/members/{userId}
```

### 6. 更新成员角色

```http
PUT /workspace/{workspaceId}/members/{userId}/role
```

**请求体**

```json
{
  "role": "admin",
  "permissions": ["read", "write", "admin"]
}
```

## 📊 统计信息 API

### 1. 获取工作空间统计

```http
GET /workspace/{workspaceId}/statistics
```

**响应示例**

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "overview": {
      "memberCount": 15,
      "documentCount": 128,
      "storageUsed": 2048576,
      "storageLimit": 10737418240,
      "activeUsers": 8
    },
    "activity": {
      "todayDocuments": 5,
      "todayEdits": 23,
      "todayComments": 12,
      "weeklyGrowth": 15.5
    },
    "storage": {
      "documents": 1536000,
      "images": 256000,
      "attachments": 256576,
      "usagePercentage": 19.2
    }
  }
}
```

### 2. 获取存储统计

```http
GET /workspace/{workspaceId}/storage/stats
```

## 🚨 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 工作空间不存在 |
| 409 | 工作空间名称已存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 📝 使用示例

### JavaScript/Axios

```javascript
import axios from 'axios'

// 创建工作空间
const createWorkspace = async (workspaceData) => {
  try {
    const response = await axios.post('/api/v1/workspace', workspaceData, {
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    })
    return response.data
  } catch (error) {
    console.error('创建工作空间失败:', error)
    throw error
  }
}

// 获取成员列表
const getMembers = async (workspaceId, query = {}) => {
  try {
    const response = await axios.get(`/api/v1/workspace/${workspaceId}/members`, {
      params: query,
      headers: {
        'Authorization': `Bearer ${token}`
      }
    })
    return response.data
  } catch (error) {
    console.error('获取成员列表失败:', error)
    throw error
  }
}
```
