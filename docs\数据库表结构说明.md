# 工作空间文档管理系统数据库表结构说明

## 📋 概述

本文档详细说明工作空间文档管理系统的数据库表结构设计，包含27个核心表和2个视图，覆盖了文档管理、版本控制、协作、模板、权限等所有功能模块。

## 🗂️ 表结构分类

### 1. 核心业务表 (Core Tables)

#### 1.1 工作空间相关
- **workspaces** - 工作空间主表
- **workspace_members** - 工作空间成员表
- **folders** - 文件夹表

#### 1.2 文档相关
- **documents** - 文档主表
- **document_permissions** - 文档权限表
- **document_shares** - 文档分享表
- **document_favorites** - 文档收藏表
- **document_access_logs** - 文档访问记录表
- **document_statistics** - 文档统计表

### 2. 版本控制表 (Version Control Tables)

- **document_versions** - 文档版本表
- **version_changes** - 版本变更记录表
- **version_tags** - 版本标签表
- **document_branches** - 文档分支表

### 3. 协作相关表 (Collaboration Tables)

- **collaboration_sessions** - 协作会话表
- **collaboration_participants** - 协作参与者表
- **collaboration_operations** - 协作操作记录表

### 4. 评论系统表 (Comment System Tables)

- **comments** - 评论表
- **mentions** - 提醒记录表

### 5. 模板系统表 (Template System Tables)

- **templates** - 模板表
- **template_categories** - 模板分类表
- **template_tags** - 模板标签表
- **template_versions** - 模板版本表
- **template_comments** - 模板评论表

### 6. 系统支持表 (System Support Tables)

- **file_uploads** - 文件上传表
- **activity_logs** - 活动日志表
- **notifications** - 通知表
- **system_configs** - 系统配置表

## 📊 核心表详细说明

### 1. workspaces (工作空间表)

**功能**: 存储工作空间的基本信息和配置

**关键字段**:
- `storage_limit/storage_used`: 存储配额管理
- `member_limit`: 成员数量限制
- `settings`: JSON格式的工作空间设置

**索引策略**:
- 主键索引: `id`
- 业务索引: `owner_id`, `status`

### 2. documents (文档表)

**功能**: 文档管理的核心表，存储文档的基本信息和内容

**关键字段**:
- `content`: 文档内容(longtext)
- `content_type`: 内容类型(markdown/html/text)
- `word_count/character_count`: 统计信息
- `view_count/edit_count/comment_count`: 活动统计
- `is_public/is_template/is_favorite`: 状态标记

**索引策略**:
- 主键索引: `id`
- 业务索引: `workspace_id`, `folder_id`, `author_id`
- 复合索引: `workspace_id + folder_id + is_deleted`
- 全文索引: `title + content`

### 3. document_versions (文档版本表)

**功能**: 实现文档的版本控制功能

**关键字段**:
- `version_number`: 版本号(递增)
- `content_hash`: 内容哈希值(用于去重)
- `changes_summary`: JSON格式的变更摘要
- `parent_version_id`: 版本关系链

**索引策略**:
- 主键索引: `id`
- 唯一索引: `document_id + version_number`
- 业务索引: `content_hash`, `parent_version_id`

### 4. collaboration_sessions (协作会话表)

**功能**: 管理实时协作会话

**关键字段**:
- `session_key`: 会话唯一标识
- `max_participants/current_participants`: 参与者管理
- `is_active`: 会话状态
- `settings`: JSON格式的会话配置

**索引策略**:
- 主键索引: `id`
- 唯一索引: `session_key`
- 业务索引: `document_id`, `is_active`

### 5. collaboration_operations (协作操作记录表)

**功能**: 记录协作过程中的所有操作，支持操作回放和冲突解决

**关键字段**:
- `operation_type`: 操作类型
- `operation_data`: JSON格式的操作数据
- `version_vector`: 版本向量(用于冲突解决)
- `timestamp`: 精确时间戳

**索引策略**:
- 主键索引: `id`
- 业务索引: `session_id`, `timestamp`
- 复合索引: `session_id + timestamp`

## 🔗 表关系说明

### 主要外键关系

```
workspaces (1) ←→ (N) workspace_members
workspaces (1) ←→ (N) documents
workspaces (1) ←→ (N) folders
workspaces (1) ←→ (N) templates

documents (1) ←→ (N) document_versions
documents (1) ←→ (N) comments
documents (1) ←→ (N) collaboration_sessions
documents (1) ←→ (N) document_permissions

collaboration_sessions (1) ←→ (N) collaboration_participants
collaboration_sessions (1) ←→ (N) collaboration_operations

templates (1) ←→ (N) template_versions
templates (1) ←→ (N) template_comments
```

### 自关联关系

```
folders.parent_id → folders.id (文件夹层级)
comments.parent_id → comments.id (评论回复)
template_categories.parent_id → template_categories.id (分类层级)
```

## 📈 性能优化策略

### 1. 索引优化

**复合索引**:
```sql
-- 文档查询优化
idx_workspace_folder_status (workspace_id, folder_id, is_deleted)

-- 版本查询优化  
idx_document_created (document_id, created_at)

-- 协作操作查询优化
idx_session_timestamp (session_id, timestamp)
```

**全文索引**:
```sql
-- 文档搜索优化
FULLTEXT ft_title_content (title, content)

-- 模板搜索优化
FULLTEXT ft_name_description (name, description)
```

### 2. 分区策略

**时间分区** (建议):
```sql
-- 活动日志按月分区
ALTER TABLE activity_logs PARTITION BY RANGE (YEAR(created_at)*100 + MONTH(created_at));

-- 协作操作按日分区
ALTER TABLE collaboration_operations PARTITION BY RANGE (TO_DAYS(created_at));
```

### 3. 存储优化

**大字段分离**:
- `documents.content` 考虑分离到独立表
- `document_versions.content` 考虑压缩存储

**JSON字段优化**:
- 为常用JSON字段创建虚拟列和索引
- 使用JSON函数进行高效查询

## 🔒 数据安全设计

### 1. 软删除策略

所有核心业务表都采用软删除:
- `is_deleted` 字段标记删除状态
- 保留数据用于审计和恢复
- 定期清理过期的软删除数据

### 2. 权限控制

**多层权限设计**:
1. 工作空间级别权限 (`workspace_members.role`)
2. 文档级别权限 (`document_permissions`)
3. 操作级别权限 (read/write/comment/share/admin)

### 3. 数据审计

**完整的操作记录**:
- `activity_logs` 记录所有用户操作
- `collaboration_operations` 记录协作操作
- `document_access_logs` 记录访问日志

## 📊 统计与分析

### 1. 实时统计

**文档统计字段**:
- 查看次数、编辑次数、评论数量
- 字数统计、字符数统计
- 协作时长、活跃用户数

### 2. 历史统计

**按日统计** (`document_statistics`):
- 支持趋势分析
- 支持报表生成
- 支持数据挖掘

### 3. 统计视图

**预定义视图**:
```sql
-- 文档详情视图
v_document_details

-- 工作空间统计视图  
v_workspace_stats
```

## 🚀 扩展性设计

### 1. 水平扩展

**分库分表策略**:
- 按 `workspace_id` 分库
- 大表按时间或ID范围分表
- 读写分离支持

### 2. 功能扩展

**预留字段**:
- `metadata` JSON字段用于功能扩展
- `settings` JSON字段用于配置扩展
- `permissions` JSON字段用于权限扩展

### 3. 版本兼容

**向后兼容**:
- 新增字段设置默认值
- 使用JSON字段存储新功能数据
- 保持API接口稳定

## 📝 使用建议

### 1. 查询优化

**常用查询模式**:
```sql
-- 获取工作空间文档列表
SELECT * FROM v_document_details 
WHERE workspace_id = ? AND folder_id = ? 
ORDER BY updated_at DESC;

-- 获取文档版本历史
SELECT * FROM document_versions 
WHERE document_id = ? 
ORDER BY version_number DESC;

-- 获取协作会话信息
SELECT s.*, COUNT(p.id) as participant_count
FROM collaboration_sessions s
LEFT JOIN collaboration_participants p ON s.id = p.session_id
WHERE s.document_id = ? AND s.is_active = 1;
```

### 2. 数据维护

**定期维护任务**:
- 清理过期的协作会话
- 压缩历史版本数据
- 更新统计信息
- 清理软删除数据

### 3. 监控指标

**关键指标**:
- 存储空间使用率
- 协作会话数量
- 版本创建频率
- 查询响应时间

---

📧 如有问题，请联系数据库管理员或查看详细的SQL文件。
