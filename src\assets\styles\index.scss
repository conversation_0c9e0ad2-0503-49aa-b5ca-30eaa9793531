@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.deleted {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

/* 全局弹出框样式优化 */
.el-dialog {
  border-radius: 12px !important;
  box-shadow: 0 12px 32px 4px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.08) !important;

  .el-dialog__header {
    padding: 20px 24px 16px !important;
    border-bottom: 1px solid #f0f0f0 !important;

    .el-dialog__title {
      font-size: 16px !important;
      font-weight: 600 !important;
      color: #303133 !important;
    }

    .el-dialog__headerbtn {
      top: 20px !important;
      right: 20px !important;

      .el-dialog__close {
        font-size: 18px !important;
        color: #909399 !important;
        transition: color 0.3s !important;

        &:hover {
          color: #409eff !important;
        }
      }
    }
  }

  .el-dialog__body {
    padding: 24px !important;
  }

  .el-dialog__footer {
    padding: 16px 24px 24px !important;
    border-top: 1px solid #f0f0f0 !important;
    text-align: right !important;
  }
}

/* 全局表单样式优化 */
.el-dialog .el-form {
  .el-form-item {
    margin-bottom: 20px;

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }

    .el-select,
    .el-input,
    .el-cascader,
    .el-date-editor {
      width: 100%;

      .el-input__inner,
      .el-select__input {
        border-radius: 8px !important;
        border: 1px solid #dcdfe6 !important;
        transition: all 0.3s !important;

        &:focus {
          border-color: #409eff !important;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1) !important;
        }
      }
    }

    .el-textarea {
      .el-textarea__inner {
        border-radius: 8px !important;
        border: 1px solid #dcdfe6 !important;
        transition: all 0.3s !important;
        resize: vertical;

        &:focus {
          border-color: #409eff !important;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1) !important;
        }

        &::placeholder {
          color: #c0c4cc;
          font-style: italic;
        }
      }
    }

    .el-input-number {
      width: 100%;

      .el-input__inner {
        border-radius: 8px !important;
        transition: all 0.3s !important;

        &:focus {
          border-color: #409eff !important;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1) !important;
        }
      }
    }

    .el-radio-group,
    .el-checkbox-group {
      .el-radio,
      .el-checkbox {
        margin-right: 20px;

        .el-radio__label,
        .el-checkbox__label {
          font-weight: 400;
        }
      }
    }
  }
}

/* 全局按钮样式优化 */
.el-dialog .dialog-footer {
  .el-button {
    border-radius: 8px !important;
    padding: 10px 20px !important;
    font-weight: 500 !important;
    transition: all 0.3s !important;
    min-width: 100px !important;

    &:not(.el-button--primary) {
      margin-right: 12px;

      &:hover {
        background-color: #f5f7fa !important;
        border-color: #c0c4cc !important;
      }
    }

    &.el-button--primary {
      &:hover {
        background-color: #66b1ff !important;
        border-color: #66b1ff !important;
      }

      &:active {
        background-color: #3a8ee6 !important;
        border-color: #3a8ee6 !important;
      }
    }

    &.el-button--success {
      &:hover {
        background-color: #85ce61 !important;
        border-color: #85ce61 !important;
      }
    }

    &.el-button--warning {
      &:hover {
        background-color: #ebb563 !important;
        border-color: #ebb563 !important;
      }
    }
  }
}

/* 全局提示框样式 */
.dialog-tips,
.import-tips,
.form-tips {
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
  border: 1px solid #e1e8ff;
  border-radius: 10px;
  padding: 16px 20px;
  margin-top: 8px;

  .tips-header {
    display: flex;
    align-items: center;
    margin-bottom: 12px;

    i {
      color: #409eff;
      font-size: 16px;
      margin-right: 8px;
    }

    span {
      font-weight: 600;
      color: #303133;
      font-size: 14px;
    }
  }

  .tips-content {
    .tip-item {
      display: flex;
      align-items: center;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      i {
        color: #67c23a;
        font-size: 14px;
        margin-right: 8px;
        flex-shrink: 0;
      }

      span {
        color: #606266;
        font-size: 13px;
        line-height: 1.4;
      }
    }
  }

  // 简单提示样式
  &.simple-tips {
    background: #f4f4f5;
    border: 1px solid #e4e7ed;

    p {
      margin: 0 0 8px 0;
      font-weight: 500;
      color: #606266;

      i {
        color: #409eff;
        margin-right: 4px;
      }
    }

    ul {
      margin: 0;
      padding-left: 16px;

      li {
        margin-bottom: 4px;
        color: #909399;
        line-height: 1.4;
      }
    }
  }
}

/* 特殊对话框类型优化 */
.el-message-box {
  border-radius: 12px !important;
  box-shadow: 0 12px 32px 4px rgba(0, 0, 0, 0.04), 0 8px 20px rgba(0, 0, 0, 0.08) !important;

  .el-message-box__header {
    padding: 20px 24px 16px !important;

    .el-message-box__title {
      font-weight: 600 !important;
    }
  }

  .el-message-box__content {
    padding: 0 24px !important;
  }

  .el-message-box__btns {
    padding: 16px 24px 24px !important;

    .el-button {
      border-radius: 8px !important;
      padding: 10px 20px !important;
      font-weight: 500 !important;
      min-width: 100px !important;
      margin-left: 12px !important;

      &:first-child {
        margin-left: 0 !important;
      }
    }
  }
}

/* 抽屉组件优化 */
.el-drawer {
  .el-drawer__header {
    padding: 20px 24px 16px !important;
    border-bottom: 1px solid #f0f0f0 !important;

    span {
      font-size: 16px !important;
      font-weight: 600 !important;
      color: #303133 !important;
    }
  }

  .el-drawer__body {
    padding: 24px !important;
  }
}

/* 全局对话框底部按钮区域 */
.el-dialog__footer {
  padding: 16px 24px 24px !important;
  text-align: right !important;

  .el-button {
    border-radius: 8px !important;
    padding: 10px 20px !important;
    font-weight: 500 !important;
    min-width: 100px !important;
    margin-left: 12px !important;

    &:first-child {
      margin-left: 0 !important;
    }

    &:hover {
      transform: translateY(-1px);
    }
  }
}

/* 自定义消息框样式 */
.custom-message-box {
  .el-message-box__btns {
    .el-button {
      min-width: 100px !important;
      padding: 10px 20px !important;
      border-radius: 8px !important;
      font-weight: 500 !important;
      margin-left: 12px !important;

      &:first-child {
        margin-left: 0 !important;
      }

      &.el-button--default {
        &:hover {
          background-color: #f5f7fa !important;
          border-color: #c0c4cc !important;
        }
      }

      &.el-button--primary {
        &:hover {
          background-color: #66b1ff !important;
          border-color: #66b1ff !important;
        }
      }
    }
  }
}



