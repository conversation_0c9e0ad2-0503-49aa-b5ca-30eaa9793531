import request from '@/utils/request'

// 获取支持的AI模型列表
export function getAiModels() {
  return request({
    url: '/ai/models',
    method: 'get'
  })
}

// 发送聊天消息
export function sendChatMessage(data) {
  return request({
    url: '/ai/chat',
    method: 'post',
    data: data
  })
}

// 获取聊天历史
export function getChatHistory(sessionId) {
  return request({
    url: '/ai/chat/history/' + sessionId,
    method: 'get'
  })
}

// 创建新的聊天会话
export function createChatSession(data) {
  return request({
    url: '/ai/chat/session',
    method: 'post',
    data: data
  })
}

// 删除聊天会话
export function deleteChatSession(sessionId) {
  return request({
    url: '/ai/chat/session/' + sessionId,
    method: 'delete'
  })
}

// 获取用户的聊天会话列表
export function getChatSessions() {
  return request({
    url: '/ai/chat/sessions',
    method: 'get'
  })
}

// 文档问答
export function askDocument(data) {
  return request({
    url: '/ai/document/ask',
    method: 'post',
    data: data
  })
}

// 原辅料数据问答
export function askMaterial(data) {
  return request({
    url: '/ai/material/ask',
    method: 'post',
    data: data
  })
}

// 获取文档摘要
export function getDocumentSummary(documentId) {
  return request({
    url: '/ai/document/summary/' + documentId,
    method: 'get'
  })
}

// 获取原辅料分析报告
export function getMaterialAnalysis(materialId) {
  return request({
    url: '/ai/material/analysis/' + materialId,
    method: 'get'
  })
}

// 批量分析文档
export function batchAnalyzeDocuments(documentIds) {
  return request({
    url: '/ai/document/batch-analyze',
    method: 'post',
    data: { documentIds }
  })
}

// 智能搜索
export function intelligentSearch(data) {
  return request({
    url: '/ai/search',
    method: 'post',
    data: data
  })
}

// 获取推荐问题
export function getRecommendedQuestions(context) {
  return request({
    url: '/ai/questions/recommend',
    method: 'post',
    data: { context }
  })
}

// 导出聊天记录
export function exportChatHistory(sessionId, format = 'pdf') {
  return request({
    url: '/ai/chat/export/' + sessionId,
    method: 'get',
    params: { format },
    responseType: 'blob'
  })
}
