<template>
  <div class="comment-system">
    <!-- 评论按钮 -->
    <el-button
      v-if="!showComments"
      type="text"
      icon="el-icon-chat-dot-round"
      @click="toggleComments"
      class="comment-toggle"
    >
      评论 ({{ comments.length }})
    </el-button>

    <!-- 评论面板 -->
    <div v-if="showComments" class="comment-panel">
      <div class="comment-header">
        <h4>评论</h4>
        <el-button type="text" icon="el-icon-close" @click="toggleComments"></el-button>
      </div>

      <!-- 评论列表 -->
      <div class="comment-list">
        <div
          v-for="comment in comments"
          :key="comment.id"
          class="comment-item"
          :class="{ 'highlighted': highlightedCommentId === comment.id }"
        >
          <!-- 评论内容 -->
          <div class="comment-content">
            <div class="comment-author">
              <el-avatar :src="comment.author.avatar" :size="24">
                {{ comment.author.nickname ? comment.author.nickname.charAt(0) : 'U' }}
              </el-avatar>
              <span class="author-name">{{ comment.author.nickname }}</span>
              <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
            </div>
            
            <div class="comment-text" v-html="formatCommentContent(comment.content)"></div>
            
            <!-- 提醒标签 -->
            <div v-if="comment.mentions && comment.mentions.length > 0" class="mentions">
              <el-tag
                v-for="mention in comment.mentions"
                :key="mention"
                size="mini"
                type="info"
              >
                @{{ mention }}
              </el-tag>
            </div>
          </div>

          <!-- 评论操作 -->
          <div class="comment-actions">
            <el-button type="text" size="mini" @click="replyToComment(comment)">
              回复
            </el-button>
            <el-button
              v-if="canDeleteComment(comment)"
              type="text"
              size="mini"
              @click="deleteComment(comment)"
            >
              删除
            </el-button>
          </div>

          <!-- 回复列表 -->
          <div v-if="comment.replies && comment.replies.length > 0" class="reply-list">
            <div
              v-for="reply in comment.replies"
              :key="reply.id"
              class="reply-item"
            >
              <div class="reply-author">
                <el-avatar :src="reply.author.avatar" :size="20">
                  {{ reply.author.nickname ? reply.author.nickname.charAt(0) : 'U' }}
                </el-avatar>
                <span class="author-name">{{ reply.author.nickname }}</span>
                <span class="reply-time">{{ formatTime(reply.createdAt) }}</span>
              </div>
              <div class="reply-text" v-html="formatCommentContent(reply.content)"></div>
            </div>
          </div>

          <!-- 回复输入框 -->
          <div v-if="replyingToComment === comment.id" class="reply-input">
            <el-input
              v-model="replyContent"
              type="textarea"
              :rows="2"
              placeholder="输入回复内容，使用@提醒其他用户..."
              @keydown.ctrl.enter="submitReply(comment)"
              ref="replyInput"
            >
            </el-input>
            <div class="reply-actions">
              <el-button size="mini" @click="cancelReply">取消</el-button>
              <el-button type="primary" size="mini" @click="submitReply(comment)">
                回复
              </el-button>
            </div>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="comments.length === 0" class="empty-comments">
          <i class="el-icon-chat-dot-round"></i>
          <p>还没有评论，来添加第一条评论吧！</p>
        </div>
      </div>

      <!-- 新增评论 -->
      <div class="add-comment">
        <el-input
          v-model="newComment"
          type="textarea"
          :rows="3"
          placeholder="添加评论，使用@提醒其他用户..."
          @keydown.ctrl.enter="addComment"
        >
        </el-input>
        
        <!-- @提醒建议 -->
        <div v-if="showMentionSuggestions" class="mention-suggestions">
          <div
            v-for="user in mentionSuggestions"
            :key="user.id"
            class="mention-item"
            @click="selectMention(user)"
          >
            <el-avatar :src="user.avatar" :size="20">
              {{ user.nickname ? user.nickname.charAt(0) : 'U' }}
            </el-avatar>
            <span>{{ user.nickname }}</span>
          </div>
        </div>

        <div class="comment-actions">
          <div class="comment-tips">
            <span>Ctrl + Enter 快速发送</span>
          </div>
          <el-button type="primary" @click="addComment" :disabled="!newComment.trim()">
            发表评论
          </el-button>
        </div>
      </div>
    </div>

    <!-- 提醒通知 -->
    <div v-if="mentionNotifications.length > 0" class="mention-notifications">
      <div
        v-for="notification in mentionNotifications"
        :key="notification.id"
        class="mention-notification"
      >
        <el-alert
          :title="`${notification.mentionedBy.nickname} 在评论中提到了你`"
          type="info"
          :closable="true"
          @close="dismissNotification(notification.id)"
        >
          <div slot="default">
            <p>{{ notification.comment.content }}</p>
            <el-button type="text" size="mini" @click="goToComment(notification.comment.id)">
              查看评论
            </el-button>
          </div>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script>
import enhancedCollaborationService from '@/utils/enhancedCollaboration'

export default {
  name: 'CommentSystem',
  props: {
    documentId: {
      type: String,
      required: true
    },
    connectionId: {
      type: String,
      required: true
    },
    collaborators: {
      type: Array,
      default: () => []
    },
    currentUser: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      showComments: false,
      comments: [],
      newComment: '',
      replyContent: '',
      replyingToComment: null,
      highlightedCommentId: null,
      mentionNotifications: [],
      showMentionSuggestions: false,
      mentionSuggestions: [],
      mentionStartIndex: -1
    }
  },
  computed: {
    availableUsers() {
      return this.collaborators.filter(user => user.id !== this.currentUser.id)
    }
  },
  watch: {
    newComment(value) {
      this.handleMentionInput(value)
    },
    replyContent(value) {
      this.handleMentionInput(value)
    }
  },
  mounted() {
    this.loadComments()
    this.setupCollaborationListeners()
  },
  beforeDestroy() {
    this.removeCollaborationListeners()
  },
  methods: {
    // 切换评论面板显示
    toggleComments() {
      this.showComments = !this.showComments
    },

    // 加载评论
    loadComments() {
      this.comments = enhancedCollaborationService.getComments(this.documentId)
    },

    // 添加评论
    addComment() {
      if (!this.newComment.trim()) return

      const comment = enhancedCollaborationService.addComment(this.connectionId, {
        content: this.newComment,
        position: 0 // 可以根据光标位置设置
      })

      if (comment) {
        this.comments.push(comment)
        this.newComment = ''
        this.$message.success('评论发表成功')
      }
    },

    // 回复评论
    replyToComment(comment) {
      this.replyingToComment = comment.id
      this.$nextTick(() => {
        if (this.$refs.replyInput) {
          this.$refs.replyInput.focus()
        }
      })
    },

    // 提交回复
    submitReply(comment) {
      if (!this.replyContent.trim()) return

      const reply = enhancedCollaborationService.replyComment(
        this.connectionId,
        comment.id,
        { content: this.replyContent }
      )

      if (reply) {
        comment.replies.push(reply)
        this.replyContent = ''
        this.replyingToComment = null
        this.$message.success('回复发表成功')
      }
    },

    // 取消回复
    cancelReply() {
      this.replyingToComment = null
      this.replyContent = ''
    },

    // 删除评论
    deleteComment(comment) {
      this.$confirm('确定要删除这条评论吗？', '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.comments.findIndex(c => c.id === comment.id)
        if (index > -1) {
          this.comments.splice(index, 1)
          this.$message.success('评论删除成功')
        }
      })
    },

    // 检查是否可以删除评论
    canDeleteComment(comment) {
      return comment.author.id === this.currentUser.id
    },

    // 格式化评论内容（处理@提醒）
    formatCommentContent(content) {
      return content.replace(/@(\w+)/g, '<span class="mention">@$1</span>')
    },

    // 格式化时间
    formatTime(timeString) {
      const time = new Date(timeString)
      const now = new Date()
      const diff = now - time

      if (diff < 60000) return '刚刚'
      if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
      if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
      return time.toLocaleDateString()
    },

    // 处理@提醒输入
    handleMentionInput(value) {
      const atIndex = value.lastIndexOf('@')
      if (atIndex === -1) {
        this.showMentionSuggestions = false
        return
      }

      const afterAt = value.substring(atIndex + 1)
      if (afterAt.includes(' ')) {
        this.showMentionSuggestions = false
        return
      }

      this.mentionStartIndex = atIndex
      this.mentionSuggestions = this.availableUsers.filter(user =>
        user.nickname.toLowerCase().includes(afterAt.toLowerCase())
      )
      this.showMentionSuggestions = this.mentionSuggestions.length > 0
    },

    // 选择@提醒用户
    selectMention(user) {
      const isReply = this.replyingToComment !== null
      const currentText = isReply ? this.replyContent : this.newComment
      
      const beforeMention = currentText.substring(0, this.mentionStartIndex)
      const afterMention = currentText.substring(this.mentionStartIndex + 1)
      const afterAtSymbol = afterMention.substring(afterMention.indexOf(' ') + 1)
      
      const newText = beforeMention + `@${user.nickname} ` + afterAtSymbol

      if (isReply) {
        this.replyContent = newText
      } else {
        this.newComment = newText
      }

      this.showMentionSuggestions = false
    },

    // 跳转到指定评论
    goToComment(commentId) {
      this.highlightedCommentId = commentId
      this.showComments = true
      
      setTimeout(() => {
        this.highlightedCommentId = null
      }, 3000)
    },

    // 设置协作监听器
    setupCollaborationListeners() {
      enhancedCollaborationService.on(this.connectionId, 'commentAdded', this.handleCommentAdded)
      enhancedCollaborationService.on(this.connectionId, 'commentReply', this.handleCommentReply)
      enhancedCollaborationService.on(this.connectionId, 'mention', this.handleMention)
    },

    // 移除协作监听器
    removeCollaborationListeners() {
      enhancedCollaborationService.off(this.connectionId, 'commentAdded', this.handleCommentAdded)
      enhancedCollaborationService.off(this.connectionId, 'commentReply', this.handleCommentReply)
      enhancedCollaborationService.off(this.connectionId, 'mention', this.handleMention)
    },

    // 处理新评论
    handleCommentAdded(comment) {
      this.comments.push(comment)
    },

    // 处理评论回复
    handleCommentReply(data) {
      const comment = this.comments.find(c => c.id === data.commentId)
      if (comment) {
        comment.replies.push(data.reply)
      }
    },

    // 处理@提醒
    handleMention(data) {
      this.mentionNotifications.push({
        id: Date.now(),
        ...data
      })
    },

    // 关闭提醒通知
    dismissNotification(notificationId) {
      const index = this.mentionNotifications.findIndex(n => n.id === notificationId)
      if (index > -1) {
        this.mentionNotifications.splice(index, 1)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.comment-system {
  position: relative;
}

.comment-toggle {
  margin-left: 8px;
}

.comment-panel {
  position: fixed;
  right: 20px;
  top: 20px;
  width: 350px;
  max-height: 80vh;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 2000;
  display: flex;
  flex-direction: column;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
  
  h4 {
    margin: 0;
    font-size: 16px;
    color: #303133;
  }
}

.comment-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  max-height: 400px;
}

.comment-item {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 6px;
  transition: background-color 0.3s;
  
  &.highlighted {
    background-color: #f0f9ff;
    border: 1px solid #409eff;
  }
}

.comment-content {
  margin-bottom: 8px;
}

.comment-author,
.reply-author {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  
  .author-name {
    margin-left: 8px;
    font-weight: 500;
    color: #303133;
  }
  
  .comment-time,
  .reply-time {
    margin-left: auto;
    font-size: 12px;
    color: #909399;
  }
}

.comment-text,
.reply-text {
  color: #606266;
  line-height: 1.5;
  margin-bottom: 8px;
}

.mentions {
  margin-top: 8px;
  
  .el-tag {
    margin-right: 4px;
  }
}

.comment-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

.reply-list {
  margin-top: 12px;
  padding-left: 16px;
  border-left: 2px solid #ebeef5;
}

.reply-item {
  margin-bottom: 12px;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.reply-input {
  margin-top: 12px;
  
  .reply-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 8px;
  }
}

.add-comment {
  padding: 16px;
  border-top: 1px solid #ebeef5;
  
  .comment-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
  }
  
  .comment-tips {
    font-size: 12px;
    color: #909399;
  }
}

.mention-suggestions {
  position: absolute;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-height: 150px;
  overflow-y: auto;
  z-index: 1000;
}

.mention-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  
  &:hover {
    background: #f5f7fa;
  }
  
  span {
    margin-left: 8px;
  }
}

.empty-comments {
  text-align: center;
  color: #909399;
  padding: 40px 20px;
  
  i {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
  }
}

.mention-notifications {
  position: fixed;
  top: 20px;
  right: 20px;
  width: 300px;
  z-index: 3000;
}

.mention-notification {
  margin-bottom: 8px;
}

:deep(.mention) {
  color: #409eff;
  font-weight: 500;
  background: #ecf5ff;
  padding: 2px 4px;
  border-radius: 3px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-panel {
    right: 10px;
    left: 10px;
    width: auto;
  }
  
  .mention-notifications {
    right: 10px;
    left: 10px;
    width: auto;
  }
}
</style>
