<template>
  <div class="dashboard-container" v-loading="loading">
    <!-- 原辅料统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon raw-material">
            <i class="el-icon-box"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ statsData.rawMaterialCount }}</div>
            <div class="stats-label">原料总数</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon auxiliary-material">
            <i class="el-icon-goods"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ statsData.auxiliaryMaterialCount }}</div>
            <div class="stats-label">辅料总数</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon recent-raw">
            <i class="el-icon-circle-plus-outline"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ statsData.recentRawCount }}</div>
            <div class="stats-label">本月新增原料</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon recent-auxiliary">
            <i class="el-icon-plus"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ statsData.recentAuxiliaryCount }}</div>
            <div class="stats-label">本月新增辅料</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 文献统计卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon literature-total">
            <i class="el-icon-document"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ literatureStatsData.totalCount }}</div>
            <div class="stats-label">文献总数</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon literature-recent">
            <i class="el-icon-document-add"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ literatureStatsData.recentCount }}</div>
            <div class="stats-label">本月新增文献</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon literature-pharmacopoeia">
            <i class="el-icon-notebook-1"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ literatureStatsData.pharmacopoeiaCount }}</div>
            <div class="stats-label">药典数量</div>
          </div>
        </div>
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <div class="stats-card">
          <div class="stats-icon literature-standard">
            <i class="el-icon-files"></i>
          </div>
          <div class="stats-content">
            <div class="stats-number">{{ literatureStatsData.standardCount }}</div>
            <div class="stats-label">标准数量</div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 原辅料图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 原辅料分布饼图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-pie-chart"></i> 原辅料分布</span>
          </div>
          <div ref="pieChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 原辅料每日新增趋势 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="16" :xl="16">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-data-line"></i> 原辅料每日新增趋势</span>
          </div>
          <div ref="lineChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 文献图表区域 -->
    <el-row :gutter="20" class="charts-row">
      <!-- 文献类型分布饼图 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-pie-chart"></i> 文献类型分布</span>
          </div>
          <div ref="literaturePieChart" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 文献新增趋势 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="16" :xl="16">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-data-line"></i> 文献新增趋势</span>
          </div>
          <div ref="literatureLineChart" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 大类分布图 -->
    <el-row :gutter="20">
      <el-col :span="24">
        <el-card class="chart-card">
          <div slot="header" class="card-header">
            <span><i class="el-icon-data-board"></i> 大类分布统计</span>
            <div class="header-controls">
              <el-radio-group v-model="categoryType" size="small" @change="updateCategoryChart">
                <el-radio-button label="raw">原料</el-radio-button>
                <el-radio-button label="auxiliary">辅料</el-radio-button>
              </el-radio-group>
            </div>
          </div>
          <div ref="categoryChart" class="chart-container-large"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getLiteratureStats, getLiteratureTypeStats, getLiteratureTrendStats } from '@/api/knowledge/literature'

export default {
  name: 'Index',
  data () {
    return {
      loading: false,
      categoryType: 'raw',
      statsData: {
        rawMaterialCount: 0,
        auxiliaryMaterialCount: 0,
        recentRawCount: 0,
        recentAuxiliaryCount: 0
      },
      literatureStatsData: {
        totalCount: 0,
        recentCount: 0,
        pharmacopoeiaCount: 0,
        standardCount: 0
      },
      pieChart: null,
      lineChart: null,
      categoryChart: null,
      literaturePieChart: null,
      literatureLineChart: null,
      chartData: {
        pieData: [],
        lineData: {
          dates: [],
          rawData: [],
          auxiliaryData: []
        },
        categoryData: {
          rawCategories: [],
          auxiliaryCategories: []
        }
      },
      literatureChartData: {
        typeData: [],
        trendData: {
          dates: [],
          counts: []
        }
      }
    }
  },
  mounted () {
    this.initCharts()
    this.loadData()
    this.loadLiteratureData()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.handleResize)
    if (this.pieChart) this.pieChart.dispose()
    if (this.lineChart) this.lineChart.dispose()
    if (this.categoryChart) this.categoryChart.dispose()
    if (this.literaturePieChart) this.literaturePieChart.dispose()
    if (this.literatureLineChart) this.literatureLineChart.dispose()
  },
  methods: {
    // 初始化图表
    initCharts () {
      this.initPieChart()
      this.initLineChart()
      this.initCategoryChart()
      this.initLiteraturePieChart()
      this.initLiteratureLineChart()
    },

    // 饼图
    initPieChart () {
      this.pieChart = echarts.init(this.$refs.pieChart)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return `<div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${params.seriesName}</div>
              <div style="display: flex; align-items: center;">
                <span style="display: inline-block; width: 10px; height: 10px; background: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
                <span>${params.name}: <strong>${params.value}</strong> (${params.percent}%)</span>
              </div>
            </div>`
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e4e7ed',
          borderWidth: 1,
          textStyle: {
            color: '#303133',
            fontSize: 12
          }
        },
        legend: {
          bottom: '8%',
          left: 'center',
          itemWidth: 14,
          itemHeight: 14,
          textStyle: {
            fontSize: 13,
            color: '#606266',
            padding: [0, 0, 0, 8]
          }
        },
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '总计\n0',
              textAlign: 'center',
              fill: '#303133',
              fontSize: 16,
              fontWeight: 'bold',
              lineHeight: 20
            }
          }
        ],
        series: [
          {
            name: '原辅料分布',
            type: 'pie',
            radius: ['50%', '75%'],
            center: ['50%', '42%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 8,
              borderColor: '#fff',
              borderWidth: 3,
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.1)'
            },
            label: {
              show: true,
              position: 'outside',
              formatter: function (params) {
                return `{name|${params.name}}\n{value|${params.value}} {percent|${params.percent}%}`
              },
              rich: {
                name: {
                  fontSize: 12,
                  fontWeight: 'bold',
                  color: '#303133',
                  lineHeight: 16
                },
                value: {
                  fontSize: 14,
                  fontWeight: 'bold',
                  color: '#1890ff',
                  lineHeight: 18
                },
                percent: {
                  fontSize: 11,
                  color: '#909399',
                  lineHeight: 18
                }
              },
              lineHeight: 16
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 8,
              smooth: true,
              lineStyle: {
                color: '#dcdfe6',
                width: 2
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 20,
                shadowColor: 'rgba(0, 0, 0, 0.2)'
              },
              label: {
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            data: [
              {
                value: 0,
                name: '原料',
                itemStyle: {
                  color: '#06B6D4'
                }
              },
              {
                value: 0,
                name: '辅料',
                itemStyle: {
                  color: '#10B981'
                }
              }
            ],
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function (idx) {
              return Math.random() * 200
            }
          }
        ]
      }
      this.pieChart.setOption(option)
    },

    // 折线图
    initLineChart () {
      this.lineChart = echarts.init(this.$refs.lineChart)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e4e7ed',
          borderWidth: 1,
          textStyle: {
            color: '#303133'
          }
        },
        legend: {
          data: ['原料', '辅料'],
          top: '5%',
          textStyle: {
            color: '#606266'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [],
          axisLine: {
            lineStyle: {
              color: '#E2E8F0'
            }
          },
          axisLabel: {
            color: '#64748B'
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#E2E8F0'
            }
          },
          axisLabel: {
            color: '#64748B'
          },
          splitLine: {
            lineStyle: {
              color: '#F1F5F9'
            }
          }
        },
        series: [
          {
            name: '原料',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 3,
              color: '#06B6D4'
            },
            areaStyle: {
              color: 'rgba(6, 182, 212, 0.15)'
            },
            data: []
          },
          {
            name: '辅料',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 3,
              color: '#10B981'
            },
            areaStyle: {
              color: 'rgba(16, 185, 129, 0.15)'
            },
            data: []
          }
        ]
      }
      this.lineChart.setOption(option)
    },

    // 大类分布图
    initCategoryChart () {
      this.categoryChart = echarts.init(this.$refs.categoryChart)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return `<div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${params.seriesName}</div>
              <div style="display: flex; align-items: center;">
                <span style="display: inline-block; width: 10px; height: 10px; background: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
                <span>${params.name}: <strong>${params.value}</strong> (${params.percent}%)</span>
              </div>
            </div>`
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e4e7ed',
          borderWidth: 1,
          textStyle: {
            color: '#303133'
          }
        },
        legend: {
          type: 'scroll',
          bottom: '5%',
          left: 'center',
          textStyle: {
            color: '#606266'
          }
        },
        series: [
          {
            name: '原料大类分布',
            type: 'pie',
            radius: ['25%', '65%'],
            center: ['50%', '45%'],
            roseType: 'area',
            itemStyle: {
              borderRadius: 6,
              borderColor: '#fff',
              borderWidth: 2,
              shadowBlur: 8,
              shadowColor: 'rgba(0, 0, 0, 0.1)'
            },
            label: {
              show: true,
              formatter: '{b}\n{d}%',
              fontSize: 12,
              color: '#606266',
              fontWeight: 'bold'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              },
              itemStyle: {
                shadowBlur: 15,
                shadowColor: 'rgba(0, 0, 0, 0.2)'
              }
            },
            data: [],
            animationType: 'scale',
            animationEasing: 'elasticOut'
          }
        ]
      }
      this.categoryChart.setOption(option)
    },

    // 更新大类分布图颜色 - 修复数据渲染问题
    updateCategoryChart () {
      const isRaw = this.categoryType === 'raw'
      const data = isRaw ? this.chartData.categoryData.rawCategories : this.chartData.categoryData.auxiliaryCategories
      const colors = isRaw
        ? ['#06B6D4', '#0EA5E9', '#3B82F6', '#6366F1', '#8B5CF6', '#A855F7']
        : ['#F59E0B', '#F97316', '#EF4444', '#EC4899', '#10B981', '#00D2A0']

      // 确保数据存在
      if (!data || data.length === 0) {
        console.warn('大类分布数据为空')
        return
      }

      this.categoryChart.setOption({
        series: [{
          name: isRaw ? '原料大类分布' : '辅料大类分布',
          type: 'pie',
          radius: ['25%', '65%'],
          center: ['50%', '45%'],
          roseType: 'area',
          data: data.map((item, index) => ({
            value: item.value,
            name: item.name,
            itemStyle: {
              color: colors[index % colors.length],
              borderRadius: 6,
              borderColor: '#fff',
              borderWidth: 2,
              shadowBlur: 8,
              shadowColor: 'rgba(0, 0, 0, 0.1)'
            }
          }))
        }]
      })
    },

    // 文献类型分布饼图
    initLiteraturePieChart () {
      this.literaturePieChart = echarts.init(this.$refs.literaturePieChart)
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: function (params) {
            return `<div style="padding: 8px;">
              <div style="font-weight: bold; margin-bottom: 4px;">${params.seriesName}</div>
              <div style="display: flex; align-items: center;">
                <span style="display: inline-block; width: 10px; height: 10px; background: ${params.color}; border-radius: 50%; margin-right: 8px;"></span>
                <span>${params.name}: <strong>${params.value}</strong> (${params.percent}%)</span>
              </div>
            </div>`
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e4e7ed',
          borderWidth: 1,
          textStyle: {
            color: '#303133',
            fontSize: 12
          }
        },
        legend: {
          bottom: '8%',
          left: 'center',
          itemWidth: 14,
          itemHeight: 14,
          textStyle: {
            fontSize: 13,
            color: '#606266',
            padding: [0, 0, 0, 8]
          }
        },
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: '总计\n0',
              textAlign: 'center',
              fill: '#303133',
              fontSize: 16,
              fontWeight: 'bold',
              lineHeight: 20
            }
          }
        ],
        series: [
          {
            name: '文献类型分布',
            type: 'pie',
            radius: ['50%', '75%'],
            center: ['50%', '42%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 8,
              borderColor: '#fff',
              borderWidth: 3,
              shadowBlur: 10,
              shadowColor: 'rgba(0, 0, 0, 0.1)'
            },
            label: {
              show: true,
              position: 'outside',
              formatter: function (params) {
                return `{name|${params.name}}\n{value|${params.value}} {percent|${params.percent}%}`
              },
              rich: {
                name: {
                  fontSize: 12,
                  fontWeight: 'bold',
                  color: '#303133',
                  lineHeight: 16
                },
                value: {
                  fontSize: 14,
                  fontWeight: 'bold',
                  color: '#8B5CF6',
                  lineHeight: 18
                },
                percent: {
                  fontSize: 11,
                  color: '#909399',
                  lineHeight: 18
                }
              },
              lineHeight: 16
            },
            labelLine: {
              show: true,
              length: 15,
              length2: 8,
              smooth: true,
              lineStyle: {
                color: '#dcdfe6',
                width: 2
              }
            },
            emphasis: {
              itemStyle: {
                shadowBlur: 20,
                shadowColor: 'rgba(0, 0, 0, 0.2)'
              },
              label: {
                fontSize: 14,
                fontWeight: 'bold'
              }
            },
            data: [],
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: function () {
              return Math.random() * 200
            }
          }
        ]
      }
      this.literaturePieChart.setOption(option)
    },

    // 文献新增趋势折线图
    initLiteratureLineChart () {
      this.literatureLineChart = echarts.init(this.$refs.literatureLineChart)
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          },
          backgroundColor: 'rgba(255, 255, 255, 0.95)',
          borderColor: '#e4e7ed',
          borderWidth: 1,
          textStyle: {
            color: '#303133'
          }
        },
        legend: {
          data: ['文献新增'],
          top: '5%',
          textStyle: {
            color: '#606266'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [],
          axisLine: {
            lineStyle: {
              color: '#E2E8F0'
            }
          },
          axisLabel: {
            color: '#64748B'
          }
        },
        yAxis: {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: '#E2E8F0'
            }
          },
          axisLabel: {
            color: '#64748B'
          },
          splitLine: {
            lineStyle: {
              color: '#F1F5F9'
            }
          }
        },
        series: [
          {
            name: '文献新增',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            lineStyle: {
              width: 3,
              color: '#8B5CF6'
            },
            areaStyle: {
              color: 'rgba(139, 92, 246, 0.15)'
            },
            data: []
          }
        ]
      }
      this.literatureLineChart.setOption(option)
    },

    // 加载数据
    async loadData () {
      this.loading = true
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        const mockData = {
          stats: {
            rawMaterialCount: 156,
            auxiliaryMaterialCount: 89,
            recentRawCount: 12,
            recentAuxiliaryCount: 8
          },
          pieData: [
            { value: 156, name: '原料' },
            { value: 89, name: '辅料' }
          ],
          lineData: {
            dates: ['12-01', '12-02', '12-03', '12-04', '12-05', '12-06', '12-07'],
            rawData: [2, 3, 1, 4, 2, 3, 2],
            auxiliaryData: [1, 2, 2, 1, 3, 1, 2]
          },
          categoryData: {
            rawCategories: [
              { value: 45, name: '有机试剂' },
              { value: 38, name: '无机试剂' },
              { value: 32, name: '生物试剂' },
              { value: 25, name: '标准品' },
              { value: 16, name: '其他' }
            ],
            auxiliaryCategories: [
              { value: 28, name: '玻璃器皿' },
              { value: 22, name: '塑料器皿' },
              { value: 18, name: '实验工具' },
              { value: 12, name: '安全用品' },
              { value: 9, name: '其他' }
            ]
          }
        }

        this.statsData = mockData.stats
        this.chartData = mockData
        this.updateCharts(mockData)
      } catch (error) {
        this.$message.error('数据加载失败')
      } finally {
        this.loading = false
      }
    },

    // 加载文献数据
    async loadLiteratureData () {
      try {
        // 模拟API调用 - 实际项目中替换为真实API
        await new Promise(resolve => setTimeout(resolve, 800))

        const mockLiteratureData = {
          stats: {
            totalCount: 45,
            recentCount: 6,
            pharmacopoeiaCount: 18,
            standardCount: 15
          },
          typeData: [
            { value: 18, name: '药典', itemStyle: { color: '#8B5CF6' } },
            { value: 15, name: '标准', itemStyle: { color: '#06B6D4' } },
            { value: 8, name: '手册', itemStyle: { color: '#10B981' } },
            { value: 4, name: '其他', itemStyle: { color: '#F59E0B' } }
          ],
          trendData: {
            dates: ['12-01', '12-02', '12-03', '12-04', '12-05', '12-06', '12-07'],
            counts: [1, 2, 0, 1, 1, 1, 0]
          }
        }

        this.literatureStatsData = mockLiteratureData.stats
        this.literatureChartData = mockLiteratureData
        this.updateLiteratureCharts(mockLiteratureData)
      } catch (error) {
        this.$message.error('文献数据加载失败')
      }
    },

    // 更新文献图表数据
    updateLiteratureCharts (data) {
      // 更新文献类型分布饼图
      const total = data.typeData.reduce((sum, item) => sum + item.value, 0)
      this.literaturePieChart.setOption({
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: `总计\n${total}`,
              textAlign: 'center',
              fill: '#303133',
              fontSize: 16,
              fontWeight: 'bold',
              lineHeight: 20
            }
          }
        ],
        series: [{
          data: data.typeData
        }]
      })

      // 更新文献新增趋势折线图
      this.literatureLineChart.setOption({
        xAxis: {
          data: data.trendData.dates
        },
        series: [
          { data: data.trendData.counts }
        ]
      })
    },

    // 更新图表数据
    updateCharts (data) {
      // 更新饼图
      const total = data.pieData.reduce((sum, item) => sum + item.value, 0)
      this.pieChart.setOption({
        graphic: [
          {
            type: 'text',
            left: 'center',
            top: 'center',
            style: {
              text: `总计\n${total}`,
              textAlign: 'center',
              fill: '#303133',
              fontSize: 16,
              fontWeight: 'bold',
              lineHeight: 20
            }
          }
        ],
        series: [{
          data: data.pieData
        }]
      })

      // 更新折线图
      this.lineChart.setOption({
        xAxis: {
          data: data.lineData.dates
        },
        series: [
          { data: data.lineData.rawData },
          { data: data.lineData.auxiliaryData }
        ]
      })

      // 更新大类分布图数据
      this.chartData = data
      this.updateCategoryChart()
    },

    // 响应式处理
    handleResize () {
      this.$nextTick(() => {
        if (this.pieChart) this.pieChart.resize()
        if (this.lineChart) this.lineChart.resize()
        if (this.categoryChart) this.categoryChart.resize()
        if (this.literaturePieChart) this.literaturePieChart.resize()
        if (this.literatureLineChart) this.literatureLineChart.resize()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import '@/assets/styles/variables.scss';

.dashboard-container {
  padding: 20px;
  background: linear-gradient(135deg, #FEFEFE 0%, #F8FAFC 100%);
  min-height: calc(100vh - 84px);

  .stats-cards {
    margin-bottom: 20px;

    .stats-card {
      background: linear-gradient(135deg, #F8FAFC 0%, #F0F4F8 100%);
      border-radius: 12px;
      padding: 20px;
      display: flex;
      align-items: center;
      box-shadow: 0 2px 8px rgba(6, 182, 212, 0.08);
      border: 1px solid rgba(6, 182, 212, 0.1);
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover {
        box-shadow: 0 4px 16px rgba(6, 182, 212, 0.12);
        transform: translateY(-2px);
        border-color: rgba(6, 182, 212, 0.2);
        background: linear-gradient(135deg, #FEFEFE 0%, #F8FAFC 100%);
      }

      .stats-icon {
        width: 56px;
        height: 56px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        i {
          font-size: 24px;
          color: white;
          font-weight: 500;
        }

        &.raw-material {
          background: #06B6D4;
        }

        &.auxiliary-material {
          background: #10B981;
        }

        &.recent-raw {
          background: #F59E0B;
        }

        &.recent-auxiliary {
          background: #EF4444;
        }

        &.literature-total {
          background: #8B5CF6;
        }

        &.literature-recent {
          background: #A855F7;
        }

        &.literature-pharmacopoeia {
          background: #06B6D4;
        }

        &.literature-standard {
          background: #10B981;
        }
      }

      .stats-content {
        flex: 1;

        .stats-number {
          font-size: 26px;
          font-weight: 600;
          color: #334155;
          line-height: 1;
          margin-bottom: 4px;
        }

        .stats-label {
          font-size: 14px;
          color: #64748B;
        }
      }
    }
  }

  .charts-row {
    margin-bottom: 20px;
  }

  .chart-card {
    background: #F8FAFC;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(6, 182, 212, 0.08);
    overflow: hidden;

    .card-header {
      padding: 16px 20px;
      background: linear-gradient(135deg, #F0F4F8 0%, #F8FAFC 100%);
      border-bottom: 1px solid rgba(6, 182, 212, 0.08);
      font-size: 15px;
      font-weight: 600;
      color: #334155;
      display: flex;
      justify-content: space-between;
      align-items: center;

      i {
        margin-right: 8px;
        color: #06B6D4;
      }

      .header-controls {
        ::v-deep .el-radio-group {
          .el-radio-button__inner {
            padding: 8px 15px;
            font-size: 12px;
            border-color: rgba(6, 182, 212, 0.15);
            color: #64748B;
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(10px);
          }

          .el-radio-button__orig-radio:checked+.el-radio-button__inner {
            background-color: #06B6D4;
            border-color: #06B6D4;
            color: #FEFEFE;
            box-shadow: 0 2px 4px rgba(6, 182, 212, 0.2);
          }
        }
      }
    }

    .card-body {
      padding: 20px;
      background: #F8FAFC;
    }

    .chart-container {
      height: 300px;
      width: 100%;
    }

    .chart-container-large {
      height: 380px;
      width: 100%;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;

    .stats-cards {
      .stats-card {
        padding: 16px;
        margin-bottom: 12px;

        .stats-icon {
          width: 48px;
          height: 48px;
          margin-right: 12px;

          i {
            font-size: 18px;
          }
        }

        .stats-content {
          .stats-number {
            font-size: 22px;
          }

          .stats-label {
            font-size: 13px;
          }
        }
      }
    }

    .chart-card {
      .chart-container {
        height: 250px;
      }

      .chart-container-large {
        height: 300px;
      }
    }
  }
}
</style>
