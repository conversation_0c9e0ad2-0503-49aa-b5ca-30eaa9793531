<template>
  <div class="collaborative-editor">
    <!-- 编辑器工具栏 -->
    <div class="editor-toolbar">
      <div class="toolbar-left">
        <!-- 编辑模式切换 -->
        <el-radio-group v-model="editorMode" size="small">
          <el-radio-button label="rich">富文本</el-radio-button>
          <el-radio-button label="markdown">Markdown</el-radio-button>
          <el-radio-button label="block">块编辑器</el-radio-button>
        </el-radio-group>

        <!-- 模板选择 -->
        <el-dropdown @command="insertTemplate" style="margin-left: 12px">
          <el-button size="small">
            模板库<i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="meeting">会议纪要</el-dropdown-item>
            <el-dropdown-item command="project">项目计划</el-dropdown-item>
            <el-dropdown-item command="report">工作报告</el-dropdown-item>
            <el-dropdown-item command="requirement">需求文档</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>

      <div class="toolbar-center">
        <!-- 协作用户 -->
        <div class="collaborators">
          <div v-for="user in activeCollaborators" :key="user.id" class="collaborator-avatar" :title="user.name">
            <el-avatar :size="24" :src="user.avatar" icon="el-icon-user" />
          </div>
        </div>
      </div>

      <div class="toolbar-right">
        <!-- 版本控制 -->
        <el-button-group size="small">
          <el-button icon="el-icon-time" @click="showVersionHistory = true">历史版本</el-button>
          <el-button icon="el-icon-chat-dot-round" @click="showComments = !showComments">评论</el-button>
          <el-button icon="el-icon-share" @click="shareDocument">分享</el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 编辑器主体 -->
    <div class="editor-container">
      <!-- 富文本编辑器 -->
      <div v-if="editorMode === 'rich'" class="rich-editor">
        <el-input v-model="content" type="textarea" :rows="20" placeholder="请输入内容..." @input="handleContentChange" />
      </div>

      <!-- Markdown编辑器 -->
      <div v-else-if="editorMode === 'markdown'" class="markdown-editor">
        <div class="markdown-container">
          <div class="markdown-input">
            <el-input v-model="content" type="textarea" :rows="20" placeholder="请输入Markdown内容..."
              @input="handleContentChange" />
          </div>
          <div class="markdown-preview">
            <div class="preview-content" v-html="markdownPreview"></div>
          </div>
        </div>
      </div>

      <!-- 块编辑器 -->
      <div v-else-if="editorMode === 'block'" class="block-editor">
        <div class="block-container">
          <div v-for="(block, index) in blocks" :key="block.id" class="block-item">
            <el-input v-model="block.content" type="textarea" :rows="3" :placeholder="getBlockPlaceholder(block.type)"
              @input="updateBlock(index)" />
            <div class="block-actions">
              <el-button type="text" size="mini" icon="el-icon-plus" @click="addBlock(index)"></el-button>
              <el-button type="text" size="mini" icon="el-icon-delete" @click="deleteBlock(index)"
                v-if="blocks.length > 1"></el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 评论侧边栏 -->
    <div v-if="showComments" class="comments-sidebar">
      <div class="comments-header">
        <h4>评论</h4>
        <el-button type="text" icon="el-icon-close" @click="showComments = false"></el-button>
      </div>
      <div class="comments-list">
        <div v-for="comment in comments" :key="comment.id" class="comment-item">
          <div class="comment-header">
            <el-avatar :size="24" :src="comment.user.avatar" icon="el-icon-user" />
            <div class="comment-info">
              <span class="comment-author">{{ comment.user.name }}</span>
              <span class="comment-time">{{ formatTime(comment.createdAt) }}</span>
            </div>
          </div>
          <div class="comment-content">{{ comment.content }}</div>
        </div>
      </div>
      <div class="add-comment">
        <el-input v-model="newComment" type="textarea" :rows="3" placeholder="添加评论..." />
        <el-button type="primary" size="small" @click="addComment" style="margin-top: 8px">
          发表评论
        </el-button>
      </div>
    </div>

    <!-- 版本历史对话框 -->
    <el-dialog title="版本历史" :visible.sync="showVersionHistory" width="80%">
      <div class="version-history">
        <div class="version-list">
          <div v-for="version in versions" :key="version.id" class="version-item" @click="selectVersion(version)">
            <div class="version-info">
              <div class="version-title">{{ version.title }}</div>
              <div class="version-meta">
                <span>{{ version.author }}</span>
                <span>{{ formatTime(version.createdAt) }}</span>
              </div>
            </div>
            <div class="version-actions">
              <el-button type="text" size="mini" @click.stop="restoreVersion(version)">恢复</el-button>
            </div>
          </div>
        </div>
        <div class="version-preview">
          <div v-if="selectedVersion" class="version-content">
            <h4>{{ selectedVersion.title }}</h4>
            <div class="version-full-content">{{ selectedVersion.content }}</div>
          </div>
          <div v-else class="no-version-selected">
            <p>请选择一个版本查看内容</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'CollaborativeEditor',
  props: {
    value: {
      type: String,
      default: ''
    },
    documentId: {
      type: String,
      required: true
    }
  },
  data () {
    return {
      content: this.value,
      editorMode: 'rich',

      // 块编辑器
      blocks: [
        { id: 1, type: 'paragraph', content: '' }
      ],

      // 协作相关
      activeCollaborators: [
        { id: 1, name: '张三', avatar: '' },
        { id: 2, name: '李四', avatar: '' }
      ],

      // 评论相关
      showComments: false,
      comments: [
        {
          id: 1,
          user: { name: '王五', avatar: '' },
          content: '这个部分需要补充更多细节',
          createdAt: new Date().toISOString()
        }
      ],
      newComment: '',

      // 版本控制
      showVersionHistory: false,
      versions: [
        {
          id: 1,
          title: '初始版本',
          author: '张三',
          createdAt: new Date().toISOString(),
          content: '这是初始版本的内容'
        }
      ],
      selectedVersion: null
    }
  },
  computed: {
    markdownPreview () {
      // 简单的Markdown预览，实际项目中可以使用marked库
      return this.content.replace(/\n/g, '<br>')
    }
  },
  watch: {
    value (newVal) {
      this.content = newVal
    },
    content (newVal) {
      this.$emit('input', newVal)
      this.$emit('content-change', newVal)
    }
  },
  methods: {
    // 处理内容变化
    handleContentChange () {
      console.log('内容变化:', this.content)
    },

    // 插入模板
    insertTemplate (templateType) {
      const templates = {
        meeting: '# 会议纪要\n\n**会议时间：**\n**参会人员：**\n**会议主题：**\n\n## 讨论内容\n\n## 决议事项',
        project: '# 项目计划\n\n## 项目概述\n\n## 项目目标\n\n## 时间安排',
        report: '# 工作报告\n\n## 本周工作总结\n\n## 下周工作计划',
        requirement: '# 需求文档\n\n## 背景\n\n## 功能需求\n\n## 验收标准'
      }

      this.content = templates[templateType] || ''
    },

    // 块编辑器相关方法
    addBlock (index) {
      const newBlock = {
        id: Date.now(),
        type: 'paragraph',
        content: ''
      }
      this.blocks.splice(index + 1, 0, newBlock)
    },

    deleteBlock (index) {
      if (this.blocks.length > 1) {
        this.blocks.splice(index, 1)
      }
    },

    updateBlock (index) {
      console.log('更新块:', index)
    },

    getBlockPlaceholder (type) {
      const placeholders = {
        paragraph: '输入段落内容...',
        h1: '输入一级标题...',
        h2: '输入二级标题...',
        h3: '输入三级标题...'
      }
      return placeholders[type] || '输入内容...'
    },

    // 评论相关方法
    addComment () {
      if (this.newComment.trim()) {
        const comment = {
          id: Date.now(),
          user: { name: '当前用户', avatar: '' },
          content: this.newComment,
          createdAt: new Date().toISOString()
        }
        this.comments.push(comment)
        this.newComment = ''
      }
    },

    // 版本控制相关方法
    selectVersion (version) {
      this.selectedVersion = version
    },

    restoreVersion (version) {
      this.$confirm('确定要恢复到此版本吗？', '确认恢复', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.content = version.content
        this.$message.success('版本恢复成功')
        this.showVersionHistory = false
      })
    },

    // 分享文档
    shareDocument () {
      this.$message.success('分享链接已复制到剪贴板')
    },

    // 格式化时间
    formatTime (time) {
      return new Date(time).toLocaleString('zh-CN')
    }
  }
}
</script>

<style lang="scss" scoped>
.collaborative-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
}

.editor-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafbfc;

  .collaborators {
    display: flex;
    gap: 8px;
  }
}

.editor-container {
  flex: 1;
  display: flex;
  position: relative;
  overflow: hidden;
}

.rich-editor,
.markdown-editor,
.block-editor {
  flex: 1;
  padding: 16px;
}

.markdown-container {
  display: flex;
  height: 100%;

  .markdown-input,
  .markdown-preview {
    flex: 1;
    border-right: 1px solid #e4e7ed;

    &:last-child {
      border-right: none;
    }
  }

  .markdown-preview {
    padding: 16px;
    overflow-y: auto;
    background: #fafbfc;
  }
}

.block-container {
  .block-item {
    margin-bottom: 16px;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 6px;

    .block-actions {
      margin-top: 8px;
      display: flex;
      gap: 4px;
    }
  }
}

.comments-sidebar {
  width: 300px;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  background: #fafbfc;

  .comments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #e4e7ed;

    h4 {
      margin: 0;
    }
  }

  .comments-list {
    flex: 1;
    overflow-y: auto;
    padding: 16px;

    .comment-item {
      margin-bottom: 16px;
      padding: 12px;
      background: white;
      border-radius: 6px;

      .comment-header {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 8px;

        .comment-info {
          .comment-author {
            font-size: 13px;
            font-weight: 500;
          }

          .comment-time {
            font-size: 11px;
            color: #909399;
            margin-left: 8px;
          }
        }
      }

      .comment-content {
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }

  .add-comment {
    padding: 16px;
    border-top: 1px solid #e4e7ed;
  }
}

.version-history {
  display: flex;
  height: 600px;

  .version-list {
    width: 300px;
    border-right: 1px solid #e4e7ed;
    overflow-y: auto;

    .version-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;

      &:hover {
        background: #f5f7fa;
      }

      .version-info {
        flex: 1;

        .version-title {
          font-size: 14px;
          font-weight: 500;
          margin-bottom: 4px;
        }

        .version-meta {
          font-size: 12px;
          color: #909399;

          span {
            margin-right: 12px;
          }
        }
      }
    }
  }

  .version-preview {
    flex: 1;
    padding: 16px;
    overflow-y: auto;

    .version-content {
      h4 {
        margin: 0 0 16px 0;
      }
    }

    .no-version-selected {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100%;
      color: #909399;
    }
  }
}
</style>
