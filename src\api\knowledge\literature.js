import request from '@/utils/request'

// 查询文献列表
export function listLiterature (query) {
  return request({
    url: '/knowledge/literature/list',
    method: 'get',
    params: query
  })
}

// 查询文献详细
export function getLiterature (id) {
  return request({
    url: '/knowledge/literature/' + id,
    method: 'get'
  })
}

// 新增文献
export function addLiterature (data) {
  return request({
    url: '/knowledge/literature',
    method: 'post',
    data: data
  })
}

// 修改文献
export function updateLiterature (data) {
  return request({
    url: '/knowledge/literature',
    method: 'put',
    data: data
  })
}

// 删除文献
export function delLiterature (id) {
  return request({
    url: '/knowledge/literature/' + id,
    method: 'delete'
  })
}

// 获取文献统计数据
export function getLiteratureStats () {
  return request({
    url: '/knowledge/literature/stats',
    method: 'get'
  })
}

// 获取文献类型分布统计
export function getLiteratureTypeStats () {
  return request({
    url: '/knowledge/literature/stats/type',
    method: 'get'
  })
}

// 获取文献新增趋势统计
export function getLiteratureTrendStats (days = 7) {
  return request({
    url: '/knowledge/literature/stats/trend',
    method: 'get',
    params: { days }
  })
}
